using UnityEngine;
using UnityEditor;
using TMPro;
using System.IO;

public class ApplyTMPFontToPrefabs : EditorWindow
{
    private TMP_FontAsset defaultFont;

    [MenuItem("Tools/Apply Default TMP Font to Prefabs")]
    public static void ShowWindow()
    {
        GetWindow<ApplyTMPFontToPrefabs>("Apply TMP Font");
    }

    private void OnGUI()
    {
        GUILayout.Label("Apply Default TMP Font to All Prefabs", EditorStyles.boldLabel);
        defaultFont = (TMP_FontAsset)EditorGUILayout.ObjectField("Default TMP Font", defaultFont, typeof(TMP_FontAsset), false);

        if (GUILayout.Button("Apply Font to Prefabs"))
        {
            ApplyFontToAllPrefabs();
        }
    }

    private void ApplyFontToAllPrefabs()
    {
        if (defaultFont == null)
        {
            Debug.LogWarning("No default TMP font selected! Please assign a font.");
            return;
        }

        string[] prefabGUIDs = AssetDatabase.FindAssets("t:Prefab");

        int modifiedCount = 0;
        foreach (string guid in prefabGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);

            if (prefab == null) continue;

            TMP_Text[] tmpTexts = prefab.GetComponentsInChildren<TMP_Text>(true);
            bool modified = false;

            foreach (TMP_Text text in tmpTexts)
            {
                if (text.font != defaultFont)
                {
                    text.font = defaultFont;
                    modified = true;
                }
            }

            if (modified)
            {
                EditorUtility.SetDirty(prefab);
                modifiedCount++;
            }
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log($"Updated {modifiedCount} prefabs with the default TMP font.");
    }
}
