using UnityEngine;

public class FPS_Shooting : MonoBehaviour
{
    [Header("Weapon Settings")]
    public Animator animator;  
    public int ammoPerMag = 10;  
    public float reloadTime = 2.0f;
    public int damage = 25;
    public float range = 100f;
    public float fireRate = 0.15f; // Add this: minimum time between shots
    public LayerMask zombieLayer;
    public bool isDualWielding = false;

    [Header("Effects")]
    public GameObject leftMuzzleFlash;  // Left gun muzzle flash
    public GameObject rightMuzzleFlash; // Right gun muzzle flash
    public Light leftMuzzleLight;       // Left gun light
    public Light rightMuzzleLight;      // Right gun light
    public AudioSource shootSound;
    public AudioSource reloadSound;     // Add this for reload sound
    public float reloadSoundDelay = 0f; // Optional delay for sync with animation
    public GameObject bloodEffect;


    [Header("References")]
    public Camera playerCamera;

    private int currentAmmo;
    private bool isReloading = false;
    private float shootDuration;
    private float nextFireTime;
    private bool useLeftGun = true; // Alternates between left and right when dual wielding
    private bool canShoot = true; // Add this field

    void Start()
    {
        currentAmmo = ammoPerMag;
        
        if (animator != null)
        {
            shootDuration = animator.GetCurrentAnimatorStateInfo(0).length;
        }
        else
        {
            shootDuration = fireRate;
        }
        
        if (playerCamera == null)
        {
            playerCamera = Camera.main;
        }

        // Ensure all effects are disabled at start
        DisableAllMuzzleEffects();

        nextFireTime = Time.time + shootDuration;
    }

    public void SetCanShoot(bool enabled)
    {
        canShoot = enabled;
    }

    void Update()
    {
        if (!canShoot || isReloading) return; // Modified condition

        if (Input.GetMouseButton(0) && Time.time >= nextFireTime)
        {
            if (currentAmmo > 0)
            {
                Shoot();
            }
            else
            {
                StartReload();
            }
        }

        if (Input.GetKeyDown(KeyCode.R) && currentAmmo < ammoPerMag && !isReloading)
        {
            StartReload();
        }
    }

    void Shoot()
    {
        nextFireTime = Time.time + Mathf.Max(shootDuration, fireRate);
        currentAmmo--;

        if (animator != null)
        {
            animator.SetTrigger("Shoot");
        }

        // Handle muzzle effects based on dual wielding state
        if (isDualWielding)
        {
            // Alternate between left and right muzzle flashes
            if (useLeftGun)
            {
                ShowMuzzleEffects(leftMuzzleFlash, leftMuzzleLight);
            }
            else
            {
                ShowMuzzleEffects(rightMuzzleFlash, rightMuzzleLight);
            }
            useLeftGun = !useLeftGun; // Switch for next shot
        }
        else
        {
            // Single gun - use right muzzle flash by default
            ShowMuzzleEffects(rightMuzzleFlash, rightMuzzleLight);
        }


        if (shootSound != null)
            shootSound.Play();

        Ray ray = playerCamera.ViewportPointToRay(new Vector3(0.5f, 0.5f, 0));
        RaycastHit hit;

        if (Physics.Raycast(ray, out hit, range, zombieLayer))
        {
            ZombieAI zombie = hit.collider.GetComponent<ZombieAI>();
            if (zombie != null)
            {
                zombie.TakeDamage(damage);

                Rigidbody zombieRb = hit.collider.GetComponent<Rigidbody>();
                if (zombieRb != null)
                {
                    zombieRb.AddForceAtPosition(ray.direction * 500f, hit.point, ForceMode.Impulse);
                }

                if (bloodEffect != null)
                {
                    GameObject blood = Instantiate(bloodEffect, hit.point, 
                        Quaternion.LookRotation(hit.normal));
                    Destroy(blood, 2f);
                }
            }
        }

        if (currentAmmo <= 0)
        {
            StartReload(); // Immediately start reloading when ammo is depleted
        }
    }

    private void ShowMuzzleEffects(GameObject muzzleFlash, Light muzzleLight)
    {
        if (muzzleFlash != null)
        {
            muzzleFlash.SetActive(true);
        }
        if (muzzleLight != null)
        {
            muzzleLight.enabled = true;
        }
        Invoke(nameof(HideMuzzleEffects), 0.15f);
    }

    private void HideMuzzleEffects()
    {
        if (leftMuzzleFlash != null) leftMuzzleFlash.SetActive(false);
        if (rightMuzzleFlash != null) rightMuzzleFlash.SetActive(false);
        if (leftMuzzleLight != null) leftMuzzleLight.enabled = false;
        if (rightMuzzleLight != null) rightMuzzleLight.enabled = false;
    }

    private void DisableAllMuzzleEffects()
    {
        if (leftMuzzleFlash != null) leftMuzzleFlash.SetActive(false);
        if (rightMuzzleFlash != null) rightMuzzleFlash.SetActive(false);
        if (leftMuzzleLight != null) leftMuzzleLight.enabled = false;
        if (rightMuzzleLight != null) rightMuzzleLight.enabled = false;
    }

    void StartReload()
    {
        if (isReloading) return;
        
        isReloading = true;
        if (animator != null)
        {
            animator.SetTrigger("ReloadTrigger");
        }

        // Play reload sound with optional delay
        if (reloadSound != null)
        {
            if (reloadSoundDelay > 0)
            {
                Invoke(nameof(PlayReloadSound), reloadSoundDelay);
            }
            else
            {
                PlayReloadSound();
            }
        }

        Invoke(nameof(FinishReload), reloadTime);
    }

    private void PlayReloadSound()
    {
        if (reloadSound != null && !reloadSound.isPlaying)
        {
            reloadSound.Play();
        }
    }

    void FinishReload()
    {
        currentAmmo = ammoPerMag;
        isReloading = false;
    }
}
