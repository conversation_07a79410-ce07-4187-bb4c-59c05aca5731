using UnityEngine;
using UnityEngine.UI;

public class UIFloatingParticle : MonoBehaviour
{
    public RectTransform particlePrefab;
    public Transform parentPanel;
    public float lifeTime = 2f;
    public Vector2 speedRange = new Vector2(20f, 60f);
    public int spawnRate = 10;

    [Header("Particle Size Settings")]
    public bool useRandomSize = false;
    [Tooltip("Fixed size of particles when useRandomSize is false")]
    public float particleSize = 1f;
    [Tooltip("Random size range when useRandomSize is true")]
    public Vector2 sizeRange = new Vector2(0.5f, 1.5f);

    public enum SizeChangeMode { None, Grow, Shrink }

    [Header("Size Animation")]
    [Tooltip("How particle size changes over lifetime")]
    public SizeChangeMode sizeChangeMode = SizeChangeMode.None;
    [Tooltip("How much to grow/shrink (multiplier of original size)")]
    [Range(0.1f, 3f)]
    public float sizeChangeFactor = 1.5f;

    [Header("Glow Settings")]
    public Color glowColor = Color.white;
    public float glowRadius = 1f;
    [Range(0.1f, 3f)]
    public float glowIntensity = 1f;
    public bool useGlowEffect = true;
    public Shader glowShader; // Assign CircleGlow shader in the inspector

    private void Awake()
    {
        // If no shader is assigned, try to find it
        if (glowShader == null)
        {
            glowShader = Shader.Find("Custom/CircleGlow");
        }
    }

    void Start()
    {
        InvokeRepeating(nameof(SpawnParticle), 0f, 1f / spawnRate);
    }

    void SpawnParticle()
    {
        // Check if prefab and parent panel are valid
        if (particlePrefab == null)
        {
            Debug.LogError("UIFloatingParticle: particlePrefab is null!");
            return;
        }

        if (parentPanel == null)
        {
            Debug.LogError("UIFloatingParticle: parentPanel is null!");
            return;
        }

        // Check if the parent panel is still valid (not destroyed)
        if (parentPanel == null || !parentPanel.gameObject.activeInHierarchy)
        {
            return;
        }

        try
        {
            RectTransform newParticle = Instantiate(particlePrefab, parentPanel);
            if (newParticle == null)
            {
                Debug.LogError("UIFloatingParticle: Failed to instantiate particle!");
                return;
            }

            // Get panel dimensions
            RectTransform panelRect = parentPanel as RectTransform;
            if (panelRect == null)
            {
                Debug.LogError("UIFloatingParticle: parentPanel is not a RectTransform!");
                Destroy(newParticle.gameObject);
                return;
            }

            // Generate a random position within the panel's local rect
            Vector2 randomPosition = new Vector2(
                Random.Range(-panelRect.rect.width / 2, panelRect.rect.width / 2),
                Random.Range(-panelRect.rect.height / 2, panelRect.rect.height / 2)
            );

            newParticle.anchoredPosition = randomPosition;

            // Apply size settings
            float size = useRandomSize ?
                Random.Range(sizeRange.x, sizeRange.y) :
                particleSize;

            newParticle.localScale = new Vector3(size, size, 1f);

            // Apply glow shader if enabled
            if (useGlowEffect && glowShader != null)
            {
                Image image = newParticle.GetComponent<Image>();
                if (image != null)
                {
                    // Create a new material instance to avoid affecting other particles
                    Material glowMaterial = new Material(glowShader);

                    // Apply glow color with original alpha
                    Color adjustedGlowColor = glowColor;
                    adjustedGlowColor.a = glowColor.a * glowIntensity;
                    glowMaterial.SetColor("_GlowColor", adjustedGlowColor);

                    glowMaterial.SetFloat("_GlowRadius", glowRadius);

                    // If shader has intensity property, set it directly
                    if (glowMaterial.HasProperty("_GlowIntensity"))
                    {
                        glowMaterial.SetFloat("_GlowIntensity", glowIntensity);
                    }

                    image.material = glowMaterial;
                }
            }

            Vector2 direction = Random.insideUnitCircle.normalized;
            float speed = Random.Range(speedRange.x, speedRange.y);

            StartCoroutine(MoveAndFade(newParticle, direction * speed));
        }
        catch (System.Exception e)
        {
            Debug.LogError($"UIFloatingParticle: Error in SpawnParticle: {e.Message}");
        }
    }

    System.Collections.IEnumerator MoveAndFade(RectTransform rect, Vector2 velocity)
    {
        // Check if the rect is valid
        if (rect == null)
        {
            Debug.LogError("UIFloatingParticle: rect is null in MoveAndFade!");
            yield break;
        }

        // Check if the rect's gameObject is still valid
        if (rect.gameObject == null || !rect.gameObject.activeInHierarchy)
        {
            Debug.LogWarning("UIFloatingParticle: rect gameObject is not active or has been destroyed!");
            yield break;
        }

        float alpha = 1f;
        float lifetime = 0f;

        // Get the Image component
        Image image = null;
        try
        {
            image = rect.GetComponent<Image>();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"UIFloatingParticle: Error getting Image component: {e.Message}");
            if (rect != null && rect.gameObject != null)
            {
                Destroy(rect.gameObject);
            }
            yield break;
        }

        if (image == null)
        {
            Debug.LogError("UIFloatingParticle: Image component is null!");
            if (rect != null && rect.gameObject != null)
            {
                Destroy(rect.gameObject);
            }
            yield break;
        }

        var color = image.color;
        Material material = null;
        bool hasGlowMaterial = false;

        try
        {
            material = image.material;
            hasGlowMaterial = material != null && material.HasProperty("_GlowColor");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"UIFloatingParticle: Error accessing material: {e.Message}");
            // Continue without glow effect
        }

        // Store initial values
        float initialGlowAlpha = 0;
        if (hasGlowMaterial)
        {
            try
            {
                initialGlowAlpha = material.GetColor("_GlowColor").a;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"UIFloatingParticle: Error getting glow color: {e.Message}");
                hasGlowMaterial = false;
            }
        }

        // Store initial scale for size animation
        Vector3 initialScale = Vector3.one;
        try
        {
            initialScale = rect.localScale;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"UIFloatingParticle: Error getting localScale: {e.Message}");
            // Use default scale
        }

        Vector3 targetScale = initialScale;

        // Calculate target scale based on size change mode
        switch (sizeChangeMode)
        {
            case SizeChangeMode.Grow:
                targetScale = initialScale * sizeChangeFactor;
                break;
            case SizeChangeMode.Shrink:
                targetScale = initialScale / sizeChangeFactor;
                break;
        }

        while (alpha > 0)
        {
            // Check if the rect and image are still valid
            if (rect == null || rect.gameObject == null || !rect.gameObject.activeInHierarchy || image == null)
            {
                yield break;
            }

            try
            {
                // Update position
                rect.anchoredPosition += velocity * Time.deltaTime;

                // Update lifetime and alpha
                lifetime += Time.deltaTime;
                alpha = 1f - (lifetime / lifeTime);

                // Ensure alpha doesn't go negative
                alpha = Mathf.Max(0, alpha);

                // Update image color
                color.a = alpha;
                image.color = color;

                // Update glow color alpha if using glow material
                if (hasGlowMaterial && material != null)
                {
                    Color currentGlowColor = material.GetColor("_GlowColor");
                    // Scale the glow alpha proportionally to maintain the intensity ratio
                    currentGlowColor.a = initialGlowAlpha * alpha;
                    material.SetColor("_GlowColor", currentGlowColor);
                }

                // Update size based on lifetime progress if size animation is enabled
                if (sizeChangeMode != SizeChangeMode.None)
                {
                    float t = lifetime / lifeTime; // Normalized time (0 to 1)
                    rect.localScale = Vector3.Lerp(initialScale, targetScale, t);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"UIFloatingParticle: Error during particle animation: {e.Message}");
                break;
            }

            yield return null;
        }

        // Clean up
        try
        {
            if (rect != null && rect.gameObject != null)
            {
                Destroy(rect.gameObject);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"UIFloatingParticle: Error destroying particle: {e.Message}");
        }
    }
}