using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

/// <summary>
/// GameManagerAdapterImpl provides a way to use the GameDataManagerImpl with the existing GameManager
/// without modifying the original GameManager class.
/// </summary>
public class GameManagerAdapterImpl : MonoBehaviour
{
    private GameDataManagerImpl dataManager;
    private MonoBehaviour gameManager;

    private void Awake()
    {
        // Get reference to GameManager
        gameManager = GetComponent<MonoBehaviour>();
        if (gameManager == null || gameManager.GetType().Name != "GameManager")
        {
            Debug.LogError("GameManagerAdapterImpl must be attached to the same GameObject as GameManager");
            return;
        }

        // Initialize GameDataManagerImpl
        dataManager = GameDataManagerImpl.Instance;

        // Migrate data from PlayerPrefs if needed
        if (PlayerPrefs.HasKey("DataMigrationCompleted") == false)
        {
            Debug.Log("Starting data migration from PlayerPrefs to SQLite...");
            MigrateFromPlayerPrefs();
            PlayerPrefs.SetInt("DataMigrationCompleted", 1);
            PlayerPrefs.Save();
            Debug.Log("Data migration completed!");
        }

        // Load resources from database
        LoadResourcesFromDatabase();
    }

    private void OnDestroy()
    {
        // Close database connection when the adapter is destroyed
        if (dataManager != null)
        {
            dataManager.Close();
        }
    }

    #region Resource Management

    // Load resources from database
    private void LoadResourcesFromDatabase()
    {
        dataManager.GetResources(out int dbGold, out int dbFood, out int dbWood, out int dbMetal, out int dbBattlePower, out int dbWelfare);

        // Use reflection to set private fields in GameManager
        var goldField = gameManager.GetType().GetField("gold", BindingFlags.Instance | BindingFlags.NonPublic);
        var foodField = gameManager.GetType().GetField("food", BindingFlags.Instance | BindingFlags.NonPublic);
        var woodField = gameManager.GetType().GetField("wood", BindingFlags.Instance | BindingFlags.NonPublic);
        var metalField = gameManager.GetType().GetField("metal", BindingFlags.Instance | BindingFlags.NonPublic);
        var bpField = gameManager.GetType().GetField("totalBattlePower", BindingFlags.Instance | BindingFlags.NonPublic);
        var welfareField = gameManager.GetType().GetField("totalWelfare", BindingFlags.Instance | BindingFlags.NonPublic);

        if (goldField != null) goldField.SetValue(gameManager, dbGold);
        if (foodField != null) foodField.SetValue(gameManager, dbFood);
        if (woodField != null) woodField.SetValue(gameManager, dbWood);
        if (metalField != null) metalField.SetValue(gameManager, dbMetal);
        if (bpField != null) bpField.SetValue(gameManager, dbBattlePower);
        if (welfareField != null) welfareField.SetValue(gameManager, dbWelfare);

        // Call UpdateResourceUI method
        var updateUIMethod = gameManager.GetType().GetMethod("UpdateResourceUI", BindingFlags.Instance | BindingFlags.NonPublic);
        if (updateUIMethod != null)
            updateUIMethod.Invoke(gameManager, null);
    }

    // Save resources to database
    public void SaveResourcesToDatabase()
    {
        // Get resources from GameManager using reflection
        var goldProperty = gameManager.GetType().GetProperty("Gold");
        var foodProperty = gameManager.GetType().GetProperty("Food");
        var woodProperty = gameManager.GetType().GetProperty("Wood");
        var metalProperty = gameManager.GetType().GetProperty("Metal");

        int gold = (int)goldProperty.GetValue(gameManager);
        int food = (int)foodProperty.GetValue(gameManager);
        int wood = (int)woodProperty.GetValue(gameManager);
        int metal = (int)metalProperty.GetValue(gameManager);

        dataManager.UpdateResources(
            gold,
            food,
            wood,
            metal,
            0, // Battle power will be recalculated
            0  // Welfare will be recalculated
        );
    }

    #endregion

    #region Event Handlers

    // Add these methods to GameManager's event handlers

    // Hook into GameManager's AddGold method
    public void OnAddGold(int amount)
    {
        SaveResourcesToDatabase();
    }

    // Hook into GameManager's SpendGold method
    public void OnSpendGold(int amount)
    {
        SaveResourcesToDatabase();
    }

    // Hook into GameManager's AddResources method
    public void OnAddResources(int foodAmount, int woodAmount, int metalAmount)
    {
        SaveResourcesToDatabase();
    }

    // Hook into GameManager's SpendResources method
    public void OnSpendResources(int foodCost, int woodCost, int metalCost)
    {
        SaveResourcesToDatabase();
    }

    // Hook into GameManager's AddBattlePower method
    public void OnAddBattlePower(int amount, bool isFromResearch = false)
    {
        SaveResourcesToDatabase();
    }

    // Hook into GameManager's AddWelfare method
    public void OnAddWelfare(int amount)
    {
        SaveResourcesToDatabase();
    }

    #endregion

    #region Data Migration

    /// <summary>
    /// Migrate data from PlayerPrefs to SQLite
    /// </summary>
    private void MigrateFromPlayerPrefs()
    {
        Debug.Log("Starting migration from PlayerPrefs to SQLite...");
        
        try
        {
            // Migrate resources
            MigrateResources();
            
            Debug.Log("Migration completed successfully!");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Migration failed: {ex.Message}");
        }
    }

    private void MigrateResources()
    {
        // Get resources from GameManager using reflection
        var goldProperty = gameManager.GetType().GetProperty("Gold");
        var foodProperty = gameManager.GetType().GetProperty("Food");
        var woodProperty = gameManager.GetType().GetProperty("Wood");
        var metalProperty = gameManager.GetType().GetProperty("Metal");

        int gold = (int)goldProperty.GetValue(gameManager);
        int food = (int)foodProperty.GetValue(gameManager);
        int wood = (int)woodProperty.GetValue(gameManager);
        int metal = (int)metalProperty.GetValue(gameManager);

        dataManager.UpdateResources(
            gold,
            food,
            wood,
            metal,
            0, // Battle power will be recalculated
            0  // Welfare will be recalculated
        );
        
        Debug.Log("Resources migrated successfully");
    }

    #endregion
}
