using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using TMPro;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class GameBalanceManager : MonoBehaviour
{
    [Header("References")]
    public GameBalanceData balanceData;
    public BalanceAnalyzer balanceAnalyzer;
    public BalanceExporter balanceExporter;
    public BalanceVisualizer balanceVisualizer;
    public AIBalanceOptimizer aiOptimizer;
    
    [Header("UI Elements")]
    public GameObject balanceManagerPanel;
    public Button collectDataButton;
    public Button analyzeDataButton;
    public Button exportDataButton;
    public Button importDataButton;
    public Button visualizeDataButton;
    public Button generateAIPromptButton;
    public Button importAISuggestionsButton;
    public Button applySuggestionsButton;
    public TextMeshProUGUI statusText;
    public Slider balanceScoreSlider;
    
    [Header("Settings")]
    public bool autoCollectOnStart = false;
    
    private void Start()
    {
        InitializeUI();
        
        if (autoCollectOnStart)
        {
            CollectGameData();
        }
    }
    
    private void InitializeUI()
    {
        if (collectDataButton != null)
            collectDataButton.onClick.AddListener(CollectGameData);
        
        if (analyzeDataButton != null)
            analyzeDataButton.onClick.AddListener(AnalyzeGameBalance);
        
        if (exportDataButton != null)
            exportDataButton.onClick.AddListener(ExportGameData);
        
        if (importDataButton != null)
            importDataButton.onClick.AddListener(ImportGameData);
        
        if (visualizeDataButton != null)
            visualizeDataButton.onClick.AddListener(VisualizeGameData);
        
        if (generateAIPromptButton != null)
            generateAIPromptButton.onClick.AddListener(GenerateAIPrompt);
        
        if (importAISuggestionsButton != null)
            importAISuggestionsButton.onClick.AddListener(ImportAISuggestions);
        
        if (applySuggestionsButton != null)
            applySuggestionsButton.onClick.AddListener(ApplySuggestions);
        
        UpdateUI();
    }
    
    // Collect game data from the current game state
    public void CollectGameData()
    {
        if (balanceExporter == null)
        {
            SetStatus("Error: Balance Exporter not assigned!");
            return;
        }
        
        balanceExporter.CollectCurrentGameData();
        SetStatus("Game data collected successfully!");
        UpdateUI();
    }
    
    // Analyze game balance
    public void AnalyzeGameBalance()
    {
        if (balanceAnalyzer == null)
        {
            SetStatus("Error: Balance Analyzer not assigned!");
            return;
        }
        
        balanceAnalyzer.AnalyzeGameBalance();
        
        // Update balance score
        if (balanceScoreSlider != null)
        {
            float score = CalculateOverallBalanceScore();
            balanceScoreSlider.value = score;
        }
        
        SetStatus($"Game balance analyzed! Found {balanceAnalyzer.detectedIssues.Count} potential issues.");
        UpdateUI();
    }
    
    // Export game data to JSON
    public void ExportGameData()
    {
        if (balanceExporter == null)
        {
            SetStatus("Error: Balance Exporter not assigned!");
            return;
        }
        
        balanceExporter.ExportBalanceData();
        SetStatus("Game data exported successfully!");
    }
    
    // Import game data from JSON
    public void ImportGameData()
    {
        if (balanceExporter == null)
        {
            SetStatus("Error: Balance Exporter not assigned!");
            return;
        }
        
        balanceExporter.ImportBalanceData();
        SetStatus("Game data imported successfully!");
        UpdateUI();
    }
    
    // Visualize game data
    public void VisualizeGameData()
    {
        if (balanceVisualizer == null)
        {
            SetStatus("Error: Balance Visualizer not assigned!");
            return;
        }
        
        balanceVisualizer.Initialize();
        SetStatus("Game data visualization updated!");
    }
    
    // Generate AI prompt
    public void GenerateAIPrompt()
    {
        if (aiOptimizer == null)
        {
            SetStatus("Error: AI Optimizer not assigned!");
            return;
        }
        
        aiOptimizer.ExportAIPrompt();
        SetStatus("AI prompt generated and exported!");
    }
    
    // Import AI suggestions
    public void ImportAISuggestions()
    {
        if (aiOptimizer == null)
        {
            SetStatus("Error: AI Optimizer not assigned!");
            return;
        }
        
        aiOptimizer.ImportAISuggestions();
        SetStatus($"AI suggestions imported! Found {aiOptimizer.suggestedChanges.Count} suggested changes.");
        UpdateUI();
    }
    
    // Apply AI suggestions
    public void ApplySuggestions()
    {
        if (aiOptimizer == null)
        {
            SetStatus("Error: AI Optimizer not assigned!");
            return;
        }
        
        aiOptimizer.ApplySuggestedChanges();
        SetStatus("AI suggestions applied to game balance!");
        
        // Update balance score
        if (balanceScoreSlider != null)
        {
            balanceScoreSlider.value = aiOptimizer.overallBalanceScore;
        }
        
        UpdateUI();
        
        #if UNITY_EDITOR
        if (balanceData != null)
        {
            EditorUtility.SetDirty(balanceData);
            AssetDatabase.SaveAssets();
        }
        #endif
    }
    
    // Calculate overall balance score
    private float CalculateOverallBalanceScore()
    {
        if (aiOptimizer != null)
        {
            return aiOptimizer.overallBalanceScore;
        }
        
        // Fallback calculation if AI optimizer is not available
        if (balanceAnalyzer != null && balanceAnalyzer.detectedIssues != null)
        {
            int issueCount = balanceAnalyzer.detectedIssues.Count;
            float totalSeverity = 0f;
            
            foreach (var issue in balanceAnalyzer.detectedIssues)
            {
                totalSeverity += issue.severity;
            }
            
            // Calculate score (inversely proportional to issues)
            float maxPossibleSeverity = issueCount * 5.0f; // Assuming max severity is 5
            float score = 1.0f - (totalSeverity / (maxPossibleSeverity > 0 ? maxPossibleSeverity : 1.0f));
            
            return Mathf.Clamp01(score);
        }
        
        return 0.5f; // Default middle value
    }
    
    // Update UI state
    private void UpdateUI()
    {
        bool hasData = balanceData != null && 
                      (balanceData.heroBalanceData.Count > 0 || 
                       balanceData.buildingBalanceData.Count > 0 || 
                       balanceData.researchBalanceData.Count > 0);
        
        bool hasAnalysis = balanceAnalyzer != null && 
                          balanceAnalyzer.detectedIssues != null && 
                          balanceAnalyzer.detectedIssues.Count > 0;
        
        bool hasSuggestions = aiOptimizer != null && 
                             aiOptimizer.suggestedChanges != null && 
                             aiOptimizer.suggestedChanges.Count > 0;
        
        // Update button states
        if (analyzeDataButton != null)
            analyzeDataButton.interactable = hasData;
        
        if (exportDataButton != null)
            exportDataButton.interactable = hasData;
        
        if (visualizeDataButton != null)
            visualizeDataButton.interactable = hasData;
        
        if (generateAIPromptButton != null)
            generateAIPromptButton.interactable = hasData;
        
        if (applySuggestionsButton != null)
            applySuggestionsButton.interactable = hasSuggestions;
    }
    
    // Set status message
    private void SetStatus(string message)
    {
        Debug.Log(message);
        
        if (statusText != null)
        {
            statusText.text = message;
        }
    }
    
    // Create a new GameBalanceData asset
    public void CreateNewBalanceData()
    {
        #if UNITY_EDITOR
        GameBalanceData newData = ScriptableObject.CreateInstance<GameBalanceData>();
        
        string path = EditorUtility.SaveFilePanelInProject(
            "Save Game Balance Data",
            "GameBalanceData",
            "asset",
            "Save game balance data as"
        );
        
        if (!string.IsNullOrEmpty(path))
        {
            AssetDatabase.CreateAsset(newData, path);
            AssetDatabase.SaveAssets();
            
            balanceData = newData;
            
            if (balanceAnalyzer != null)
                balanceAnalyzer.balanceData = newData;
            
            if (balanceExporter != null)
                balanceExporter.balanceData = newData;
            
            if (balanceVisualizer != null)
                balanceVisualizer.balanceData = newData;
            
            if (aiOptimizer != null)
                aiOptimizer.balanceData = newData;
            
            SetStatus("New game balance data created!");
        }
        #endif
    }
}
