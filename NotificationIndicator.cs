using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Visual indicator component for notifications.
/// This component handles the visual effects for notification indicators.
/// </summary>
public class NotificationIndicator : MonoBehaviour
{
    [Header("Visual Settings")]
    [SerializeField] private float pulseSpeed = 1.5f;
    [SerializeField] private float minScale = 0.8f;
    [SerializeField] private float maxScale = 1.2f;
    [SerializeField] private Color glowColor = new Color(0, 1, 1, 1);
    [SerializeField] private float glowIntensity = 1.5f;

    [Header("Components")]
    [SerializeField] public Image iconImage;
    [SerializeField] public Image glowImage;
    [SerializeField] public TextMeshProUGUI labelText;
    [SerializeField] public GameObject pulseEffect;
    [SerializeField] private MonoBehaviour energyLineComponent; // Generic reference to UIEnergyLine

    private string targetId;
    private Vector3 originalScale;
    private Material glowMaterial;
    private bool isInitialized = false;

    private void Awake()
    {
        // Store original scale
        originalScale = transform.localScale;

        // Set up glow material if glow image exists
        if (glowImage != null)
        {
            // Try to find UIGlow shader
            Shader glowShader = Shader.Find("Custom/UIGlow");
            if (glowShader != null)
            {
                glowMaterial = new Material(glowShader);
                glowMaterial.SetColor("_GlowColor", glowColor);
                glowMaterial.SetFloat("_GlowIntensity", glowIntensity);
                glowImage.material = glowMaterial;
            }
            else
            {
                Debug.LogWarning("UIGlow shader not found. Using default material for glow effect.");
            }
        }

        // Set up energy line if it exists
        // We'll use reflection to set properties if the component exists
        if (energyLineComponent != null)
        {
            try
            {
                var colorProperty = energyLineComponent.GetType().GetProperty("glowColor");
                var intensityProperty = energyLineComponent.GetType().GetProperty("glowIntensity");

                if (colorProperty != null)
                    colorProperty.SetValue(energyLineComponent, glowColor);

                if (intensityProperty != null)
                    intensityProperty.SetValue(energyLineComponent, glowIntensity);
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to set energy line properties: {e.Message}");
            }
        }
    }

    private void OnEnable()
    {
        // Start visual effects
        if (isInitialized)
        {
            StartCoroutine(PulseAnimation());
        }
    }

    private void OnDisable()
    {
        // Stop all coroutines when disabled
        StopAllCoroutines();
    }

    /// <summary>
    /// Initialize the notification indicator
    /// </summary>
    public void Initialize(string targetId, string labelText = "")
    {
        this.targetId = targetId;

        // Set label text if provided
        if (!string.IsNullOrEmpty(labelText) && this.labelText != null)
        {
            this.labelText.text = labelText;
            this.labelText.gameObject.SetActive(true);
        }
        else if (this.labelText != null)
        {
            this.labelText.gameObject.SetActive(false);
        }

        isInitialized = true;

        // Start visual effects
        StartCoroutine(PulseAnimation());
    }

    /// <summary>
    /// Set the icon for the notification
    /// </summary>
    public void SetIcon(Sprite icon)
    {
        if (iconImage != null && icon != null)
        {
            iconImage.sprite = icon;
            iconImage.gameObject.SetActive(true);
        }
    }

    /// <summary>
    /// Set the label text for the notification
    /// </summary>
    public void SetLabel(string text)
    {
        if (labelText != null)
        {
            labelText.text = text;
            labelText.gameObject.SetActive(!string.IsNullOrEmpty(text));
        }
    }

    /// <summary>
    /// Set the glow color for the notification
    /// </summary>
    public void SetGlowColor(Color color)
    {
        glowColor = color;

        if (glowMaterial != null)
        {
            glowMaterial.SetColor("_GlowColor", glowColor);
        }

        if (energyLineComponent != null)
        {
            try
            {
                var colorProperty = energyLineComponent.GetType().GetProperty("glowColor");
                if (colorProperty != null)
                    colorProperty.SetValue(energyLineComponent, glowColor);
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to set energy line color: {e.Message}");
            }
        }
    }

    /// <summary>
    /// Pulse animation coroutine
    /// </summary>
    private IEnumerator PulseAnimation()
    {
        while (true)
        {
            float t = (Mathf.Sin(Time.time * pulseSpeed) + 1) / 2;
            float scale = Mathf.Lerp(minScale, maxScale, t);

            transform.localScale = originalScale * scale;

            // Update glow intensity if using glow material
            if (glowMaterial != null)
            {
                float glowValue = Mathf.Lerp(glowIntensity * 0.5f, glowIntensity, t);
                glowMaterial.SetFloat("_GlowIntensity", glowValue);
            }

            // Update energy line intensity if it exists
            if (energyLineComponent != null)
            {
                try
                {
                    float lineIntensity = Mathf.Lerp(glowIntensity * 0.5f, glowIntensity, t);
                    var intensityProperty = energyLineComponent.GetType().GetProperty("glowIntensity");
                    if (intensityProperty != null)
                        intensityProperty.SetValue(energyLineComponent, lineIntensity);
                }
                catch (System.Exception)
                {
                    // Silently fail during animation
                }
            }

            yield return null;
        }
    }
}
