using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public enum BuildingCapabilityType
{
    MarchCapacity,
    ReinforcementCapacity,
    AllianceHelps,
    StorageProtection_Food,
    StorageProtection_Wood,
    StorageProtection_Metal,
    ResearchSpeed,
    TrainingCapacity,
    RallyCapacity,
    HospitalCapacity,
    TowerAttack,
    ResourceProduction_Food,
    ResourceProduction_Wood,
    ResourceProduction_Metal,
    ResourceProduction_Energy,
    TrainingSpeed,
    HideTroops,
    DroneDamage,
    FreeDevelopmentChance,
    FreeAdvancedSearch,
    FreeDailySearches,
    BattlePower,
    Welfare
}

[Serializable]
public class BuildingCapability
{
    public BuildingCapabilityType capabilityType;
    public float baseValue;
    public float growthFactor;
    public int growthInterval = 1; // 1 = every level, 2 = every 2 levels, etc.
}

public class BuildingCapabilities : MonoBehaviour
{
    public float TotalEnergy => totalEnergy;
    public float EnergyProduction => energyProduction;
    public float TotalEnergyConsumption => totalEnergyConsumption;

    // Calculate energy balance (positive = surplus, negative = deficit)
    public float EnergyBalance => energyProduction - totalEnergyConsumption;

    // For backward compatibility
    public float EnergyDeficit => energyProduction - totalEnergyConsumption;

    public static BuildingCapabilities Instance { get; private set; }

    private float totalBattlePower = 0;
    private float totalWelfare = 0;

    public int startingFood = 1000;  // Set the initial amount for Food
    public int startingWood = 1000;  // Set the initial amount for Wood
    public int startingMetal = 1000; // Set the initial amount for Metal
    public float startingEnergy = 500; // Set the initial amount for Energy

    public List<BuildingCapability> capabilities = new List<BuildingCapability>();
    public float energyProduction;
    public float energyConsumption;
    public float generationInterval = 60f;
    private float resourceTimer;
    private float energyTimer;
    public static float totalEnergy = 0;
    public static float totalEnergyConsumption = 0;

    // Store dynamically updated values for affected capabilities
    private float currentTrainingSpeed;
    private float currentResearchSpeed;
    private float currentMarchCapacity;

    private void Awake()
    {
        // We need a main instance for global capabilities, but we also need
        // individual instances on each building for building-specific capabilities

        // If this is the first instance or it's on a building, keep it
        if (Instance == null || gameObject.GetComponent<BuildingUpgrade>() != null)
        {
            // If this is the first instance and not on a building, make it the main instance
            if (Instance == null && gameObject.GetComponent<BuildingUpgrade>() == null)
            {
                Instance = this;
                // Debug.Log("Set main BuildingCapabilities instance");
            }

            // Initialize capabilities for this instance
            InitializeCapabilities();
        }
        else
        {
            // This is a duplicate instance not on a building, so destroy it
            // Debug.LogWarning($"Duplicate BuildingCapabilities detected on {gameObject.name} (not a building). Destroying.");
            Destroy(this);
        }
    }

    private void InitializeCapabilities()
    {
        // Initialize with default capabilities based on the building type
        BuildingUpgrade buildingUpgrade = GetComponent<BuildingUpgrade>();

        // This is a building-specific instance
        // Debug.Log($"Initializing capabilities for building: {gameObject.name}");

        // Add default capabilities
        EnsureCapability(BuildingCapabilityType.BattlePower, 10);
        EnsureCapability(BuildingCapabilityType.Welfare, 5);

        // If this building has energy production set in the Inspector, add it as a capability
        if (energyProduction > 0)
        {
            EnsureCapability(BuildingCapabilityType.ResourceProduction_Energy, energyProduction);
            // Debug.Log($"Added Energy Production capability from Inspector value: {energyProduction}");
        }

        // If this building has a BuildingUpgrade component, add capabilities based on building type
        if (buildingUpgrade != null)
        {
            switch (buildingUpgrade.buildingType)
            {
                case BuildingUpgrade.BuildingType.Farm:
                    EnsureCapability(BuildingCapabilityType.ResourceProduction_Food, 100);
                    break;
                case BuildingUpgrade.BuildingType.LumberMill:
                    EnsureCapability(BuildingCapabilityType.ResourceProduction_Wood, 100);
                    break;
                case BuildingUpgrade.BuildingType.Quarry:
                    EnsureCapability(BuildingCapabilityType.ResourceProduction_Metal, 100);
                    break;
                case BuildingUpgrade.BuildingType.PowerPlant:
                    // If energyProduction is already set from Inspector, don't override it
                    if (!HasCapability(BuildingCapabilityType.ResourceProduction_Energy))
                    {
                        // Use the baseResourceGeneration from BuildingUpgrade if available
                        float powerValue = buildingUpgrade.baseResourceGeneration > 0 ?
                            buildingUpgrade.baseResourceGeneration : 50;

                        EnsureCapability(BuildingCapabilityType.ResourceProduction_Energy, powerValue);
                        // Debug.Log($"Added Energy Production capability from BuildingUpgrade: {powerValue}");
                    }
                    break;
            }
        }
    }



    // **Getter properties for Battle Power & Welfare**
    public float TotalBattlePower => totalBattlePower;
    public float TotalWelfare => totalWelfare;


    private bool hasGivenStartingResources = false; // Prevent duplication

    private void Start()
    {
        // Only the main instance should handle global resource initialization
        if (this == Instance)
        {
            // Debug.Log("Main BuildingCapabilities instance starting...");

            if (GameObject.Find("HQ") == null)
            {
                // Debug.LogError("HQ was removed from the scene!");
            }

            if (!hasGivenStartingResources)
            {
                // Debug.Log("Assigning starting resources to player...");

                AddResources(1000, 1000, 1000); // Adjust starting values if needed
                hasGivenStartingResources = true;

                GameManager.Instance.UpdateResourceUI();
            }

            totalEnergy += startingEnergy;
            totalEnergyConsumption += energyConsumption;

            InitializeResourceCapabilities();

            // Initialize energy production from all power plants
            InitializeEnergyProduction();

            Invoke(nameof(DelayedUIUpdate), 0.1f);
        }
        else
        {
            // This is a building-specific instance
            BuildingUpgrade buildingUpgrade = GetComponent<BuildingUpgrade>();
            if (buildingUpgrade != null)
            {
                // Debug.Log($"Building-specific BuildingCapabilities starting on {gameObject.name}");

                // Register this building with the GameManager
                GameManager.Instance.RegisterBuilding(gameObject);

                // If this is a power plant, initialize its energy production
                if (buildingUpgrade.buildingType == BuildingUpgrade.BuildingType.PowerPlant)
                {
                    int generatedAmount = Mathf.RoundToInt(buildingUpgrade.baseResourceGeneration *
                        Mathf.Pow(buildingUpgrade.resourceGenerationGrowthFactor, buildingUpgrade.CurrentLevel - 1));

                    // Add the energy capability if it doesn't exist
                    EnsureCapability(BuildingCapabilityType.ResourceProduction_Energy, generatedAmount);

                    // Debug.Log($"Initialized Power Plant {gameObject.name} with energy production: {generatedAmount}");
                }
            }
        }
    }

    private void InitializeEnergyProduction()
    {
        // Initialize energy production from power plants
        float totalPower = 0;

        // Find all buildings with BuildingCapabilities component
        BuildingCapabilities[] allBuildingCapabilities = FindObjectsByType<BuildingCapabilities>(FindObjectsSortMode.None);

        foreach (BuildingCapabilities bc in allBuildingCapabilities)
        {
            // Skip the main instance to avoid double counting
            if (bc != Instance)
            {
                // Check if this is a power plant (either by BuildingUpgrade or by having the energy capability)
                BuildingUpgrade buildingUpgrade = bc.GetComponent<BuildingUpgrade>();

                // Check for energy production capability
                float energyProdValue = bc.GetCapabilityValue(BuildingCapabilityType.ResourceProduction_Energy);
                if (energyProdValue > 0)
                {
                    totalPower += energyProdValue;
                    // Debug.Log($"Found building {bc.gameObject.name} with energy production capability: {energyProdValue}");
                }
                // If no capability but it's a power plant by type
                else if (buildingUpgrade != null && buildingUpgrade.buildingType == BuildingUpgrade.BuildingType.PowerPlant)
                {
                    int generatedAmount = Mathf.RoundToInt(buildingUpgrade.baseResourceGeneration *
                        Mathf.Pow(buildingUpgrade.resourceGenerationGrowthFactor, buildingUpgrade.CurrentLevel - 1));

                    totalPower += generatedAmount;
                    // Debug.Log($"Found Power Plant {bc.gameObject.name} with energy production: {generatedAmount}");
                }
            }
        }

        // Update the energy production field
        energyProduction = totalPower;

        // Initialize energy consumption
        UpdateTotalEnergyConsumption();

        // Debug.Log($"Total Energy Production initialized to: {energyProduction}, Consumption: {totalEnergyConsumption}");
    }


    private void DelayedUIUpdate()
    {
        GameManager.Instance.UpdateResourceUI();
        RecalculateBattlePowerAndWelfare();
    }


    private void InitializeResourceCapabilities()
    {
        // Ensure each resource capability exists in the list
        EnsureCapability(BuildingCapabilityType.ResourceProduction_Food, startingFood);
        EnsureCapability(BuildingCapabilityType.ResourceProduction_Wood, startingWood);
        EnsureCapability(BuildingCapabilityType.ResourceProduction_Metal, startingMetal);
        EnsureCapability(BuildingCapabilityType.ResourceProduction_Energy, startingEnergy);
    }

    // Helper method to check and add capabilities
    public void EnsureCapability(BuildingCapabilityType type, float baseValue)
    {
        if (!capabilities.Exists(cap => cap.capabilityType == type))
        {
            capabilities.Add(new BuildingCapability { capabilityType = type, baseValue = baseValue, growthFactor = 1.0f });
            // Debug.Log($"Added capability {type} with value {baseValue} to {gameObject.name}");
        }
        else
        {
            // Update the existing capability if the new value is higher
            foreach (var capability in capabilities)
            {
                if (capability.capabilityType == type && capability.baseValue < baseValue)
                {
                    capability.baseValue = baseValue;
                    // Debug.Log($"Updated capability {type} to higher value {baseValue} on {gameObject.name}");
                    break;
                }
            }
        }
    }


    private void Update()
    {
        // Only the main instance should handle global resource generation
        if (this == Instance)
        {
            resourceTimer += Time.deltaTime;
            energyTimer += Time.deltaTime;

            // Update energy production and consumption from all buildings
            UpdateEnergyProduction();

            if (resourceTimer >= generationInterval)
            {
                GenerateResources();
                resourceTimer = 0;
            }

            if (energyTimer >= 3600f)
            {
                // Add energy production to total energy
                totalEnergy += energyProduction;

                // Energy consumption is already updated by UpdateEnergyProduction

                energyTimer = 0;
            }
        }
    }

    public void UpdateEnergyProduction()
    {
        // Only update the main instance's energy production
        if (this == Instance)
        {
            // Reset total energy production
            float totalEnergyProd = 0;

            // Find all BuildingCapabilities in the scene
            BuildingCapabilities[] allBuildingCapabilities = FindObjectsByType<BuildingCapabilities>(FindObjectsSortMode.None);

            foreach (BuildingCapabilities bc in allBuildingCapabilities)
            {
                // Skip the main instance to avoid double counting
                if (bc != Instance)
                {
                    // Check for energy production capability
                    float buildingEnergyProd = bc.GetCapabilityValue(BuildingCapabilityType.ResourceProduction_Energy);

                    // If no capability value but energyProduction is set directly in the Inspector
                    if (buildingEnergyProd <= 0 && bc.energyProduction > 0)
                    {
                        // Add the Inspector value to the capability
                        bc.EnsureCapability(BuildingCapabilityType.ResourceProduction_Energy, bc.energyProduction);
                        buildingEnergyProd = bc.energyProduction;
                    }

                    if (buildingEnergyProd > 0)
                    {
                        totalEnergyProd += buildingEnergyProd;
                    }
                }
            }

            // Update the main instance's energyProduction field
            energyProduction = totalEnergyProd;

            // Update total energy consumption
            UpdateTotalEnergyConsumption();

            // Debug log
        }
    }

    private void UpdateTotalEnergyConsumption()
    {
        // Reset the total consumption
        totalEnergyConsumption = 0;

        // Find all BuildingCapabilities in the scene
        BuildingCapabilities[] allBuildingCapabilities = FindObjectsByType<BuildingCapabilities>(FindObjectsSortMode.None);

        foreach (BuildingCapabilities bc in allBuildingCapabilities)
        {
            // Skip the main instance to avoid double counting
            if (bc != Instance)
            {
                totalEnergyConsumption += bc.energyConsumption;
            }
        }

        // Add the main instance's own consumption
        totalEnergyConsumption += energyConsumption;

        // Debug.Log($"Updated total energy consumption: {totalEnergyConsumption}");
    }

    private void GenerateResources()
    {
        float energyDeficit = totalEnergy - totalEnergyConsumption;
        float deficitPercentage = (totalEnergyConsumption > 0) ? (energyDeficit / totalEnergyConsumption * 100f) : 0f;
        float penaltyMultiplier = 1f;

        if (deficitPercentage < 0)
        {
            if (deficitPercentage >= -20f)
                penaltyMultiplier = 0.75f; // Mild Deficit
            else
                penaltyMultiplier = 0.5f;  // Severe Deficit
        }

        foreach (var capability in capabilities)
        {
            // Apply penalty to resource production
            if (capability.capabilityType == BuildingCapabilityType.ResourceProduction_Food ||
                capability.capabilityType == BuildingCapabilityType.ResourceProduction_Wood ||
                capability.capabilityType == BuildingCapabilityType.ResourceProduction_Metal ||
                capability.capabilityType == BuildingCapabilityType.ResourceProduction_Energy)
            {
                float generatedAmount = capability.baseValue * penaltyMultiplier;
                AddResource(capability.capabilityType, generatedAmount);
            }
        }

        // Apply dynamic penalties in case of Severe Deficit
        if (penaltyMultiplier == 0.5f)
        {
            ApplyCapabilityPenalty(BuildingCapabilityType.TrainingSpeed, penaltyMultiplier);
            ApplyCapabilityPenalty(BuildingCapabilityType.ResearchSpeed, penaltyMultiplier);
            ApplyCapabilityPenalty(BuildingCapabilityType.MarchCapacity, penaltyMultiplier);
        }
        else
        {
            // Reset values to normal if no severe deficit
            ResetPenalties();
        }
    }

    public void AddBattlePower(float amount)
    {
        totalBattlePower += amount;
        GameManager.Instance.UpdateResourceUI();
        // Debug.Log($"Total Battle Power updated: {totalBattlePower}");
    }

    public void AddWelfare(float amount)
    {
        totalWelfare += amount;
        GameManager.Instance.UpdateResourceUI();
        // Debug.Log($"Total Welfare updated: {totalWelfare}");
    }


    public void AddResources(int foodAmount, int woodAmount, int stoneAmount)
    {
        foreach (var capability in capabilities)
        {
            if (capability.capabilityType == BuildingCapabilityType.ResourceProduction_Food && foodAmount > 0)
            {
                // Debug.Log($"Adding {foodAmount} Food to {capability.capabilityType}");
                capability.baseValue += foodAmount;
            }

            if (capability.capabilityType == BuildingCapabilityType.ResourceProduction_Wood && woodAmount > 0)
            {
                // Debug.Log($"Adding {woodAmount} Wood to {capability.capabilityType}");
                capability.baseValue += woodAmount;
            }

            if (capability.capabilityType == BuildingCapabilityType.ResourceProduction_Metal && stoneAmount > 0)
            {
                // Debug.Log($"Adding {stoneAmount} Metal to {capability.capabilityType}");
                capability.baseValue += stoneAmount;
            }
        }

        GameManager.Instance.UpdateResourceUI();
    }


    public float TotalResource(BuildingCapabilityType type)
    {
        float total = 0;
        foreach (var capability in capabilities)
        {
            if (capability.capabilityType == type)
                total += capability.baseValue;
        }
        return total;
    }

    public bool HasEnoughResources(int foodCost, int woodCost, int stoneCost)
    {
        return Mathf.RoundToInt(TotalResource(BuildingCapabilityType.ResourceProduction_Food)) >= foodCost &&
               Mathf.RoundToInt(TotalResource(BuildingCapabilityType.ResourceProduction_Wood)) >= woodCost &&
               Mathf.RoundToInt(TotalResource(BuildingCapabilityType.ResourceProduction_Metal)) >= stoneCost;
    }

    public void SpendResources(int foodCost, int woodCost, int stoneCost)
    {
        foreach (var capability in capabilities)
        {
            if (capability.capabilityType == BuildingCapabilityType.ResourceProduction_Food && foodCost > 0)
                capability.baseValue = Mathf.Max(0, capability.baseValue - foodCost);

            if (capability.capabilityType == BuildingCapabilityType.ResourceProduction_Wood && woodCost > 0)
                capability.baseValue = Mathf.Max(0, capability.baseValue - woodCost);

            if (capability.capabilityType == BuildingCapabilityType.ResourceProduction_Metal && stoneCost > 0)
                capability.baseValue = Mathf.Max(0, capability.baseValue - stoneCost);
        }
        GameManager.Instance.UpdateResourceUI();

        // Debug.Log($"Available: {TotalResource(BuildingCapabilityType.ResourceProduction_Food)} Food, " +
        //  $"{TotalResource(BuildingCapabilityType.ResourceProduction_Wood)} Wood, " +
        //  $"{TotalResource(BuildingCapabilityType.ResourceProduction_Metal)} Metal");

    }



    private void ApplyCapabilityPenalty(BuildingCapabilityType type, float multiplier)
    {
        float newValue = GetCapabilityValue(type) * multiplier;

        switch (type)
        {
            case BuildingCapabilityType.TrainingSpeed:
                currentTrainingSpeed = newValue;
                // Debug.Log($"Severe Deficit! Training Speed reduced to {currentTrainingSpeed}");
                break;

            case BuildingCapabilityType.ResearchSpeed:
                currentResearchSpeed = newValue;
                // Debug.Log($"Severe Deficit! Research Speed reduced to {currentResearchSpeed}");
                break;

            case BuildingCapabilityType.MarchCapacity:
                currentMarchCapacity = newValue;
                // Debug.Log($"Severe Deficit! March Capacity reduced to {currentMarchCapacity}");
                break;
        }
    }

    private void ResetPenalties()
    {
        currentTrainingSpeed = GetCapabilityValue(BuildingCapabilityType.TrainingSpeed);
        currentResearchSpeed = GetCapabilityValue(BuildingCapabilityType.ResearchSpeed);
        currentMarchCapacity = GetCapabilityValue(BuildingCapabilityType.MarchCapacity);

        // Debug.Log("Energy stabilized. Training Speed, Research Speed, and March Capacity restored to normal.");
    }

    // Method to get capability value
    public float GetCapabilityValue(BuildingCapabilityType type)
    {
        foreach (var capability in capabilities)
        {
            if (capability.capabilityType == type)
            {
                return capability.baseValue;
            }
        }
        return 0;
    }

    private void AddResource(BuildingCapabilityType type, float amount)
    {
        // Debug.Log($"Generated {amount} of {type}");

        // Update the appropriate resource based on type
        switch (type)
        {
            case BuildingCapabilityType.ResourceProduction_Energy:
                // Update the energyProduction field to match the capability
                energyProduction = amount;
                // Debug.Log($"Updated Energy Production to {energyProduction}");
                break;
            // Other resource types can be handled here
        }
    }

    public void RecalculateBattlePowerAndWelfare()
    {
        totalBattlePower = 0;
        totalWelfare = 0;

        // Recalculate from all buildings in the scene
        foreach (BuildingUpgrade building in FindObjectsByType<BuildingUpgrade>(FindObjectsSortMode.None))
        {
            totalBattlePower += building.baseBattlePower * Mathf.Pow(building.battlePowerGrowthFactor, building.CurrentLevel - 1);
            totalWelfare += building.baseWelfare * Mathf.Pow(building.welfareGrowthFactor, building.CurrentLevel - 1);
        }

        GameManager.Instance.UpdateResourceUI();
        // Debug.Log($"Recalculated BP: {totalBattlePower}, Welfare: {totalWelfare}");
    }

    public bool HasCapability(BuildingCapabilityType type)
    {
        return capabilities.Exists(cap => cap.capabilityType == type);
    }


}

