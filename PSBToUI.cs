using UnityEngine;
using UnityEngine.UI;

public class PSBToUI : MonoBehaviour
{
    public Sprite[] psbSprites; // Assign the sliced PSB sprite pieces in the Inspector
    public Transform uiParent; // Assign a UI Panel inside the Canvas as the parent

    void Start()
    {
        foreach (Sprite sprite in psbSprites) 
        {
            // Create a new UI Image for each sprite
            GameObject newImage = new GameObject(sprite.name, typeof(Image));
            newImage.transform.SetParent(uiParent, false); // Set parent to UI Panel

            Image img = newImage.GetComponent<Image>();
            img.sprite = sprite; // Assign the PSB sprite
            img.rectTransform.sizeDelta = sprite.bounds.size * 100; // Adjust size for UI scaling
            img.rectTransform.anchoredPosition = Vector2.zero; // Center in the UI Parent
        }
    }
}
