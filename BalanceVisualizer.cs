using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using HeroSystem;

public class BalanceVisualizer : MonoBehaviour
{
    [Header("References")]
    public GameBalanceData balanceData;
    public BalanceAnalyzer balanceAnalyzer;

    [Header("UI Elements")]
    public GameObject visualizerPanel;
    public Transform heroBalanceContent;
    public Transform buildingBalanceContent;
    public Transform researchBalanceContent;
    public Transform suggestionsContent;

    [Header("Prefabs")]
    public GameObject heroCardPrefab;
    public GameObject buildingCardPrefab;
    public GameObject researchCardPrefab;
    public GameObject suggestionCardPrefab;

    [Header("Chart Settings")]
    public RectTransform heroChartContainer;
    public RectTransform buildingChartContainer;
    public RectTransform researchChartContainer;
    public RectTransform progressionChartContainer;

    private List<GameObject> instantiatedCards = new List<GameObject>();

    // Initialize the visualizer
    public void Initialize()
    {
        ClearVisualizer();

        if (balanceData == null)
        {
            Debug.LogError("No balance data assigned to visualizer!");
            return;
        }

        // Visualize hero balance
        VisualizeHeroBalance();

        // Visualize building balance
        VisualizeBuildingBalance();

        // Visualize research balance
        VisualizeResearchBalance();

        // Visualize progression curve
        VisualizeProgressionCurve();

        // Show balance suggestions
        if (balanceAnalyzer != null)
        {
            ShowBalanceSuggestions();
        }
    }

    // Clear all visualizations
    public void ClearVisualizer()
    {
        foreach (var card in instantiatedCards)
        {
            Destroy(card);
        }

        instantiatedCards.Clear();
    }

    // Visualize hero balance
    private void VisualizeHeroBalance()
    {
        if (heroBalanceContent == null || heroCardPrefab == null) return;

        foreach (var hero in balanceData.heroBalanceData)
        {
            GameObject card = Instantiate(heroCardPrefab, heroBalanceContent);
            instantiatedCards.Add(card);

            // Set card data
            TextMeshProUGUI nameText = card.transform.Find("NameText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI powerText = card.transform.Find("PowerText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI typeText = card.transform.Find("TypeText")?.GetComponent<TextMeshProUGUI>();
            Image rarityImage = card.transform.Find("RarityImage")?.GetComponent<Image>();

            if (nameText != null) nameText.text = hero.heroName;
            if (powerText != null) powerText.text = $"Power: {hero.initialPower}";
            if (typeText != null) typeText.text = $"Type: {hero.heroType}";

            if (rarityImage != null)
            {
                // Set color based on rarity
                switch (hero.rarity)
                {
                    case HeroRarity.Rare:
                        rarityImage.color = new Color(0.2f, 0.6f, 1f);
                        break;
                    case HeroRarity.Elite:
                        rarityImage.color = new Color(0.6f, 0.2f, 1f);
                        break;
                    case HeroRarity.Legendary:
                        rarityImage.color = new Color(1f, 0.8f, 0.2f);
                        break;
                }
            }
        }

        // Create hero power chart
        CreateHeroPowerChart();
    }

    // Visualize building balance
    private void VisualizeBuildingBalance()
    {
        if (buildingBalanceContent == null || buildingCardPrefab == null) return;

        foreach (var building in balanceData.buildingBalanceData)
        {
            GameObject card = Instantiate(buildingCardPrefab, buildingBalanceContent);
            instantiatedCards.Add(card);

            // Set card data
            TextMeshProUGUI nameText = card.transform.Find("NameText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI typeText = card.transform.Find("TypeText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI costText = card.transform.Find("CostText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI benefitText = card.transform.Find("BenefitText")?.GetComponent<TextMeshProUGUI>();

            if (nameText != null) nameText.text = building.buildingName;
            if (typeText != null) typeText.text = $"Type: {building.buildingType}";
            if (costText != null) costText.text = $"Base Costs: F{building.baseFoodCost} W{building.baseWoodCost} M{building.baseMetalCost}";

            string benefitStr = "Benefits: ";
            if (building.baseBattlePower > 0) benefitStr += $"BP+{building.baseBattlePower} ";
            if (building.baseWelfare > 0) benefitStr += $"WF+{building.baseWelfare} ";
            if (building.baseResourceGeneration > 0) benefitStr += $"Gen+{building.baseResourceGeneration}";

            if (benefitText != null) benefitText.text = benefitStr;
        }

        // Create building cost-benefit chart
        CreateBuildingCostBenefitChart();
    }

    // Visualize research balance
    private void VisualizeResearchBalance()
    {
        if (researchBalanceContent == null || researchCardPrefab == null) return;

        foreach (var research in balanceData.researchBalanceData)
        {
            GameObject card = Instantiate(researchCardPrefab, researchBalanceContent);
            instantiatedCards.Add(card);

            // Set card data
            TextMeshProUGUI nameText = card.transform.Find("NameText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI typeText = card.transform.Find("TypeText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI bonusText = card.transform.Find("BonusText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI timeText = card.transform.Find("TimeText")?.GetComponent<TextMeshProUGUI>();

            if (nameText != null) nameText.text = research.researchName;
            if (typeText != null) typeText.text = $"Type: {research.nodeType}";

            string bonusStr = $"Bonus: {research.bonusType}";
            if (research.isPercentageBonus) bonusStr += " (%)";

            if (bonusText != null) bonusText.text = bonusStr;
            if (timeText != null) timeText.text = $"Time: {research.startingTime}s";
        }

        // Create research time-value chart
        CreateResearchTimeValueChart();
    }

    // Visualize progression curve
    private void VisualizeProgressionCurve()
    {
        // Create progression curve chart
        CreateProgressionCurveChart();
    }

    // Show balance suggestions
    private void ShowBalanceSuggestions()
    {
        if (suggestionsContent == null || suggestionCardPrefab == null) return;

        // Analyze balance
        balanceAnalyzer.AnalyzeGameBalance();

        // Get suggestions
        List<BalanceSuggestion> suggestions = balanceAnalyzer.GenerateBalanceSuggestions();

        foreach (var suggestion in suggestions)
        {
            GameObject card = Instantiate(suggestionCardPrefab, suggestionsContent);
            instantiatedCards.Add(card);

            // Set card data
            TextMeshProUGUI titleText = card.transform.Find("TitleText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI descriptionText = card.transform.Find("DescriptionText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI suggestionText = card.transform.Find("SuggestionText")?.GetComponent<TextMeshProUGUI>();
            Image priorityImage = card.transform.Find("PriorityImage")?.GetComponent<Image>();

            if (titleText != null) titleText.text = $"{suggestion.issueType}";
            if (descriptionText != null) descriptionText.text = suggestion.description;
            if (suggestionText != null) suggestionText.text = suggestion.suggestion;

            if (priorityImage != null)
            {
                // Set color based on priority
                switch (suggestion.priority)
                {
                    case SuggestionPriority.Low:
                        priorityImage.color = Color.yellow;
                        break;
                    case SuggestionPriority.Medium:
                        priorityImage.color = new Color(1f, 0.5f, 0f); // Orange
                        break;
                    case SuggestionPriority.High:
                        priorityImage.color = Color.red;
                        break;
                }
            }
        }
    }

    // Chart creation methods
    private void CreateHeroPowerChart()
    {
        if (heroChartContainer == null) return;

        // This is a placeholder for actual chart creation
        // In a real implementation, you would use a charting library or create custom visualization

        Debug.Log("Hero power chart would be created here");

        // Example of what you might do:
        // 1. Group heroes by type
        // 2. Calculate average power by type
        // 3. Create bar chart showing power distribution
    }

    private void CreateBuildingCostBenefitChart()
    {
        if (buildingChartContainer == null) return;

        // This is a placeholder for actual chart creation
        Debug.Log("Building cost-benefit chart would be created here");
    }

    private void CreateResearchTimeValueChart()
    {
        if (researchChartContainer == null) return;

        // This is a placeholder for actual chart creation
        Debug.Log("Research time-value chart would be created here");
    }

    private void CreateProgressionCurveChart()
    {
        if (progressionChartContainer == null) return;

        // This is a placeholder for actual chart creation
        Debug.Log("Progression curve chart would be created here");
    }
}
