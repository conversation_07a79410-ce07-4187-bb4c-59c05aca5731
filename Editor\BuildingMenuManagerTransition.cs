using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

public class BuildingMenuManagerTransition : EditorWindow
{
    private bool showHelp = true;
    private Vector2 scrollPosition;

    [MenuItem("Tools/Building System/Building Menu Manager Transition")]
    public static void ShowWindow()
    {
        GetWindow<BuildingMenuManagerTransition>("Menu Manager Transition");
    }

    private void OnGUI()
    {
        GUILayout.Label("Building Menu Manager Transition", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        showHelp = EditorGUILayout.Foldout(showHelp, "Transition Information");
        if (showHelp)
        {
            EditorGUILayout.HelpBox(
                "The BuildingMenuManager class is no longer needed with the new BuildingMenu system. " +
                "Each building now manages its own menu through the BuildingMenu component.\n\n" +
                "This tool will help you transition from the old system to the new one.",
                MessageType.Info);
        }

        EditorGUILayout.Space();

        // Check for BuildingMenuManager in the scene
        BuildingMenuManager[] managers = FindObjectsByType<BuildingMenuManager>(FindObjectsSortMode.None);
        if (managers.Length > 0)
        {
            EditorGUILayout.HelpBox(
                $"Found {managers.Length} BuildingMenuManager instance(s) in the scene. " +
                "These can be safely removed after all buildings have been converted to use the new BuildingMenu component.",
                MessageType.Warning);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(100));
            foreach (BuildingMenuManager manager in managers)
            {
                EditorGUILayout.ObjectField(manager, typeof(BuildingMenuManager), true);
            }
            EditorGUILayout.EndScrollView();

            if (GUILayout.Button("Remove BuildingMenuManager Instances"))
            {
                if (EditorUtility.DisplayDialog(
                    "Remove BuildingMenuManager Instances",
                    "Are you sure you want to remove all BuildingMenuManager instances from the scene? " +
                    "Make sure all buildings have been converted to use the new BuildingMenu component first.",
                    "Yes, Remove", "Cancel"))
                {
                    RemoveBuildingMenuManagers();
                }
            }
        }
        else
        {
            EditorGUILayout.HelpBox(
                "No BuildingMenuManager instances found in the scene. " +
                "You have successfully transitioned to the new BuildingMenu system!",
                MessageType.Info);
        }

        EditorGUILayout.Space();

        // Check for buildings without BuildingMenu component
        BuildingUpgrade[] buildings = FindObjectsByType<BuildingUpgrade>(FindObjectsSortMode.None);
        List<BuildingUpgrade> buildingsWithoutMenu = new List<BuildingUpgrade>();

        foreach (BuildingUpgrade building in buildings)
        {
            if (building.GetComponent<BuildingMenu>() == null)
            {
                buildingsWithoutMenu.Add(building);
            }
        }

        if (buildingsWithoutMenu.Count > 0)
        {
            EditorGUILayout.HelpBox(
                $"Found {buildingsWithoutMenu.Count} building(s) without a BuildingMenu component. " +
                "These buildings will not work with the new menu system.",
                MessageType.Warning);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(100));
            foreach (BuildingUpgrade building in buildingsWithoutMenu)
            {
                EditorGUILayout.ObjectField(building, typeof(BuildingUpgrade), true);
            }
            EditorGUILayout.EndScrollView();

            if (GUILayout.Button("Convert Buildings"))
            {
                if (EditorUtility.DisplayDialog(
                    "Convert Buildings",
                    "This will add the BuildingMenu component to all buildings that don't have it. " +
                    "You will need to configure the menu settings for each building afterward.",
                    "Convert", "Cancel"))
                {
                    ConvertBuildings(buildingsWithoutMenu);
                }
            }
        }
        else
        {
            EditorGUILayout.HelpBox(
                "All buildings have the BuildingMenu component. " +
                "Your buildings are ready to use the new menu system!",
                MessageType.Info);
        }

        EditorGUILayout.Space();

        // Check for prefabs
        if (GUILayout.Button("Check Building Prefabs"))
        {
            CheckBuildingPrefabs();
        }
    }

    private void RemoveBuildingMenuManagers()
    {
        BuildingMenuManager[] managers = FindObjectsByType<BuildingMenuManager>(FindObjectsSortMode.None);
        foreach (BuildingMenuManager manager in managers)
        {
            DestroyImmediate(manager.gameObject);
        }

        Debug.Log("Removed all BuildingMenuManager instances from the scene.");
    }

    private void ConvertBuildings(List<BuildingUpgrade> buildings)
    {
        foreach (BuildingUpgrade building in buildings)
        {
            BuildingMenu menu = building.gameObject.AddComponent<BuildingMenu>();
            menu.buildingName = building.gameObject.name;
            menu.buildingDescription = $"{building.gameObject.name} - Level {building.CurrentLevel}";

            // Set default button visibility
            menu.hasInfoButton = true;
            menu.hasUpgradeButton = true;
            menu.hasMoveButton = true;

            // Set function button based on building type
            switch (building.buildingType)
            {
                case BuildingUpgrade.BuildingType.Farm:
                case BuildingUpgrade.BuildingType.LumberMill:
                case BuildingUpgrade.BuildingType.Quarry: // Metal production
                    menu.hasFunctionButton = false;
                    break;
                case BuildingUpgrade.BuildingType.NonGenerating:
                    // Check for specific building types by name
                    if (building.name.Contains("Barracks"))
                    {
                        menu.hasFunctionButton = true;
                        menu.functionButtonText = "Train";
                        menu.functionName = "training";
                    }
                    else if (building.name.Contains("Research"))
                    {
                        menu.hasFunctionButton = true;
                        menu.functionButtonText = "Research";
                        menu.functionName = "research";
                    }
                    break;
            }

            EditorUtility.SetDirty(building.gameObject);
        }

        Debug.Log($"Added BuildingMenu component to {buildings.Count} buildings.");
    }

    private void CheckBuildingPrefabs()
    {
        string[] guids = AssetDatabase.FindAssets("t:Prefab", new[] { "Assets" });
        List<GameObject> buildingPrefabs = new List<GameObject>();
        List<GameObject> prefabsWithoutMenu = new List<GameObject>();

        foreach (string guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);

            if (prefab.GetComponent<BuildingUpgrade>() != null)
            {
                buildingPrefabs.Add(prefab);

                if (prefab.GetComponent<BuildingMenu>() == null)
                {
                    prefabsWithoutMenu.Add(prefab);
                }
            }
        }

        if (prefabsWithoutMenu.Count > 0)
        {
            if (EditorUtility.DisplayDialog(
                "Building Prefabs Without Menu",
                $"Found {prefabsWithoutMenu.Count} building prefab(s) without a BuildingMenu component. " +
                "Would you like to add the BuildingMenu component to these prefabs?",
                "Yes", "No"))
            {
                foreach (GameObject prefab in prefabsWithoutMenu)
                {
                    // Get the prefab asset
                    GameObject prefabAsset = PrefabUtility.GetCorrespondingObjectFromSource(prefab);
                    if (prefabAsset == null)
                    {
                        prefabAsset = prefab;
                    }

                    // Add BuildingMenu component
                    BuildingMenu menu = prefabAsset.AddComponent<BuildingMenu>();
                    menu.buildingName = prefabAsset.name;

                    // Set default button visibility
                    menu.hasInfoButton = true;
                    menu.hasUpgradeButton = true;
                    menu.hasMoveButton = true;

                    // Save the prefab
                    EditorUtility.SetDirty(prefabAsset);
                    PrefabUtility.SavePrefabAsset(prefabAsset);
                }

                Debug.Log($"Added BuildingMenu component to {prefabsWithoutMenu.Count} building prefabs.");
            }
        }
        else
        {
            EditorUtility.DisplayDialog(
                "Building Prefabs",
                $"All {buildingPrefabs.Count} building prefabs have the BuildingMenu component.",
                "OK");
        }
    }
}
