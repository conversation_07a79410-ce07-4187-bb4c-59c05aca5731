using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// This file contains fixes for the GameDataManager implementation
/// </summary>

// 1. Fix for GameManager partial class
#if UNITY_EDITOR
using UnityEditor;

[InitializeOnLoad]
public class GameManagerPartialFix
{
    static GameManagerPartialFix()
    {
        // This will run when Unity starts or when scripts are recompiled
        FixGameManagerClass();
    }

    private static void FixGameManagerClass()
    {
        string gameManagerPath = "Assets/Game System/GameManager.cs";

        try
        {
            string content = System.IO.File.ReadAllText(gameManagerPath);

            // Add partial modifier if it doesn't exist
            if (!content.Contains("public partial class GameManager"))
            {
                content = content.Replace("public class GameManager", "public partial class GameManager");
                System.IO.File.WriteAllText(gameManagerPath, content);
                Debug.Log("Added partial modifier to GameManager class");

                // Force Unity to recompile scripts
                AssetDatabase.ImportAsset(gameManagerPath);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to fix GameManager class: {e.Message}");
        }
    }
}
#endif

// 2. Fix for GameManagerExtension class
// This class should be used instead of the original GameManagerExtension.cs
public static class GameManagerExtensionMethods
{
    // Initialize SimpleGameDataManager
    public static void InitializeDataManager(this GameManager gameManager)
    {
        var dataManager = SimpleGameDataManager.Instance;

        // Store reference to dataManager in a field
        var field = typeof(GameManager).GetField("dataManager",
            System.Reflection.BindingFlags.Instance |
            System.Reflection.BindingFlags.NonPublic);

        if (field != null)
            field.SetValue(gameManager, dataManager);

        // Load resources from PlayerPrefs
        LoadResourcesFromPlayerPrefs(gameManager, dataManager);
    }

    // Load resources from PlayerPrefs
    public static void LoadResourcesFromPlayerPrefs(this GameManager gameManager, SimpleGameDataManager dataManager)
    {
        dataManager.GetResources(out int dbGold, out int dbFood, out int dbWood, out int dbMetal, out int dbBattlePower, out int dbWelfare);

        // Set the values using reflection
        var goldField = typeof(GameManager).GetField("gold",
            System.Reflection.BindingFlags.Instance |
            System.Reflection.BindingFlags.NonPublic);

        var foodField = typeof(GameManager).GetField("food",
            System.Reflection.BindingFlags.Instance |
            System.Reflection.BindingFlags.NonPublic);

        var woodField = typeof(GameManager).GetField("wood",
            System.Reflection.BindingFlags.Instance |
            System.Reflection.BindingFlags.NonPublic);

        var metalField = typeof(GameManager).GetField("metal",
            System.Reflection.BindingFlags.Instance |
            System.Reflection.BindingFlags.NonPublic);

        var bpField = typeof(GameManager).GetField("totalBattlePower",
            System.Reflection.BindingFlags.Instance |
            System.Reflection.BindingFlags.NonPublic);

        var welfareField = typeof(GameManager).GetField("totalWelfare",
            System.Reflection.BindingFlags.Instance |
            System.Reflection.BindingFlags.NonPublic);

        if (goldField != null) goldField.SetValue(gameManager, dbGold);
        if (foodField != null) foodField.SetValue(gameManager, dbFood);
        if (woodField != null) woodField.SetValue(gameManager, dbWood);
        if (metalField != null) metalField.SetValue(gameManager, dbMetal);
        if (bpField != null) bpField.SetValue(gameManager, dbBattlePower);
        if (welfareField != null) welfareField.SetValue(gameManager, dbWelfare);

        // Call UpdateResourceUI method
        var updateUIMethod = typeof(GameManager).GetMethod("UpdateResourceUI",
            System.Reflection.BindingFlags.Instance |
            System.Reflection.BindingFlags.NonPublic);

        if (updateUIMethod != null)
            updateUIMethod.Invoke(gameManager, null);
    }

    // Save resources to PlayerPrefs
    public static void SaveResourcesToPlayerPrefs(this GameManager gameManager, SimpleGameDataManager dataManager)
    {
        dataManager.UpdateResources(
            gameManager.Gold,
            gameManager.Food,
            gameManager.Wood,
            gameManager.Metal,
            0, // Battle power will be recalculated
            0  // Welfare will be recalculated
        );
    }
}

// 3. Fix for HeroData class conflict
// This should be placed in a separate file or namespace
namespace HeroSystem
{
    [System.Serializable]
    public class HeroDataWrapper
    {
        // Add properties that match the original HeroData class
        // This is just a placeholder - you'll need to add the actual properties
        public string HeroName { get; set; }
        public int Rank { get; set; }
        public int ExpLevel { get; set; }
        // Add other properties as needed
    }
}
