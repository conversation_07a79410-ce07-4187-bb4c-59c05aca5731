using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text.RegularExpressions;

/// <summary>
/// Editor utility to help set up the SimpleGameDataManager
/// </summary>
public class SimpleGameDataManagerSetup : EditorWindow
{
    private bool fixGameManagerPartial = true;
    private bool attachAdapter = true;
    private bool fixHeroDataConflict = true;
    private bool deleteConflictingFiles = true;
    private GameObject gameManagerObject;

    [MenuItem("Tools/Simple Game Data Manager Setup")]
    public static void ShowWindow()
    {
        GetWindow<SimpleGameDataManagerSetup>("Simple Game Data Manager Setup");
    }

    private void OnGUI()
    {
        GUILayout.Label("Simple Game Data Manager Setup", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        fixGameManagerPartial = EditorGUILayout.Toggle("Fix GameManager Partial", fixGameManagerPartial);
        attachAdapter = EditorGUILayout.Toggle("Attach Adapter to GameManager", attachAdapter);
        fixHeroDataConflict = EditorGUILayout.Toggle("Fix HeroData Conflict", fixHeroDataConflict);
        deleteConflictingFiles = EditorGUILayout.Toggle("Delete Conflicting Files", deleteConflictingFiles);

        EditorGUILayout.Space();
        
        gameManagerObject = EditorGUILayout.ObjectField("GameManager Object", gameManagerObject, typeof(GameObject), true) as GameObject;

        EditorGUILayout.Space();

        if (GUILayout.Button("Apply Changes"))
        {
            ApplyChanges();
        }

        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("This tool will help you set up the SimpleGameDataManager by fixing common issues.", MessageType.Info);
    }

    private void ApplyChanges()
    {
        if (deleteConflictingFiles)
        {
            DeleteConflictingFiles();
        }

        if (fixGameManagerPartial)
        {
            FixGameManagerPartial();
        }

        if (attachAdapter && gameManagerObject != null)
        {
            AttachAdapter();
        }

        if (fixHeroDataConflict)
        {
            FixHeroDataConflict();
        }

        AssetDatabase.Refresh();
    }

    private void DeleteConflictingFiles()
    {
        string[] filesToDelete = new string[]
        {
            "Assets/Hero System/GameDataManager.cs",
            "Assets/Hero System/GameDataManagerSqlite.cs",
            "Assets/Hero System/GameManagerExtension.cs",
            "Assets/Hero System/GameManagerAdapter.cs"
        };

        foreach (string file in filesToDelete)
        {
            if (File.Exists(file))
            {
                File.Delete(file);
                Debug.Log("Deleted " + file);
            }
        }
    }

    private void FixGameManagerPartial()
    {
        string gameManagerPath = "Assets/Game System/GameManager.cs";
        if (File.Exists(gameManagerPath))
        {
            string content = File.ReadAllText(gameManagerPath);
            
            // Add partial modifier if it doesn't exist
            if (!content.Contains("public partial class GameManager"))
            {
                content = content.Replace("public class GameManager", "public partial class GameManager");
                File.WriteAllText(gameManagerPath, content);
                Debug.Log("Added partial modifier to GameManager class");
            }
            else
            {
                Debug.Log("GameManager already has partial modifier");
            }
        }
        else
        {
            Debug.LogError("GameManager.cs not found at " + gameManagerPath);
        }
    }

    private void AttachAdapter()
    {
        if (gameManagerObject.GetComponent<SimpleGameManagerAdapter>() == null)
        {
            gameManagerObject.AddComponent<SimpleGameManagerAdapter>();
            Debug.Log("Added SimpleGameManagerAdapter to " + gameManagerObject.name);
        }
        else
        {
            Debug.Log("SimpleGameManagerAdapter already attached to " + gameManagerObject.name);
        }
    }

    private void FixHeroDataConflict()
    {
        string heroDataPath = "Assets/Hero System/HeroData.cs";
        if (File.Exists(heroDataPath))
        {
            string content = File.ReadAllText(heroDataPath);
            
            // Check if the class is already in a namespace
            if (!Regex.IsMatch(content, @"namespace\s+\w+\s*{"))
            {
                // Find the class declaration
                Match match = Regex.Match(content, @"public\s+class\s+HeroData");
                if (match.Success)
                {
                    // Find the position to insert the namespace
                    int insertPos = content.Substring(0, match.Index).LastIndexOf("using");
                    if (insertPos >= 0)
                    {
                        insertPos = content.IndexOf('\n', insertPos) + 1;
                        
                        // Add an empty line after the last using statement
                        if (content[insertPos] != '\n')
                        {
                            content = content.Insert(insertPos, "\n");
                            insertPos++;
                        }
                        
                        // Add namespace declaration
                        content = content.Insert(insertPos, "namespace HeroSystem\n{\n");
                        
                        // Add closing brace at the end
                        content += "\n}";
                        
                        File.WriteAllText(heroDataPath, content);
                        Debug.Log("Added HeroSystem namespace to HeroData class");
                    }
                    else
                    {
                        Debug.LogError("Could not find a suitable position to insert namespace");
                    }
                }
                else
                {
                    Debug.LogError("Could not find HeroData class declaration");
                }
            }
            else
            {
                Debug.Log("HeroData already in a namespace");
            }
        }
        else
        {
            Debug.LogError("HeroData.cs not found at " + heroDataPath);
        }
    }
}
