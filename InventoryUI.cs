using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Linq;
using System.Collections.Generic;

public class InventoryUI : MonoBehaviour
{
    public static InventoryUI Instance;
    private UIElementAnimator uiElementAnimator;

    [SerializeField] private Button resourcesButton;
    [SerializeField] private Button speedUpButton;
    [SerializeField] private Button buffButton;
    [SerializeField] private Button materialsButton;
    [SerializeField] private Button otherButton;
    [SerializeField] private Transform itemsContainer;
    [SerializeField] private GameObject itemPrefab;
    [SerializeField] private GameObject inventoryUI; // Reference to the Hero Level Up UI
    [SerializeField] private Button closeInventoryUIButton; // Button to close the Inventory UI
    [SerializeField] private ItemSelector itemSelector; // Reference to the ItemSelector

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        resourcesButton.GetComponent<Button>().onClick.RemoveAllListeners();
        resourcesButton.GetComponent<Button>().onClick.AddListener(OnResourcesButtonClick);
        
        speedUpButton.onClick.RemoveAllListeners();
        speedUpButton.onClick.AddListener(OnSpeedUpButtonClick);
        
        buffButton.onClick.RemoveAllListeners();
        buffButton.onClick.AddListener(OnBuffButtonClick);
        
        materialsButton.onClick.RemoveAllListeners();
        materialsButton.onClick.AddListener(OnMaterialsButtonClick);
        
        otherButton.onClick.RemoveAllListeners();
        otherButton.onClick.AddListener(OnOtherButtonClick);
        
        DisplayItems(ItemCategory.Resources);

        // Display the default category (e.g., Resources)
        DisplayItems(ItemCategory.Resources);

        closeInventoryUIButton.GetComponent<Button>().onClick.RemoveAllListeners();
        closeInventoryUIButton.GetComponent<Button>().onClick.AddListener(CloseInventoryUI);

        uiElementAnimator = inventoryUI.GetComponent<UIElementAnimator>();
    }

    private void OnEnable()
    {
        Debug.Log("InventoryUI enabled");
        resourcesButton.onClick.AddListener(() => Debug.Log("Resources direct click"));
        speedUpButton.onClick.AddListener(() => Debug.Log("Speed Up direct click"));
        otherButton.onClick.AddListener(() => Debug.Log("Other direct click"));
        // Add similar listeners for other buttons
    }

    public void DisplayItems(ItemCategory category)
    {
        if (itemsContainer == null)
        {
            Debug.LogError("Items Container is not assigned in the Inspector.");
            return;
        }

        if (itemPrefab == null)
        {
            Debug.LogError("Item Prefab is not assigned in the Inspector.");
            return;
        }

        // Clear existing items
        foreach (Transform child in itemsContainer)
        {
            Destroy(child.gameObject);
        }

        // Get items of the selected category
        List<InventoryItem> items = InventorySystem.Instance?.GetItemsByCategory(category);

        if (items == null)
        {
            Debug.LogError("InventorySystem.Instance.GetItemsByCategory returned null.");
            return;
        }

        // Filter out items with quantity 0
        items = items.Where(item => item.quantity > 0).ToList();

        Debug.Log($"Displaying {items.Count} items of category {category}");

        // Instantiate item UI elements
        foreach (InventoryItem item in items)
        {
            GameObject itemGO = Instantiate(itemPrefab, itemsContainer);
            itemGO.GetComponent<ItemUI>().Initialize(item, itemSelector);
        }
    }

    public void CloseInventoryUI()
    {
        if (uiElementAnimator != null)
        {
            uiElementAnimator.Close();
        }
        else
        {
            inventoryUI.SetActive(false);
        }
    }

    private void OnResourcesButtonClick() 
    {
        Debug.Log("Resources clicked");
        DisplayItems(ItemCategory.Resources);
    }

    private void OnSpeedUpButtonClick() 
    {
        Debug.Log("SpeedUp clicked");
        DisplayItems(ItemCategory.SpeedUp);
    }

    private void OnBuffButtonClick() 
    {
        Debug.Log("Buff clicked");
        DisplayItems(ItemCategory.Battle);
    }

    private void OnMaterialsButtonClick() 
    {
        Debug.Log("Materials clicked");
        DisplayItems(ItemCategory.Hero);
    }

    private void OnOtherButtonClick() 
    {
        Debug.Log("Other clicked");
        DisplayItems(ItemCategory.Other);
    }
}
