using UnityEngine;
using UnityEngine.UI;

public class UILampSequencer : MonoBehaviour
{
    public Image[] lamps;                  // Drag your 5 lamp UI images here
    public float interval = 0.3f;          // Time between each lamp switching
    public Color onColor = Color.green;    // Illuminated color
    public Color offColor = Color.black;   // Off/dimmed color
    public bool pingPong = false;          // If true, lights bounce back and forth

    private int currentIndex = 0;
    private float timer = 0f;
    private int direction = 1;

    void Start()
    {
        UpdateLampColors();
    }

    void Update()
    {
        timer += Time.deltaTime;

        if (timer >= interval)
        {
            timer = 0f;

            // Move to next lamp
            currentIndex += direction;

            if (pingPong)
            {
                // Reverse direction at ends
                if (currentIndex >= lamps.Length || currentIndex < 0)
                {
                    direction *= -1;
                    currentIndex += direction * 2; // Bounce back
                }
            }
            else
            {
                // Loop back to start
                if (currentIndex >= lamps.Length)
                    currentIndex = 0;
            }

            UpdateLampColors();
        }
    }

    void UpdateLampColors()
    {
        for (int i = 0; i < lamps.Length; i++)
        {
            if (lamps[i] != null)
            {
                lamps[i].color = (i == currentIndex) ? onColor : offColor;

                var outline = lamps[i].GetComponent<Outline>();
                if (outline != null)
                    outline.enabled = (i == currentIndex);
            }
        }
    }

}
