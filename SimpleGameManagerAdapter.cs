using UnityEngine;
using System.Collections.Generic;
using System.Reflection;

/// <summary>
/// A simple adapter that connects the GameManager to the SimpleGameDataManager.
/// </summary>
public class SimpleGameManagerAdapter : MonoBehaviour
{
    private SimpleGameDataManager dataManager;
    private MonoBehaviour gameManager;

    private void Awake()
    {
        // Get reference to GameManager
        gameManager = GetComponent<MonoBehaviour>();
        if (gameManager == null || gameManager.GetType().Name != "GameManager")
        {
            Debug.LogError("SimpleGameManagerAdapter must be attached to the same GameObject as GameManager");
            return;
        }

        // Initialize SimpleGameDataManager
        dataManager = SimpleGameDataManager.Instance;

        // Load resources from PlayerPrefs
        LoadResourcesFromPlayerPrefs();
    }

    #region Resource Management

    // Load resources from PlayerPrefs
    private void LoadResourcesFromPlayerPrefs()
    {
        dataManager.GetResources(out int dbGold, out int dbFood, out int dbWood, out int dbMetal, out int dbBattlePower, out int dbWelfare);

        // Use reflection to set private fields in GameManager
        var goldField = gameManager.GetType().GetField("gold", BindingFlags.Instance | BindingFlags.NonPublic);
        var foodField = gameManager.GetType().GetField("food", BindingFlags.Instance | BindingFlags.NonPublic);
        var woodField = gameManager.GetType().GetField("wood", BindingFlags.Instance | BindingFlags.NonPublic);
        var metalField = gameManager.GetType().GetField("metal", BindingFlags.Instance | BindingFlags.NonPublic);
        var bpField = gameManager.GetType().GetField("totalBattlePower", BindingFlags.Instance | BindingFlags.NonPublic);
        var welfareField = gameManager.GetType().GetField("totalWelfare", BindingFlags.Instance | BindingFlags.NonPublic);

        if (goldField != null) goldField.SetValue(gameManager, dbGold);
        if (foodField != null) foodField.SetValue(gameManager, dbFood);
        if (woodField != null) woodField.SetValue(gameManager, dbWood);
        if (metalField != null) metalField.SetValue(gameManager, dbMetal);
        if (bpField != null) bpField.SetValue(gameManager, dbBattlePower);
        if (welfareField != null) welfareField.SetValue(gameManager, dbWelfare);

        // Call UpdateResourceUI method
        var updateUIMethod = gameManager.GetType().GetMethod("UpdateResourceUI", BindingFlags.Instance | BindingFlags.NonPublic);
        if (updateUIMethod != null)
            updateUIMethod.Invoke(gameManager, null);
    }

    // Save resources to PlayerPrefs
    public void SaveResourcesToPlayerPrefs()
    {
        // Get resources from GameManager using reflection
        var goldProperty = gameManager.GetType().GetProperty("Gold");
        var foodProperty = gameManager.GetType().GetProperty("Food");
        var woodProperty = gameManager.GetType().GetProperty("Wood");
        var metalProperty = gameManager.GetType().GetProperty("Metal");

        int gold = (int)goldProperty.GetValue(gameManager);
        int food = (int)foodProperty.GetValue(gameManager);
        int wood = (int)woodProperty.GetValue(gameManager);
        int metal = (int)metalProperty.GetValue(gameManager);

        // Get battle power and welfare using reflection
        var bpField = gameManager.GetType().GetField("totalBattlePower", BindingFlags.Instance | BindingFlags.NonPublic);
        var welfareField = gameManager.GetType().GetField("totalWelfare", BindingFlags.Instance | BindingFlags.NonPublic);

        int battlePower = (int)bpField.GetValue(gameManager);
        int welfare = (int)welfareField.GetValue(gameManager);

        dataManager.UpdateResources(
            gold,
            food,
            wood,
            metal,
            battlePower,
            welfare
        );
    }

    #endregion

    #region Event Handlers

    // These methods can be called from GameManager's methods to save data

    // Call this from GameManager's AddGold method
    public void OnAddGold(int amount)
    {
        SaveResourcesToPlayerPrefs();
    }

    // Call this from GameManager's SpendGold method
    public void OnSpendGold(int amount)
    {
        SaveResourcesToPlayerPrefs();
    }

    // Call this from GameManager's AddResources method
    public void OnAddResources(int foodAmount, int woodAmount, int metalAmount)
    {
        SaveResourcesToPlayerPrefs();
    }

    // Call this from GameManager's SpendResources method
    public void OnSpendResources(int foodCost, int woodCost, int metalCost)
    {
        SaveResourcesToPlayerPrefs();
    }

    // Call this from GameManager's AddBattlePower method
    public void OnAddBattlePower(int amount, bool isFromResearch = false)
    {
        SaveResourcesToPlayerPrefs();
    }

    // Call this from GameManager's AddWelfare method
    public void OnAddWelfare(int amount)
    {
        SaveResourcesToPlayerPrefs();
    }

    #endregion
}
