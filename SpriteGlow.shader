Shader "Custom/SpriteGlow"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _GlowColor ("Glow Color", Color) = (1,1,1,0.5) // Reduced alpha for subtlety
        _GlowIntensity ("Glow Intensity", Range(0, 1)) = 0.5 // Reduced range
        _OuterGlowWidth ("Outer Glow Width", Range(0.1, 2)) = 0.8 // Reduced width range
    }
    
    SubShader
    {
        Tags { "Queue"="Transparent" "RenderType"="Transparent" }
        Blend SrcAlpha OneMinusSrcAlpha
        
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };
            
            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };
            
            sampler2D _MainTex;
            float4 _GlowColor;
            float _GlowIntensity;
            float _OuterGlowWidth;
            
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv);
                
                // Calculate distance from sprite edge
                float alpha = col.a;
                float2 spriteUV = i.uv - 0.5;
                float distance = (1 - alpha) * length(spriteUV);
                
                // Create outer glow only
                float outerGlow = exp(-distance * _OuterGlowWidth);
                float glowAlpha = outerGlow * (1 - alpha) * _GlowIntensity;
                
                // Combine sprite and glow
                return fixed4(col.rgb, col.a) + _GlowColor * glowAlpha;
            }
            ENDCG
        }
    }
}