using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(GameManager))]
public class GameManagerEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        GameManager gameManager = (GameManager)target;

        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("Debug Tools", EditorStyles.boldLabel);

        if (GUILayout.Button("Reset All Progress"))
        {
            if (EditorUtility.DisplayDialog("Reset Progress",
                "Are you sure you want to reset all progress? This cannot be undone.",
                "Yes, Reset", "Cancel"))
            {
                gameManager.ResetAllProgress();
            }
        }

        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("Balance System", EditorStyles.boldLabel);

        // Check if balance data is assigned
        if (gameManager.balanceData == null)
        {
            EditorGUILayout.HelpBox("No balance data assigned. Please assign a GameBalanceData asset.", MessageType.Warning);

            if (GUILayout.<PERSON><PERSON>("Create New Balance Data"))
            {
                // Create a new GameBalanceData asset
                GameBalanceData newData = ScriptableObject.CreateInstance<GameBalanceData>();

                string path = EditorUtility.SaveFilePanelInProject(
                    "Save Game Balance Data",
                    "GameBalanceData",
                    "asset",
                    "Save game balance data as"
                );

                if (!string.IsNullOrEmpty(path))
                {
                    AssetDatabase.CreateAsset(newData, path);
                    AssetDatabase.SaveAssets();

                    // Assign the new asset to the GameManager
                    gameManager.balanceData = newData;
                    EditorUtility.SetDirty(gameManager);
                }
            }
        }
        else
        {
            // Show balance data controls
            if (GUILayout.Button("Update Balance Data from Game"))
            {
                gameManager.UpdateBalanceData();
            }

            if (GUILayout.Button("Apply Balance Data to Game"))
            {
                if (EditorUtility.DisplayDialog("Apply Balance Data",
                    "Are you sure you want to apply the balance data to the game? This will overwrite current game values.",
                    "Yes, Apply", "Cancel"))
                {
                    // Call the ApplyBalanceData method
                    gameManager.ApplyBalanceData();
                }
            }

            // Add button to open the balance data asset
            if (GUILayout.Button("Edit Balance Data"))
            {
                Selection.activeObject = gameManager.balanceData;
            }
        }

        // Add debug information
        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("Debug Info", EditorStyles.boldLabel);
        if (gameManager.heroProgressData != null)
        {
            EditorGUILayout.LabelField($"Heroes with saved progress: {gameManager.heroProgressData.Count}");

            // Display detailed hero progress
            foreach (var heroProgress in gameManager.heroProgressData)
            {
                EditorGUILayout.LabelField($"Hero: {heroProgress.Key}");
                EditorGUI.indentLevel++;
                foreach (var skill in heroProgress.Value.skillProgresses)
                {
                    EditorGUILayout.LabelField($"Skill: {skill.skillName}, Level: {skill.level}");
                }
                EditorGUI.indentLevel--;
            }
        }
    }
}