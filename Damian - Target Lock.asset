%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af3965b754ff7fc42b77202b337a77ff, type: 3}
  m_Name: Damian - Target Lock
  m_EditorClassIdentifier: 
  skillName: Target Lock
  skillIcon: {fileID: 21300000, guid: 8bb71d635fc7cfa43a853b6c03bbad94, type: 3}
  description: 
  secondaryDescription: 
  initialLevel: 0
  attackBonus: 2
  damageBonus: 11
  defenseBonus: 0
  healthBonus: 0
  marchCapacityBonus: 0
  rallyCapacityBonus: 0
  skillEffects:
  - effectType: 4
    startTurn: 10
    turns: 2
    effectChance: 0
    effectValue: 29
    targetType: 0
