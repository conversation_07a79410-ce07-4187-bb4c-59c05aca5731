using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class ItemDetailsUI : MonoBehaviour
{
    [SerializeField] private GameObject detailsPanel;
    [SerializeField] private Image itemIcon;
    [SerializeField] private TextMesh<PERSON><PERSON><PERSON><PERSON><PERSON> itemName;
    [SerializeField] private TextMesh<PERSON><PERSON><PERSON><PERSON><PERSON> itemQuantity;
    [SerializeField] private TextMeshProUGUI itemDescription;
    [SerializeField] private Slider quantitySlider;
    [SerializeField] private TMP_InputField quantityInputField;

    private InventoryItem currentItem;

    private void Start()
    {
        detailsPanel.SetActive(false); // Hide the details panel by default
    }

    public void ShowItemDetails(InventoryItem item)
    {
        currentItem = item;

        if (itemIcon != null)
            itemIcon.sprite = item.itemSO.Icon;
        if (itemName != null)
            itemName.text = item.itemSO.Name;
        if (itemQuantity != null)
            itemQuantity.text = "Owned: " + item.quantity.ToString();
        if (itemDescription != null)
            itemDescription.text = item.itemSO.Description;

        if (quantitySlider != null)
        {
            quantitySlider.minValue = 1;
            quantitySlider.maxValue = item.quantity;
            quantitySlider.value = 1;
            quantitySlider.onValueChanged.AddListener(OnSliderValueChanged);
        }

        if (quantityInputField != null)
        {
            quantityInputField.text = "1";
            quantityInputField.onValueChanged.AddListener(OnInputFieldValueChanged);
        }

        detailsPanel.SetActive(true); // Show the details panel
    }

    public void HideItemDetails()
    {
        detailsPanel.SetActive(false); // Hide the details panel
    }

    private void OnSliderValueChanged(float value)
    {
        if (quantityInputField != null)
        {
            quantityInputField.text = value.ToString();
        }
    }

    private void OnInputFieldValueChanged(string value)
    {
        if (quantitySlider != null)
        {
            if (int.TryParse(value, out int intValue))
            {
                quantitySlider.value = Mathf.Clamp(intValue, (int)quantitySlider.minValue, (int)quantitySlider.maxValue);
            }
        }
    }
}
