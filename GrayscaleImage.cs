using UnityEngine;
using UnityEngine.UI;

public class GrayscaleImage : MonoBehaviour
{
    private Material materialInstance;
    private Image image;

    private void Awake()
    {
        image = GetComponent<Image>();

        // Ensure each image has its own unique material instance
        materialInstance = new Material(image.material);
        image.material = materialInstance;
    }

    public void SetGrayscaleAmount(float amount)
    {
        if (materialInstance != null)
        {
            materialInstance.SetFloat("_GrayscaleAmount", amount);
        }
    }
}
