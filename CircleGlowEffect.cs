using UnityEngine;
using UnityEngine.UI;

public class CircleGlowEffect : MonoBehaviour
{
    [SerializeField] private float glowSpeed = 1f;
    [SerializeField] private float minGlow = 0.5f;
    [SerializeField] private float maxGlow = 1.5f;
    [SerializeField] private Color glowColor = Color.white;
    
    private Material material;
    private float currentGlow;
    private bool increasing = true;

    void Start()
    {
        Image image = GetComponent<Image>();
        material = new Material(Shader.Find("Custom/CircleGlow"));
        image.material = material;
        
        material.SetColor("_GlowColor", glowColor);
        currentGlow = minGlow;
    }

    void Update()
    {
        if (increasing)
        {
            currentGlow += Time.deltaTime * glowSpeed;
            if (currentGlow >= maxGlow) increasing = false;
        }
        else
        {
            currentGlow -= Time.deltaTime * glowSpeed;
            if (currentGlow <= minGlow) increasing = true;
        }
        
        material.SetFloat("_GlowRadius", currentGlow);
    }
}