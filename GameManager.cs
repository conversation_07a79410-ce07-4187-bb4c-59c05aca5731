using UnityEngine;
using TMPro;
using static UpgradeManager;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.SceneManagement;
using UnityEngine.Serialization;
using HeroSystem;

public partial class GameManager : MonoBehaviour
{
    private static GameManager _instance;
    public static GameManager Instance => _instance;

    [Header("Balance System")]
    public GameBalanceData balanceData;

    private HeroData selectedHero;

    private Dictionary<string, Player> players = new Dictionary<string, Player>();

    [Header("Inventory Items")]
    public InventoryItemSO[] initialItems;

    private int gold = 1000; // Starting gold (adjust as needed)
    private int totalBattlePower = 0;
    private int totalWelfare = 0;
    private int food = 100000;
    private int wood = 100000;
    private int metal = 100000;

    [Header("UI Elements")]
    public TextMeshProUGUI foodText;
    public TextMeshProUGUI woodText;
    public TextMeshProUGUI metalText;
    public TextMeshProUGUI goldText;
    public TextMeshProUGUI bpText;
    public TextMeshProUGUI welfareText;

    private HeroSelector heroSelector;

    #if UNITY_EDITOR
    public Dictionary<string, HeroProgress> heroProgressData = new Dictionary<string, HeroProgress>();
    #else
    private Dictionary<string, HeroProgress> heroProgressData = new Dictionary<string, HeroProgress>();
    #endif
    private Dictionary<string, ResearchProgress> activeResearch = new Dictionary<string, ResearchProgress>();
    private Dictionary<string, ResearchSave> completedResearch = new Dictionary<string, ResearchSave>();
    private ResearchNode FindResearchNode(ResearchNode.NodeType nodeType, ResearchNode.BonusType bonusType)
    {
        ResearchNode[] allNodes = UnityEngine.Object.FindObjectsByType<ResearchNode>(FindObjectsSortMode.None);
        return allNodes.FirstOrDefault(n => n.nodeType == nodeType && n.bonusType == bonusType);
    }

    private void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            LoadResearchData();

            // Initialize balance system if balance data is assigned
            if (balanceData != null)
            {
                ApplyBalanceData();
            }
        }
        else
        {
            Destroy(gameObject);
        }
    }

    // Apply balance data to game elements
    public void ApplyBalanceData()
    {
        Debug.Log("Applying balance data to game elements...");

        // Apply global multipliers
        ApplyGlobalMultipliers();

        // Apply hero balance data
        ApplyHeroBalanceData();

        // Apply building balance data
        ApplyBuildingBalanceData();

        // Apply research balance data
        ApplyResearchBalanceData();

        Debug.Log("Balance data applied successfully!");
    }

    // Apply global multipliers
    private void ApplyGlobalMultipliers()
    {
        // These multipliers can be used throughout the game to scale various aspects
        // For example, resource generation, time requirements, power calculations, etc.
        Debug.Log($"Applied global multipliers: Resource={balanceData.globalResourceMultiplier}, Time={balanceData.globalTimeMultiplier}, Power={balanceData.globalPowerMultiplier}");
    }

    // Apply hero balance data
    private void ApplyHeroBalanceData()
    {
        if (balanceData.heroBalanceData.Count == 0) return;

        // Find all heroes in the game
        HeroData[] allHeroes = Resources.FindObjectsOfTypeAll<HeroData>();

        foreach (var hero in allHeroes)
        {
            // Find matching balance data
            var heroBalance = balanceData.heroBalanceData.Find(h => h.heroName == hero.HeroName);

            if (heroBalance != null)
            {
                // Apply balance data to hero
                hero.InitialPower = heroBalance.initialPower;

                // Apply base stats
                hero.BaseStats.Attack = heroBalance.baseStats.Attack;
                hero.BaseStats.Defense = heroBalance.baseStats.Defense;
                hero.BaseStats.HP = heroBalance.baseStats.HP;
                hero.BaseStats.MarchCapacity = heroBalance.baseStats.MarchCapacity;

                // Apply troop modifiers
                hero.TroopModifiers.AttackBonus = heroBalance.troopModifiers.AttackBonus;
                hero.TroopModifiers.DefenseBonus = heroBalance.troopModifiers.DefenseBonus;
                hero.TroopModifiers.HpBonus = heroBalance.troopModifiers.HpBonus;

                Debug.Log($"Applied balance data to hero: {hero.HeroName}");
            }
        }
    }

    // Apply building balance data
    private void ApplyBuildingBalanceData()
    {
        if (balanceData.buildingBalanceData.Count == 0) return;

        // Find all building prefabs or instances
        BuildingUpgrade[] allBuildings = Resources.FindObjectsOfTypeAll<BuildingUpgrade>();

        foreach (var building in allBuildings)
        {
            // Find matching balance data
            var buildingBalance = balanceData.buildingBalanceData.Find(b => b.buildingName == building.name);

            if (buildingBalance != null)
            {
                // Apply balance data to building
                building.maxLevel = buildingBalance.maxLevel;

                // Apply base costs
                building.baseFoodCost = buildingBalance.baseFoodCost;
                building.baseWoodCost = buildingBalance.baseWoodCost;
                building.baseMetalCost = buildingBalance.baseMetalCost;
                building.baseGoldCost = buildingBalance.baseGoldCost;

                // Apply base values
                building.baseUpgradeTime = buildingBalance.baseUpgradeTime;
                building.baseBattlePower = buildingBalance.baseBattlePower;
                building.baseWelfare = buildingBalance.baseWelfare;
                building.baseResourceGeneration = buildingBalance.baseResourceGeneration;

                // Apply scaling factors
                building.resourceGrowthFactor = buildingBalance.resourceGrowthFactor;
                building.timeGrowthFactor = buildingBalance.timeGrowthFactor;
                building.battlePowerGrowthFactor = buildingBalance.battlePowerGrowthFactor;
                building.welfareGrowthFactor = buildingBalance.welfareGrowthFactor;
                building.resourceGenerationGrowthFactor = buildingBalance.resourceGenerationGrowthFactor;

                Debug.Log($"Applied balance data to building: {building.name}");
            }
        }
    }

    // Apply research balance data
    private void ApplyResearchBalanceData()
    {
        if (balanceData.researchBalanceData.Count == 0) return;

        // Find all research nodes
        ResearchNode[] allResearch = Resources.FindObjectsOfTypeAll<ResearchNode>();

        Debug.Log($"Found {allResearch.Length} research nodes to apply balance data to.");

        foreach (var research in allResearch)
        {
            // Find matching balance data
            var researchBalance = balanceData.researchBalanceData.Find(r =>
                r.nodeType == research.nodeType && r.bonusType == research.bonusType);

            if (researchBalance != null)
            {
                // Apply balance data to research
                research.isPercentageBonus = researchBalance.isPercentageBonus;

                // Apply base costs
                research.startingFoodCost = researchBalance.startingFoodCost;
                research.startingWoodCost = researchBalance.startingWoodCost;
                research.startingMetalCost = researchBalance.startingMetalCost;
                research.startingGoldCost = researchBalance.startingGoldCost;

                // Apply base values
                research.startingTime = researchBalance.startingTime;
                research.startingPower = researchBalance.startingPower;

                // Apply tier data if available
                if (researchBalance.tiers.Count > 0 && research.tiers != null)
                {
                    // Match tiers by tier number
                    foreach (var balanceTier in researchBalance.tiers)
                    {
                        var matchingTier = research.tiers.Find(t => t.tierNumber == balanceTier.tierNumber);
                        if (matchingTier != null)
                        {
                            matchingTier.maxLevel = balanceTier.maxLevel;
                            matchingTier.bonus = balanceTier.bonus;
                            matchingTier.requiredLabLevel = balanceTier.requiredLabLevel;
                        }
                    }
                }

                Debug.Log($"Applied balance data to research: {research.nodeType}_{research.bonusType}");
            }
            else
            {
                Debug.LogWarning($"No balance data found for research: {research.nodeType}_{research.bonusType}");
            }
        }
    }

    private void Start()
    {
        heroSelector = UnityEngine.Object.FindFirstObjectByType<HeroSelector>();

        if (heroSelector == null)
        {
            Debug.LogError("HeroSelector not found!");
            return;
        }

        UpdateResourceUI(); // Ensure UI updates at the beginning

        // Add initial items to the inventory with random quantities
        foreach (var itemSO in initialItems)
        {
            int randomQuantity = UnityEngine.Random.Range(1, 100); // Random quantity between 1-99
            InventorySystem.Instance.AddItem(itemSO, randomQuantity);
        }

        // Delay the calculation to ensure HeroSelector is populated
        Invoke("CalculateTotalBattlePower", 1f); // Delay by 1 second

        // Subscribe to hero power change events
        Invoke("SubscribeToHeroPowerChanges", 1.1f); // Delay to ensure heroes are populated

        // Initialize hero progress
        if (heroSelector != null)
        {
            HeroData[] allHeroes = heroSelector.GetAllHeroes();
            if (allHeroes != null)
            {
                foreach (HeroData hero in allHeroes)
                {
                    if (hero != null)
                    {
                        UpdateHeroProgress(hero);
                    }
                }
            }
        }

        LoadFPSProgress();
    }

    public HeroData SelectedHero
    {
        get => selectedHero;
        set
        {
            selectedHero = value;
            // Additional logic when hero is selected
        }
    }

    public void SetSelectedHero(HeroData hero)
    {
        SelectedHero = hero;
    }

    private void SubscribeToHeroPowerChanges()
    {
        if (heroSelector != null)
        {
            HeroData[] allHeroes = heroSelector.GetAllHeroes();
            if (allHeroes != null)
            {
                foreach (HeroData hero in allHeroes)
                {
                    if (hero != null)
                    {
                        hero.OnHeroPowerChanged += CalculateTotalBattlePower;
                    }
                }
            }
            else
            {
                Debug.LogWarning("No heroes found in HeroSelector!");
            }
        }
        else
        {
            Debug.LogError("HeroSelector is null!");
        }
    }

    public void AddPlayer(string playerId, Player player)
    {
        players[playerId] = player;
    }

    public Player GetPlayer(string playerId)
    {
        return players.ContainsKey(playerId) ? players[playerId] : null;
    }

    // Getter properties for resources
    public int Food => food;
    public int Wood => wood;
    public int Metal => metal;
    public int Gold => gold;

    public void AddBattlePower(int amount, bool isFromResearch = false)
    {
        totalBattlePower += amount;
        UpdateResourceUI();
        Debug.Log($"Total Battle Power: {totalBattlePower}");

        if (isFromResearch)
        {
            SaveResearchData();
        }
    }

    public void AddWelfare(int amount)
    {
        totalWelfare += amount;
        UpdateResourceUI();
        Debug.Log($"Total Welfare: {totalWelfare}");
    }

    #region Building Management
    // Dictionary to store building data by building ID (using GameObject.GetInstanceID() as key)
    private Dictionary<int, BuildingData> buildingDataDict = new Dictionary<int, BuildingData>();

    // Class to store building data
    [System.Serializable]
    public class BuildingData
    {
        public int buildingId;
        public string buildingName;
        public int level = 1;
        public Dictionary<BuildingCapabilityType, float> capabilities = new Dictionary<BuildingCapabilityType, float>();

        public BuildingData(int id, string name)
        {
            buildingId = id;
            buildingName = name;
            level = 1;
        }
    }

    // Register a building with the GameManager
    public void RegisterBuilding(GameObject building)
    {
        int buildingId = building.GetInstanceID();

        // Check if building is already registered
        if (buildingDataDict.ContainsKey(buildingId))
        {
            Debug.Log($"Building {building.name} (ID: {buildingId}) is already registered.");
            return;
        }

        // Create new building data
        BuildingData buildingData = new BuildingData(buildingId, building.name);

        // Get building level from BuildingUpgrade component if available
        BuildingUpgrade buildingUpgrade = building.GetComponent<BuildingUpgrade>();
        if (buildingUpgrade != null)
        {
            buildingData.level = buildingUpgrade.CurrentLevel;
        }

        // Initialize capabilities based on building type
        InitializeBuildingCapabilities(building, buildingData);

        // Add to dictionary
        buildingDataDict.Add(buildingId, buildingData);

        Debug.Log($"Registered building {building.name} (ID: {buildingId}) with GameManager.");
    }

    // Initialize building capabilities based on building type
    private void InitializeBuildingCapabilities(GameObject building, BuildingData buildingData)
    {
        // Add default capabilities
        buildingData.capabilities[BuildingCapabilityType.BattlePower] = 10;
        buildingData.capabilities[BuildingCapabilityType.Welfare] = 5;

        // Add specific capabilities based on building name
        string buildingName = building.name.ToLower();

        if (buildingName.Contains("farm"))
        {
            buildingData.capabilities[BuildingCapabilityType.ResourceProduction_Food] = 100 * buildingData.level;
        }
        else if (buildingName.Contains("lumber") || buildingName.Contains("wood"))
        {
            buildingData.capabilities[BuildingCapabilityType.ResourceProduction_Wood] = 100 * buildingData.level;
        }
        else if (buildingName.Contains("quarry") || buildingName.Contains("metal"))
        {
            buildingData.capabilities[BuildingCapabilityType.ResourceProduction_Metal] = 100 * buildingData.level;
        }
        else if (buildingName.Contains("power") || buildingName.Contains("energy"))
        {
            buildingData.capabilities[BuildingCapabilityType.ResourceProduction_Energy] = 50 * buildingData.level;
            // Debug.Log($"Added Energy Production capability to {building.name}: {50 * buildingData.level}");
        }
        else if (buildingName.Contains("barracks"))
        {
            buildingData.capabilities[BuildingCapabilityType.TrainingCapacity] = 50 * buildingData.level;
            buildingData.capabilities[BuildingCapabilityType.TrainingSpeed] = 10 * buildingData.level;
        }
        else if (buildingName.Contains("lab") || buildingName.Contains("research"))
        {
            buildingData.capabilities[BuildingCapabilityType.ResearchSpeed] = 10 * buildingData.level;
        }
        else if (buildingName.Contains("storage"))
        {
            buildingData.capabilities[BuildingCapabilityType.StorageProtection_Food] = 1000 * buildingData.level;
            buildingData.capabilities[BuildingCapabilityType.StorageProtection_Wood] = 1000 * buildingData.level;
            buildingData.capabilities[BuildingCapabilityType.StorageProtection_Metal] = 1000 * buildingData.level;
        }
        else if (buildingName.Contains("tower"))
        {
            buildingData.capabilities[BuildingCapabilityType.TowerAttack] = 50 * buildingData.level;
        }
        else if (buildingName.Contains("hospital"))
        {
            buildingData.capabilities[BuildingCapabilityType.HospitalCapacity] = 100 * buildingData.level;
        }
    }

    // Update building level
    public void UpdateBuildingLevel(GameObject building, int newLevel)
    {
        int buildingId = building.GetInstanceID();

        // Register building if not already registered
        if (!buildingDataDict.ContainsKey(buildingId))
        {
            RegisterBuilding(building);
        }

        // Update level
        buildingDataDict[buildingId].level = newLevel;

        // Update capabilities based on new level
        UpdateBuildingCapabilities(buildingId);

        // Debug.Log($"Updated building {building.name} (ID: {buildingId}) to level {newLevel}.");
    }

    // Update building capabilities based on level
    private void UpdateBuildingCapabilities(int buildingId)
    {
        if (!buildingDataDict.ContainsKey(buildingId))
        {
            // Debug.LogWarning($"Building with ID {buildingId} not found in GameManager.");
            return;
        }

        BuildingData buildingData = buildingDataDict[buildingId];

        // Scale capabilities based on level
        foreach (var capability in buildingData.capabilities.Keys.ToList())
        {
            // Get base value (level 1 value)
            float baseValue = buildingData.capabilities[capability] / (buildingData.level > 0 ? buildingData.level : 1);

            // Scale based on level
            buildingData.capabilities[capability] = baseValue * buildingData.level;
        }
    }

    // Get building capabilities
    public List<BuildingCapabilityType> GetBuildingCapabilities(GameObject building)
    {
        int buildingId = building.GetInstanceID();

        // Register building if not already registered
        if (!buildingDataDict.ContainsKey(buildingId))
        {
            RegisterBuilding(building);
        }

        return buildingDataDict[buildingId].capabilities.Keys.ToList();
    }

    // Get building capability value
    public float GetBuildingCapabilityValue(GameObject building, BuildingCapabilityType capabilityType)
    {
        int buildingId = building.GetInstanceID();

        // Register building if not already registered
        if (!buildingDataDict.ContainsKey(buildingId))
        {
            RegisterBuilding(building);
        }

        if (buildingDataDict[buildingId].capabilities.ContainsKey(capabilityType))
        {
            return buildingDataDict[buildingId].capabilities[capabilityType];
        }

        return 0;
    }

    // Set building capability value
    public void SetBuildingCapabilityValue(GameObject building, BuildingCapabilityType capabilityType, float value)
    {
        int buildingId = building.GetInstanceID();

        // Register building if not already registered
        if (!buildingDataDict.ContainsKey(buildingId))
        {
            RegisterBuilding(building);
        }

        buildingDataDict[buildingId].capabilities[capabilityType] = value;
    }
    #endregion

    #region Resource Management
    public bool HasEnoughFood(int amount)
    {
        return food >= amount;
    }

    public bool HasEnoughWood(int amount)
    {
        return wood >= amount;
    }

    public bool HasEnoughMetal(int amount)
    {
        return metal >= amount;
    }

    public bool HasEnoughResources(int foodCost, int woodCost, int metalCost)
    {
        bool canAfford = (Food >= foodCost && Wood >= woodCost && Metal >= metalCost);

        // Debug.Log($"HasEnoughResources Check: Food({Food} / {foodCost}), Wood({Wood} / {woodCost}), Metal({Metal} / {metalCost}) => {canAfford}");

        return canAfford;
    }

    public void SpendResources(int foodCost, int woodCost, int metalCost)
    {
        if (HasEnoughResources(foodCost, woodCost, metalCost))
        {
            food -= foodCost;
            wood -= woodCost;
            metal -= metalCost;
            UpdateResourceUI();
            Debug.Log($"Spent: {foodCost} Food, {woodCost} Wood, {metalCost} Metal");
        }
        else
        {
            Debug.Log("Not enough resources!");
        }
    }

    public void AddResources(int foodAmount, int woodAmount, int metalAmount)
    {
        food += foodAmount;
        wood += woodAmount;
        metal += metalAmount;
        UpdateResourceUI();
        // Debug.Log($"Gained: {foodAmount} Food, {woodAmount} Wood, {metalAmount} Metal");
    }

    public bool HasEnoughGold(int amount)
    {
        return gold >= amount;
    }

    public void SpendGold(int amount)
    {
        if (HasEnoughGold(amount))
        {
            gold -= amount;
            UpdateResourceUI();
            Debug.Log($"Spent {amount} Gold. Remaining: {gold}");
        }
        else
        {
            Debug.Log("Not enough Gold!");
        }
    }

    public void AddGold(int amount)
    {
        gold += amount;
        UpdateResourceUI();
        Debug.Log($"Gained {amount} Gold. Total: {gold}");
    }
    #endregion

    public void UpdateResourceUI()
    {
        if (foodText) foodText.text = $"{NumberFormatter.FormatLargeNumber(food)}";
        if (woodText) woodText.text = $"{NumberFormatter.FormatLargeNumber(wood)}";
        if (metalText) metalText.text = $"{NumberFormatter.FormatLargeNumber(metal)}";
        if (goldText) goldText.text = $"{NumberFormatter.FormatLargeNumber(gold)}";
        if (bpText) bpText.text = $"{NumberFormatter.FormatLargeNumber(totalBattlePower)}";
        if (welfareText) welfareText.text = $"{NumberFormatter.FormatLargeNumber(totalWelfare)}";
    }

    #region Research Management
    public void SaveResearchProgress(ResearchNode node, int tierIndex, int currentLevel, float remainingTime)
    {
        string key = GetResearchKey(node, tierIndex);
        activeResearch[key] = new ResearchProgress
        {
            nodeType = node.nodeType,
            bonusType = node.bonusType,
            tierIndex = tierIndex,
            currentLevel = currentLevel,
            remainingTime = remainingTime
        };
        SaveResearchData();
    }

    public void SaveResearchCompletion(ResearchNode node, int tierIndex, int level)
    {
        string key = GetResearchKey(node, tierIndex);
        // Ensure we don't exceed max level
        int maxLevel = node.tiers[tierIndex].maxLevel;
        int finalLevel = Mathf.Min(level, maxLevel);

        completedResearch[key] = new ResearchSave
        {
            nodeType = node.nodeType,
            bonusType = node.bonusType,
            tierIndex = tierIndex,
            level = finalLevel
        };
        SaveResearchData();
    }

    public void ClearResearchProgress(ResearchNode node, int tierIndex)
    {
        string key = GetResearchKey(node, tierIndex);
        if (activeResearch.ContainsKey(key))
        {
            activeResearch.Remove(key);
            SaveResearchData();
        }
    }

    public int GetResearchLevel(ResearchNode node, int tierIndex)
    {
        string key = GetResearchKey(node, tierIndex);
        if (completedResearch.ContainsKey(key))
        {
            int maxLevel = node.tiers[tierIndex].maxLevel;
            return Mathf.Min(completedResearch[key].level, maxLevel);
        }
        return 0;
    }

     private string GetResearchKey(ResearchNode node, int tierIndex)
    {
        return $"{node.nodeType}_{node.bonusType}_{tierIndex}";
    }

    private void SaveResearchData()
    {
        // Implement save logic (PlayerPrefs, JSON, etc.)
    }

    private void LoadResearchData()
    {
        // Implement load logic
    }

    public int GetLabLevel()
    {
        // TODO: Implement actual lab level tracking
        // For now, return a default value of 1
        // Later, this should be connected to your building system
        return 1;
    }
    #endregion


    public void CalculateTotalBattlePower()
    {
        totalBattlePower = 0;

        // Add power from all buildings
        BuildingUpgrade[] buildings = UnityEngine.Object.FindObjectsByType<BuildingUpgrade>(FindObjectsSortMode.None);
        foreach (BuildingUpgrade building in buildings)
        {
            if (building.gameObject.layer == LayerMask.NameToLayer("Buildings")) // Only count actual buildings
            {
                totalBattlePower += building.baseBattlePower;
                // Debug.Log($"Adding building power: {building.gameObject.name} with power {building.baseBattlePower}");
            }
        }

        // Add current power from all heroes
        if (heroSelector != null)
        {
            HeroData[] allHeroes = heroSelector.GetAllHeroes();
            if (allHeroes == null || allHeroes.Length == 0)
            {
                Debug.LogWarning("No heroes found!");
            }
            else
            {
                foreach (HeroData hero in allHeroes)
                {
                    if (hero != null)
                    {
                        // Debug.Log($"Adding hero power: {hero.HeroName} with power {hero.CurrentPower}");
                        totalBattlePower += hero.CurrentPower;
                    }
                }
            }
        }
        else
        {
            Debug.LogError("HeroSelector is null!");
        }

        // Add power from completed research
        foreach (var research in completedResearch)
        {
            // Find the corresponding ResearchNode
            ResearchNode node = FindResearchNode(research.Value.nodeType, research.Value.bonusType);
            if (node != null)
            {
                int researchPower = node.GetPower(research.Value.tierIndex, research.Value.level);
                totalBattlePower += researchPower;
                Debug.Log($"Adding research power: {node.nodeType}_{node.bonusType} Tier {research.Value.tierIndex} Level {research.Value.level} with power {researchPower}");
            }
        }

        UpdateResourceUI();
        // Debug.Log("Total Battle Power: " + totalBattlePower);
    }

    [System.Serializable]
    public class SkillProgress
    {
        public string skillName;
        public int level;
        public float attackBonus;
        public float damageBonus;
        public float defenseBonus;
        public float healthBonus;
        public float marchCapacityBonus;
        public float rallyCapacityBonus;
        public SkillEffect[] activeEffects;
    }

    [System.Serializable]
    public class HeroProgress
    {
        public string heroName;
        public int rank = 1;
        public int expLevel = 1;
        public int currentExp = 0;
        public int currentPts = 0;
        public bool isLocked = true;
        public List<SkillProgress> skillProgresses = new List<SkillProgress>();
        public BaseStats currentStats;
        public TroopModifiers currentModifiers;
    }



    // Method to initialize or update hero progress
    public void UpdateHeroProgress(HeroData hero)
    {
        if (!heroProgressData.ContainsKey(hero.HeroName))
        {
            HeroProgress progress = new HeroProgress
            {
                heroName = hero.HeroName,
                rank = hero.Rank,
                expLevel = hero.ExpLevel,
                currentExp = hero.CurrentExp,
                currentPts = hero.CurrentPts,
                isLocked = hero.IsLocked,
                currentStats = hero.BaseStats,
                currentModifiers = hero.TroopModifiers,
                skillProgresses = new List<SkillProgress>()
            };

            // Initialize all skills at level 1
            for (int i = 0; i < hero.Skills.Length; i++)
            {
                SkillData skill = hero.Skills[i];
                SkillProgress skillProgress = new SkillProgress
                {
                    skillName = skill.skillName,
                    level = 1,  // All skills start at level 1
                    attackBonus = skill.attackBonus,
                    damageBonus = skill.damageBonus,
                    defenseBonus = skill.defenseBonus,
                    healthBonus = skill.healthBonus,
                    marchCapacityBonus = skill.marchCapacityBonus,
                    rallyCapacityBonus = skill.rallyCapacityBonus,
                    activeEffects = skill.skillEffects
                };
                progress.skillProgresses.Add(skillProgress);
            }

            heroProgressData[hero.HeroName] = progress;
            SaveHeroProgress(hero.HeroName, progress);
        }
        else
        {
            HeroProgress progress = heroProgressData[hero.HeroName];
            progress.rank = hero.Rank;
            progress.expLevel = hero.ExpLevel;
            progress.currentExp = hero.CurrentExp;
            progress.currentPts = hero.CurrentPts;
            progress.isLocked = hero.IsLocked;
            progress.currentStats = hero.BaseStats;
            progress.currentModifiers = hero.TroopModifiers;

            // Update skill progress without incrementing levels
            for (int i = 0; i < hero.Skills.Length; i++)
            {
                SkillData skill = hero.Skills[i];
                SkillProgress existingProgress = progress.skillProgresses.Find(sp => sp.skillName == skill.skillName);

                if (existingProgress == null)
                {
                    // If somehow a skill is missing, add it at level 1
                    existingProgress = new SkillProgress
                    {
                        skillName = skill.skillName,
                        level = 1
                    };
                    progress.skillProgresses.Add(existingProgress);
                }

                // Update the bonuses based on current level
                existingProgress.attackBonus = skill.attackBonus * existingProgress.level;
                existingProgress.damageBonus = skill.damageBonus * existingProgress.level;
                existingProgress.defenseBonus = skill.defenseBonus * existingProgress.level;
                existingProgress.healthBonus = skill.healthBonus * existingProgress.level;
                existingProgress.marchCapacityBonus = skill.marchCapacityBonus * existingProgress.level;
                existingProgress.rallyCapacityBonus = skill.rallyCapacityBonus * existingProgress.level;
                existingProgress.activeEffects = skill.skillEffects;
            }

            SaveHeroProgress(hero.HeroName, progress);
        }
    }

    // Method to get specific skill progress
    public SkillProgress GetSkillProgress(string heroName, string skillName)
    {
        if (heroProgressData.TryGetValue(heroName, out HeroProgress heroProgress))
        {
            return heroProgress.skillProgresses.Find(sp => sp.skillName == skillName);
        }
        return null;
    }

    // Method to get all skills progress for a hero
    public List<SkillProgress> GetAllSkillsProgress(string heroName)
    {
        if (heroProgressData.TryGetValue(heroName, out HeroProgress heroProgress))
        {
            return heroProgress.skillProgresses;
        }
        return null;
    }

    // Method to update specific skill progress
    public void UpdateSkillProgress(string heroName, SkillData skill)
    {
        if (!heroProgressData.TryGetValue(heroName, out HeroProgress heroProgress))
        {
            heroProgress = new HeroProgress();
            heroProgressData[heroName] = heroProgress;
        }

        var skillProgress = heroProgress.skillProgresses.Find(sp => sp.skillName == skill.skillName);
        if (skillProgress == null)
        {
            skillProgress = new SkillProgress { skillName = skill.skillName, level = 1 };
            heroProgress.skillProgresses.Add(skillProgress);
        }
        else
        {
            skillProgress.level++;
        }

        // Update bonuses based on new level
        skillProgress.attackBonus = skill.attackBonus * skillProgress.level;
        skillProgress.damageBonus = skill.damageBonus * skillProgress.level;
        skillProgress.defenseBonus = skill.defenseBonus * skillProgress.level;
        skillProgress.healthBonus = skill.healthBonus * skillProgress.level;
        skillProgress.marchCapacityBonus = skill.marchCapacityBonus * skillProgress.level;
        skillProgress.rallyCapacityBonus = skill.rallyCapacityBonus * skillProgress.level;
        skillProgress.activeEffects = skill.skillEffects;

        // Debug log
        Debug.Log($"Skill {skill.skillName} updated. New level: {skillProgress.level}");

        // Save progress immediately
        SaveHeroProgress(heroName, heroProgress);
    }

    private void SaveHeroProgress(string heroName, HeroProgress progress)
    {
        // Convert progress to JSON
        string progressJson = JsonUtility.ToJson(progress);
        // Save to PlayerPrefs
        PlayerPrefs.SetString($"HeroProgress_{heroName}", progressJson);
        PlayerPrefs.Save();
    }

    public int GetSkillLevel(string heroName, string skillName)
    {
        if (heroProgressData.TryGetValue(heroName, out HeroProgress progress))
        {
            var skillProgress = progress.skillProgresses.Find(sp => sp.skillName == skillName);
            if (skillProgress != null)
            {
                Debug.Log($"Getting skill level for {heroName} - {skillName}: {skillProgress.level}");
                return skillProgress.level;
            }
        }
        Debug.Log($"No progress found for {heroName} - {skillName}, returning 1");
        return 1;  // Return 1 for any unlocked skill (unlocking logic handled elsewhere)
    }

    // Method to get hero progress
    public HeroProgress GetHeroProgress(string heroName)
    {
        if (heroProgressData.TryGetValue(heroName, out HeroProgress progress))
        {
            return progress;
        }
        return null;
    }

    // Method to apply stored progress to a hero
    // Modified ApplyHeroProgress to include skill data
    public void ApplyHeroProgress(HeroData hero)
    {
        if (!heroProgressData.ContainsKey(hero.HeroName))
        {
            // Initialize progress data for new hero
            UpdateHeroProgress(hero);
        }

        HeroProgress progress = heroProgressData[hero.HeroName];

        // Load the stored progress into the hero
        hero.LoadProgress(
            progress.rank,
            progress.expLevel,
            progress.currentExp,
            progress.currentPts,
            progress.isLocked,
            progress.skillProgresses.Select(sp => sp.level).ToArray(),
            false  // Add this parameter to prevent skill updates
        );

        // Apply skill levels and effects from stored progress
        for (int i = 0; i < hero.Skills.Length; i++)
        {
            var skillProgress = progress.skillProgresses.Find(sp => sp.skillName == hero.Skills[i].skillName);
            if (skillProgress != null)
            {
                // Ensure first skill is at least level 1
                if (i == 0 && skillProgress.level < 1)
                {
                    skillProgress.level = 1;
                }

                // Update the skill's current stats based on its level
                SkillData skill = hero.Skills[i];
                skillProgress.attackBonus = skill.attackBonus * skillProgress.level;
                skillProgress.damageBonus = skill.damageBonus * skillProgress.level;
                skillProgress.defenseBonus = skill.defenseBonus * skillProgress.level;
                skillProgress.healthBonus = skill.healthBonus * skillProgress.level;
                skillProgress.marchCapacityBonus = skill.marchCapacityBonus * skillProgress.level;
                skillProgress.rallyCapacityBonus = skill.rallyCapacityBonus * skillProgress.level;

                // Debug.Log($"Applied progress for skill {hero.Skills[i].skillName}: Level {skillProgress.level}");
            }
            else
            {
                Debug.LogWarning($"No progress found for skill {hero.Skills[i].skillName} of hero {hero.HeroName}");
            }
        }
    }

    // Method to save all hero progress
    public void SaveAllHeroProgress()
    {
        if (heroSelector != null)
        {
            HeroData[] allHeroes = heroSelector.GetAllHeroes();
            if (allHeroes != null)
            {
                foreach (HeroData hero in allHeroes)
                {
                    if (hero != null)
                    {
                        UpdateHeroProgress(hero);
                    }
                }
            }
        }
    }

    [System.Serializable]
    public class FPSGameProgress
    {
        public int highestLevelReached = 0;
        public Dictionary<int, LevelStats> levelStatistics = new Dictionary<int, LevelStats>();
    }

    [System.Serializable]
    public class LevelStats
    {
        public int timesCompleted = 0;
        public float bestTime = float.MaxValue;
        public int highestScore = 0;
        public List<string> heroesUsed = new List<string>(); // Track which heroes completed this level
    }

    private const int MAX_FPS_LEVEL = 80;
    private FPSGameProgress fpsProgress = new FPSGameProgress();

    // Add these methods to handle FPS game progress
    public int GetCurrentFPSLevel()
    {
        return fpsProgress.highestLevelReached + 1;
    }

    public bool IsLevelUnlocked(int level)
    {
        return level <= fpsProgress.highestLevelReached + 1 && level <= MAX_FPS_LEVEL;
    }

    public void CompleteFPSLevel(int level, float completionTime, int score, string heroName)
    {
        if (level > MAX_FPS_LEVEL) return;

        if (!fpsProgress.levelStatistics.ContainsKey(level))
        {
            fpsProgress.levelStatistics[level] = new LevelStats();
        }

        LevelStats stats = fpsProgress.levelStatistics[level];
        stats.timesCompleted++;
        stats.bestTime = Mathf.Min(stats.bestTime, completionTime);
        stats.highestScore = Mathf.Max(stats.highestScore, score);

        if (!stats.heroesUsed.Contains(heroName))
        {
            stats.heroesUsed.Add(heroName);
        }

        // Update highest level if this is a new level completion
        if (level > fpsProgress.highestLevelReached)
        {
            fpsProgress.highestLevelReached = level;
        }

        // Save progress
        SaveFPSProgress();
    }

    public LevelStats GetLevelStats(int level)
    {
        return fpsProgress.levelStatistics.ContainsKey(level)
            ? fpsProgress.levelStatistics[level]
            : new LevelStats();
    }

    public int GetHighestLevelReached()
    {
        return fpsProgress.highestLevelReached;
    }

    private void SaveFPSProgress()
    {
        // Convert the progress data to JSON
        string progressJson = JsonUtility.ToJson(fpsProgress);

        // Save to PlayerPrefs
        PlayerPrefs.SetString("FPSGameProgress", progressJson);
        PlayerPrefs.Save();
    }

    private void LoadFPSProgress()
    {
        if (PlayerPrefs.HasKey("FPSGameProgress"))
        {
            string progressJson = PlayerPrefs.GetString("FPSGameProgress");
            fpsProgress = JsonUtility.FromJson<FPSGameProgress>(progressJson);
        }
    }


    #if UNITY_EDITOR
    public void ResetAllProgress()
    {
        heroProgressData.Clear();
        fpsProgress = new FPSGameProgress();

        // Clear ALL hero progress keys from PlayerPrefs
        var allKeys = new List<string>();
        foreach (var heroName in heroProgressData.Keys)
        {
            allKeys.Add($"HeroProgress_{heroName}");
        }

        // Also check for any other hero progress keys that might exist
        HeroData[] allHeroes = Resources.LoadAll<HeroData>("Heroes");
        foreach (var hero in allHeroes)
        {
            allKeys.Add($"HeroProgress_{hero.HeroName}");
        }

        // Delete all found keys
        foreach (var key in allKeys)
        {
            PlayerPrefs.DeleteKey(key);
        }

        PlayerPrefs.DeleteKey("FPSGameProgress");
        PlayerPrefs.Save();

        Debug.Log("All progress has been reset!");

        // If HeroSelector is available, refresh it
        var heroSelector = Object.FindFirstObjectByType<HeroSelector>();
        if (heroSelector != null)
        {
            heroSelector.RefreshUI();
        }
    }

    // Update balance data from the current game state
    public void UpdateBalanceData()
    {
        if (balanceData == null)
        {
            Debug.LogError("No balance data assigned!");
            return;
        }

        Debug.Log("Updating balance data from current game state...");

        // Clear existing data
        balanceData.heroBalanceData.Clear();
        balanceData.buildingBalanceData.Clear();
        balanceData.researchBalanceData.Clear();

        // Update hero balance data
        UpdateHeroBalanceData();

        // Update building balance data
        UpdateBuildingBalanceData();

        // Update research balance data
        UpdateResearchBalanceData();

        // Update resource generation data
        UpdateResourceGenerationData();

        // Update upgrade scaling data
        UpdateUpgradeScalingData();

        Debug.Log("Balance data updated successfully!");

        // Mark the asset as dirty so it gets saved
        UnityEditor.EditorUtility.SetDirty(balanceData);
        UnityEditor.AssetDatabase.SaveAssets();
    }

    // Update hero balance data
    private void UpdateHeroBalanceData()
    {
        // Find all heroes in the game
        HeroData[] allHeroes = Resources.FindObjectsOfTypeAll<HeroData>();

        foreach (var hero in allHeroes)
        {
            HeroBalanceData heroBalance = new HeroBalanceData
            {
                heroName = hero.HeroName,
                rarity = hero.Rarity,
                heroType = hero.CurrentHeroType,
                initialPower = hero.InitialPower,
                baseStats = hero.BaseStats,
                troopModifiers = hero.TroopModifiers,
                skills = new List<SkillBalanceData>()
            };

            // Collect skill data
            if (hero.Skills != null)
            {
                foreach (var skill in hero.Skills)
                {
                    if (skill != null)
                    {
                        SkillBalanceData skillBalance = new SkillBalanceData
                        {
                            skillName = skill.skillName,
                            attackBonus = skill.attackBonus,
                            damageBonus = skill.damageBonus,
                            defenseBonus = skill.defenseBonus,
                            healthBonus = skill.healthBonus,
                            marchCapacityBonus = skill.marchCapacityBonus,
                            rallyCapacityBonus = skill.rallyCapacityBonus,
                            effects = new List<SkillEffectData>()
                        };

                        // Collect skill effects
                        if (skill.skillEffects != null)
                        {
                            foreach (var effect in skill.skillEffects)
                            {
                                SkillEffectData effectData = new SkillEffectData
                                {
                                    effectType = effect.effectType,
                                    effectValue = effect.effectValue,
                                    effectChance = effect.effectChance,
                                    duration = effect.turns
                                };

                                skillBalance.effects.Add(effectData);
                            }
                        }

                        heroBalance.skills.Add(skillBalance);
                    }
                }
            }

            balanceData.heroBalanceData.Add(heroBalance);
        }
    }

    // Update building balance data
    private void UpdateBuildingBalanceData()
    {
        // Find all building prefabs or instances
        BuildingUpgrade[] allBuildings = Resources.FindObjectsOfTypeAll<BuildingUpgrade>();

        foreach (var building in allBuildings)
        {
            // Skip duplicates
            if (balanceData.buildingBalanceData.Any(b => b.buildingName == building.name))
                continue;

            BuildingBalanceData buildingBalance = new BuildingBalanceData
            {
                buildingName = building.name,
                buildingType = building.buildingType,
                maxLevel = building.maxLevel,

                // Base costs
                baseFoodCost = building.baseFoodCost,
                baseWoodCost = building.baseWoodCost,
                baseMetalCost = building.baseMetalCost,
                baseGoldCost = building.baseGoldCost,

                // Base values
                baseUpgradeTime = building.baseUpgradeTime,
                baseBattlePower = building.baseBattlePower,
                baseWelfare = building.baseWelfare,
                baseResourceGeneration = building.baseResourceGeneration,

                // Scaling factors
                resourceGrowthFactor = building.resourceGrowthFactor,
                timeGrowthFactor = building.timeGrowthFactor,
                battlePowerGrowthFactor = building.battlePowerGrowthFactor,
                welfareGrowthFactor = building.welfareGrowthFactor,
                resourceGenerationGrowthFactor = building.resourceGenerationGrowthFactor
            };

            balanceData.buildingBalanceData.Add(buildingBalance);
        }
    }

    // Update research balance data
    private void UpdateResearchBalanceData()
    {
        // Find all research node assets
        ResearchNode[] allResearch = Resources.FindObjectsOfTypeAll<ResearchNode>();

        Debug.Log($"Found {allResearch.Length} research nodes to collect balance data from.");

        // Clear existing research data to avoid duplicates
        balanceData.researchBalanceData.Clear();

        foreach (var research in allResearch)
        {
            // Skip duplicates by checking if we already have this research type and bonus combination
            if (balanceData.researchBalanceData.Any(r =>
                r.nodeType == research.nodeType && r.bonusType == research.bonusType))
            {
                continue;
            }

            ResearchBalanceData researchBalance = new ResearchBalanceData
            {
                researchName = research.name,
                nodeType = research.nodeType,
                bonusType = research.bonusType,
                isPercentageBonus = research.isPercentageBonus,

                // Base costs
                startingFoodCost = research.startingFoodCost,
                startingWoodCost = research.startingWoodCost,
                startingMetalCost = research.startingMetalCost,
                startingGoldCost = research.startingGoldCost,

                // Base values
                startingTime = research.startingTime,
                startingPower = research.startingPower,

                // Tiers
                tiers = new List<ResearchTierBalanceData>()
            };

            // Collect tier data
            if (research.tiers != null)
            {
                foreach (var tier in research.tiers)
                {
                    ResearchTierBalanceData tierData = new ResearchTierBalanceData
                    {
                        tierNumber = tier.tierNumber,
                        maxLevel = tier.maxLevel,
                        bonus = tier.bonus,
                        requiredLabLevel = tier.requiredLabLevel
                    };

                    researchBalance.tiers.Add(tierData);
                }

                // Sort tiers by tier number for better readability
                researchBalance.tiers = researchBalance.tiers.OrderBy(t => t.tierNumber).ToList();
            }

            balanceData.researchBalanceData.Add(researchBalance);
            Debug.Log($"Collected balance data for research: {research.nodeType}_{research.bonusType} with {researchBalance.tiers.Count} tiers.");
        }
    }

    // Update resource generation data
    private void UpdateResourceGenerationData()
    {
        // This is a placeholder - you'll need to adapt this to your actual game data
        balanceData.resourceGeneration.foodGenerationRate = 1.0f; // Replace with actual values
        balanceData.resourceGeneration.woodGenerationRate = 1.0f;
        balanceData.resourceGeneration.metalGenerationRate = 1.0f;
        balanceData.resourceGeneration.goldGenerationRate = 0.5f;
    }

    // Update upgrade scaling data
    private void UpdateUpgradeScalingData()
    {
        // This is a placeholder - you'll need to adapt this to your actual game data
        balanceData.upgradeScaling.heroRankScaling = 3.6f; // From HeroData.CalculateRequiredPtsForNextRank
        balanceData.upgradeScaling.buildingUpgradeTimeScaling = 1.3f; // From BuildingUpgrade.timeGrowthFactor
        balanceData.upgradeScaling.buildingResourceCostScaling = 1.5f; // From BuildingUpgrade.resourceGrowthFactor
        balanceData.upgradeScaling.researchTimeScaling = 1.3f; // From ResearchNode calculations
        balanceData.upgradeScaling.researchCostScaling = 1.3f; // From ResearchNode calculations
    }
    #endif

}

[System.Serializable]
    public class ResearchProgress
    {
        public ResearchNode.NodeType nodeType;
        public ResearchNode.BonusType bonusType;
        public int tierIndex;
        public int currentLevel;
        public float remainingTime;
    }

    [System.Serializable]
    public class ResearchSave
    {
        public ResearchNode.NodeType nodeType;
        public ResearchNode.BonusType bonusType;
        public int tierIndex;
        public int level;
    }
