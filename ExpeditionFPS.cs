using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.SceneManagement;
using System.Collections;
using DG.Tweening;
using System;
using HeroSystem;

// Add this line to get access to HeroProgress
using static GameManager;  // This gives access to nested classes in GameManager

public class ExpeditionFPS : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private TextMeshProUGUI heroNameText;
    [SerializeField] private Image heroImage;
    [SerializeField] private TextMeshProUGUI rarityText;
    [SerializeField] private TextMeshProUGUI attackText;
    [SerializeField] private TextMeshProUGUI defenseText;
    [SerializeField] private TextMeshProUGUI hpText;
    [SerializeField] private TextMeshProUGUI heroTypeText;
    [SerializeField] private TextMeshProUGUI generationText;
    [SerializeField] private TextMesh<PERSON><PERSON><PERSON>GUI powerText;

    [Header("Skills UI")]
    [SerializeField] private Transform skillsContainer;
    [SerializeField] private GameObject skillItemPrefab; // Prefab containing Icon and Level text

    [Header("Navigation")]
    [SerializeField] private Button nextButton;
    [SerializeField] private Button previousButton;
    [SerializeField] private Button chooseButton;
    [SerializeField] private Button closeButton;
    [SerializeField] private GameObject expeditionFPSUI;

    [Header("Ready UI")]
    [SerializeField] private GameObject readyUI;
    [SerializeField] private CanvasGroup fadeCanvasGroup;

    [Header("Scene Transition")]
    [SerializeField] private CanvasGroup canvasGroup;
    [SerializeField] private Image transitionImage; // The UI image that matches the 3D plane texture
    [SerializeField] private float fadeDuration = 1f;

    private List<HeroData> availableHeroes;
    private int currentHeroIndex = 0;
    private AsyncOperation sceneLoadOperation;
    private UIElementAnimator uiElementAnimator;
    private bool isTransitioning = false;

    private void Start()
    {
        // Make sure the fade canvas group persists during scene transitions
        if (fadeCanvasGroup != null)
        {
            DontDestroyOnLoad(fadeCanvasGroup.gameObject);
        }

        uiElementAnimator = expeditionFPSUI.GetComponent<UIElementAnimator>();
        LoadAvailableHeroes();
        SetupButtons();
        DisplayCurrentHero();

        // Get or add CanvasGroup component
        canvasGroup = expeditionFPSUI.GetComponent<CanvasGroup>();
        if (canvasGroup == null)
        {
            canvasGroup = expeditionFPSUI.AddComponent<CanvasGroup>();
        }

        // Initialize CanvasGroup settings
        canvasGroup.alpha = 1f;
        canvasGroup.interactable = true;
        canvasGroup.blocksRaycasts = true;

        // Initialize transition image
        if (transitionImage != null)
        {
            Color imageColor = transitionImage.color;
            imageColor.a = 0f;
            transitionImage.color = imageColor;
            transitionImage.gameObject.SetActive(false);
        }
    }

    private void LoadAvailableHeroes()
    {
        // Similar to HeroSelector's approach
        if (InventorySystem.Instance == null)
        {
            Debug.LogError("InventorySystem instance not found!");
            return;
        }

        HeroData[] allHeroes = Resources.LoadAll<HeroData>("Heroes");
        availableHeroes = allHeroes.Where(hero => !hero.IsLocked).ToList();

        // Sort by power like in HeroSelector
        availableHeroes = availableHeroes.OrderByDescending(hero => hero.CurrentPower).ToList();

        if (availableHeroes.Count == 0)
        {
            Debug.LogError("No available heroes found!");
            return;
        }
    }

    private void SetupButtons()
    {
        nextButton.onClick.AddListener(OnNextButtonClicked);
        previousButton.onClick.AddListener(OnPreviousButtonClicked);
        chooseButton.onClick.AddListener(OnChooseButtonClicked);
        closeButton.onClick.AddListener(CloseExpeditionFPSUI);
    }

    public void DisplayCurrentHero()
    {
        if (availableHeroes.Count == 0) return;

        HeroData hero = availableHeroes[currentHeroIndex];

        // Load hero progress from GameManager
        GameManager.Instance.ApplyHeroProgress(hero);

        // Update basic info
        heroNameText.text = hero.HeroName;
        heroImage.sprite = hero.FallbackImage;
        rarityText.text = hero.Rarity.ToString();
        heroTypeText.text = hero.CurrentHeroType.ToString();
        generationText.text = $"Gen {hero.HeroGeneration}";
        powerText.text = hero.CurrentPower.ToString();

        // Update stats using BaseStats
        attackText.text = hero.BaseStats.Attack.ToString();
        defenseText.text = hero.BaseStats.Defense.ToString();
        hpText.text = hero.BaseStats.HP.ToString();

        // Clear and populate skills
        foreach (Transform child in skillsContainer)
        {
            Destroy(child.gameObject);
        }

        // Display skills with proper level check
        for (int i = 0; i < Mathf.Min(3, hero.Skills.Length); i++)
        {
            GameObject skillItem = Instantiate(skillItemPrefab, skillsContainer);

            // Get components
            Image skillIcon = skillItem.GetComponentInChildren<Image>();
            TextMeshProUGUI skillLevel = skillItem.transform.Find("LevelText")?.GetComponent<TextMeshProUGUI>();
            TextMeshProUGUI skillName = skillItem.transform.Find("SkillName")?.GetComponent<TextMeshProUGUI>();

            // Get skill level from GameManager's progress data
            HeroProgress progress = GameManager.Instance.GetHeroProgress(hero.HeroName);
            // First skill starts at level 1, others use progress data
            int level = (i == 0) ?
                Math.Max(1, progress?.skillProgresses[i]?.level ?? 1) :
                progress?.skillProgresses[i]?.level ?? 0;

            // Update UI elements
            if (skillIcon != null) skillIcon.sprite = hero.Skills[i].skillIcon;
            if (skillLevel != null) skillLevel.text = $"{level}";
            if (skillName != null) skillName.text = hero.Skills[i].skillName;
        }
    }

    private void OnNextButtonClicked()
    {
        currentHeroIndex = (currentHeroIndex + 1) % availableHeroes.Count;
        DisplayCurrentHero();
    }

    private void OnPreviousButtonClicked()
    {
        currentHeroIndex = (currentHeroIndex - 1 + availableHeroes.Count) % availableHeroes.Count;
        DisplayCurrentHero();
    }

    private void OnChooseButtonClicked()
    {
        HeroData selectedHero = availableHeroes[currentHeroIndex];

        // Save progress before transitioning
        GameManager.Instance.UpdateHeroProgress(selectedHero);

        // Store selected hero
        GameManager.Instance.SetSelectedHero(selectedHero);

        // Get current FPS level
        int currentLevel = GameManager.Instance.GetCurrentFPSLevel();

        // Store level info for the game scene to use
        PlayerPrefs.SetInt("CurrentFPSLevel", currentLevel);
        PlayerPrefs.Save();

        // Show Ready UI - animations will play automatically
        readyUI.SetActive(true);

        // Start scene loading
        StartCoroutine(LoadNextScene());
    }

    private IEnumerator LoadNextScene()
    {
        // Start loading the new scene
        sceneLoadOperation = SceneManager.LoadSceneAsync("Defense Challenge");
        sceneLoadOperation.allowSceneActivation = false;

        // Get the Animator component from Ready UI's child
        Animator readyAnimator = readyUI.GetComponentInChildren<Animator>();
        if (readyAnimator != null)
        {
            // Get the current animation clip info
            AnimatorClipInfo[] clipInfo = readyAnimator.GetCurrentAnimatorClipInfo(0);
            if (clipInfo.Length > 0)
            {
                // Wait for the animation length plus a small buffer
                float clipLength = clipInfo[0].clip.length;
                yield return new WaitForSeconds(clipLength + 0.2f);
            }
        }

        // Wait for scene to be ready
        while (sceneLoadOperation.progress < 0.9f)
        {
            yield return null;
        }

        // Fade in the transition image that matches the 3D plane
        transitionImage.gameObject.SetActive(true);
        yield return transitionImage.DOFade(1f, fadeDuration).WaitForCompletion();

        // Switch scenes - the second scene will start with camera looking at the 3D plane
        sceneLoadOperation.allowSceneActivation = true;
    }

    public void CloseExpeditionFPSUI()
    {
        if (isTransitioning) return;
        isTransitioning = true;

        StartCoroutine(FadeOut());
    }

    private IEnumerator FadeOut()
    {
        float duration = 0.3f;
        float elapsedTime = 0f;
        float startAlpha = canvasGroup.alpha;

        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float normalizedTime = elapsedTime / duration;

            canvasGroup.alpha = Mathf.Lerp(startAlpha, 0f, normalizedTime);
            canvasGroup.interactable = false;
            canvasGroup.blocksRaycasts = false;

            yield return null;
        }

        // Ensure we reach the final state
        canvasGroup.alpha = 0f;
        canvasGroup.interactable = false;
        canvasGroup.blocksRaycasts = false;

        // Deactivate the UI
        expeditionFPSUI.SetActive(false);

        // Reset for next time
        canvasGroup.alpha = 1f;
        canvasGroup.interactable = true;
        canvasGroup.blocksRaycasts = true;
        isTransitioning = false;
    }

    private void OnDestroy()
    {
        // Clean up button listeners
        nextButton.onClick.RemoveListener(OnNextButtonClicked);
        previousButton.onClick.RemoveListener(OnPreviousButtonClicked);
        chooseButton.onClick.RemoveListener(OnChooseButtonClicked);
        closeButton.onClick.RemoveListener(CloseExpeditionFPSUI);
    }
}
