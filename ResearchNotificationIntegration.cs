using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Integrates the notification system with the research system.
/// Attach this to research node buttons or objects that should show notifications.
/// </summary>
public class ResearchNotificationIntegration : MonoBehaviour
{
    [Header("Research Settings")]
    [SerializeField] private ResearchNode researchNode;
    [SerializeField] private int tierIndex = 0;
    [SerializeField] private Button researchButton;

    private NotificationSystem notificationSystem;

    private void Awake()
    {
        // Add NotificationSystem component if it doesn't exist
        notificationSystem = GetComponent<NotificationSystem>();
        if (notificationSystem == null)
        {
            notificationSystem = gameObject.AddComponent<NotificationSystem>();
        }

        // Set process type to Research
        notificationSystem.SetProcessType("Research");

        // Set target ID based on research node and tier
        if (researchNode != null)
        {
            notificationSystem.SetTargetId($"Research_{researchNode.nodeType}_{researchNode.bonusType}_{tierIndex}");
        }
    }

    private void Start()
    {
        // Set resource requirements based on research node
        if (researchNode != null && tierIndex < researchNode.tiers.Count)
        {
            // Calculate costs for the next level
            int currentLevel = GameManager.Instance.GetResearchLevel(researchNode, tierIndex);
            int nextLevel = currentLevel + 1;

            // Check if we've reached max level
            if (nextLevel <= researchNode.tiers[tierIndex].maxLevel)
            {
                // Calculate costs
                int foodCost = CalculateCost(researchNode.startingFoodCost, nextLevel);
                int woodCost = CalculateCost(researchNode.startingWoodCost, nextLevel);
                int metalCost = CalculateCost(researchNode.startingMetalCost, nextLevel);
                int goldCost = CalculateCost(researchNode.startingGoldCost, nextLevel);

                // Set resource requirements
                notificationSystem.SetResourceRequirements(foodCost, woodCost, metalCost, goldCost);

                // Set custom availability check to also check lab level
                notificationSystem.SetCustomAvailabilityCheck(CheckResearchAvailability);
            }
            else
            {
                // Research is maxed out, no notification needed
                notificationSystem.SetEnabled(false);
            }
        }

        // Subscribe to button click event if button is assigned
        if (researchButton != null)
        {
            researchButton.onClick.AddListener(OnResearchButtonClicked);
        }

        // Subscribe to notification status changed event
        if (NotificationManager.Instance != null)
        {
            NotificationManager.Instance.OnNotificationStatusChanged += OnNotificationStatusChanged;
        }
    }

    private void OnDestroy()
    {
        // Unsubscribe from notification status changed event
        if (NotificationManager.Instance != null)
        {
            NotificationManager.Instance.OnNotificationStatusChanged -= OnNotificationStatusChanged;
        }

        // Unsubscribe from button click event
        if (researchButton != null)
        {
            researchButton.onClick.RemoveListener(OnResearchButtonClicked);
        }
    }

    /// <summary>
    /// Calculate cost based on starting cost and level
    /// </summary>
    private int CalculateCost(int startingCost, int level)
    {
        // This should match the formula used in ResearchNode.cs
        return Mathf.RoundToInt(startingCost * Mathf.Pow(1.3f, level - 1));
    }

    /// <summary>
    /// Check if research is available (resources and lab level)
    /// </summary>
    private bool CheckResearchAvailability()
    {
        if (researchNode == null || tierIndex >= researchNode.tiers.Count)
        {
            return false;
        }

        // Check if we've reached max level
        int currentLevel = GameManager.Instance.GetResearchLevel(researchNode, tierIndex);
        int nextLevel = currentLevel + 1;
        if (nextLevel > researchNode.tiers[tierIndex].maxLevel)
        {
            return false;
        }

        // Check lab level
        int requiredLabLevel = researchNode.tiers[tierIndex].requiredLabLevel;
        int currentLabLevel = GameManager.Instance.GetLabLevel();
        if (currentLabLevel < requiredLabLevel)
        {
            return false;
        }

        // Check resources
        int foodCost = CalculateCost(researchNode.startingFoodCost, nextLevel);
        int woodCost = CalculateCost(researchNode.startingWoodCost, nextLevel);
        int metalCost = CalculateCost(researchNode.startingMetalCost, nextLevel);
        int goldCost = CalculateCost(researchNode.startingGoldCost, nextLevel);

        return GameManager.Instance.HasEnoughResources(foodCost, woodCost, metalCost) &&
               GameManager.Instance.HasEnoughGold(goldCost);
    }

    /// <summary>
    /// Handle research button click
    /// </summary>
    private void OnResearchButtonClicked()
    {
        // Force check notification status after button click
        notificationSystem.ForceCheckNotification();
    }

    /// <summary>
    /// Handle notification status changed event
    /// </summary>
    private void OnNotificationStatusChanged(string targetId, bool isActive)
    {
        // Only handle events for this target
        if (targetId != notificationSystem.TargetId)
        {
            return;
        }

        // Update button interactable state if button is assigned
        if (researchButton != null)
        {
            researchButton.interactable = isActive;
        }

        // Update visual feedback on the research node button
        ResearchNodeButton nodeButton = GetComponent<ResearchNodeButton>();
        if (nodeButton != null)
        {
            // Try to enable/disable glow effects using reflection
            try
            {
                var method = nodeButton.GetType().GetMethod("SetGlowEffectsActive");
                if (method != null)
                {
                    method.Invoke(nodeButton, new object[] { isActive });
                }
                else
                {
                    // Try to find a similar method or field
                    var glowField = nodeButton.GetType().GetField("glowEffect",
                        System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (glowField != null)
                    {
                        var glowObject = glowField.GetValue(nodeButton) as GameObject;
                        if (glowObject != null)
                        {
                            glowObject.SetActive(isActive);
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to set glow effects: {e.Message}");
            }
        }
    }
}
