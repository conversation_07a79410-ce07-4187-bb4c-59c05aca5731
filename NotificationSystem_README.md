# Notification System

## Overview

The Notification System provides visual feedback to the player about available functions and upgradable objects in the game. It shows notifications when processes are available (not running) and the required resources or materials are available.

## File Structure

All notification system files are located in the `Assets/Game System/` folder:

- `NotificationManager.cs` - Central manager for the notification system
- `NotificationIndicator.cs` - Visual indicator component for notifications
- `NotificationSystem.cs` - Component to register objects with the NotificationManager
- `TrainingNotificationIntegration.cs` - Integration with the training system
- `ResearchNotificationIntegration.cs` - Integration with the research system
- `HeroSearchNotificationIntegration.cs` - Integration with the hero search system
- `NotificationIndicatorPrefab.cs` - Editor script to create the notification indicator prefab
- `NotificationSystem_README.md` - This documentation file

## Components

### NotificationManager

The central manager for the notification system. It tracks all processes and their availability, checks resource availability, and manages notification indicators.

- **Singleton**: Access via `NotificationManager.Instance`
- **Events**: Subscribe to `OnNotificationStatusChanged` to be notified when a notification status changes
- **Process Tracking**: Tracks ongoing processes and their status

### NotificationIndicator

Visual indicator component for notifications. It handles the visual effects for notification indicators, such as pulsing, glowing, and color changes.

- **Visual Effects**: Pulsing, glowing, color changes
- **Customization**: Set icon, label, color, etc.

### NotificationSystem

Component to register objects with the NotificationManager. Attach this to any object that needs to show notifications.

- **Resource Checking**: Automatically checks if required resources are available
- **Custom Availability**: Set custom availability check functions
- **Process Types**: Specify which process type this notification is for

### Integration Components

- **TrainingNotificationIntegration**: Integrates with the training system
- **ResearchNotificationIntegration**: Integrates with the research system
- **HeroSearchNotificationIntegration**: Integrates with the hero search system

## Process Types

The notification system uses process types to categorize different notifications. Here are the standard process types used in the system:

### Training Process Types
- `Training_Infantry` - For infantry troop training
- `Training_Rider` - For rider troop training
- `Training_Ranged` - For ranged troop training

### Research Process Types
- `Research` - For general research
- `Research_Combat` - For combat-related research
- `Research_Economy` - For economy-related research
- `Research_Defense` - For defense-related research

### Hero System Process Types
- `HeroUpgrade` - For all hero upgrades (experience, rank, and skills)
- `HeroSearch` - For hero search/recruitment

### Building System Process Types
- `BuildingUpgrade` - For building upgrades
- `BuildingConstruction` - For new building construction

## How to Use

### 1. Set Up NotificationManager

1. Create an empty GameObject in your scene
2. Add the `NotificationManager` component to it
3. Assign the `DefaultNotificationIndicatorPrefab` in the inspector
4. Set up custom indicators for different process types:
   - Add entries to the `Process Indicators` array
   - For each entry, specify the process type (e.g., "Training_Infantry", "HeroUpgrade")
   - Assign a custom prefab, glow color, label, and icon for each process type

### 2. Create Notification Indicator Prefab

1. In the Unity Editor, go to Tools > Create Notification Indicator Prefab
2. This will create a prefab at `Assets/UI System/Prefabs/NotificationIndicator.prefab`
3. Customize the prefab as needed (colors, sizes, etc.)

### 3. Add NotificationSystem to Objects

For basic notification functionality:

1. Add the `NotificationSystem` component to any object that needs notifications
2. Set the `Process Type` to match the process (e.g., "Training", "Research", "HeroSearch")
3. Set resource requirements if needed

For integration with specific systems:

1. Add the appropriate integration component:
   - `TrainingNotificationIntegration` for training buttons
   - `ResearchNotificationIntegration` for research nodes
   - `HeroSearchNotificationIntegration` for hero search buttons

### 4. Register Processes with NotificationManager

When a process starts:

```csharp
NotificationManager.Instance.RegisterOngoingProcess(
    "UniqueProcessId",
    "ProcessType",
    duration
);
```

When a process completes or is cancelled:

```csharp
NotificationManager.Instance.UnregisterOngoingProcess("UniqueProcessId");
```

## Example: Training System Integration

```csharp
// On training button
[SerializeField] private TroopType troopType;
[SerializeField] private int troopLevel = 1;

private void Start()
{
    // Add notification integration
    var integration = gameObject.AddComponent<TrainingNotificationIntegration>();
    integration.troopType = troopType;
    integration.troopLevel = troopLevel;
    integration.trainingButton = GetComponent<Button>();
}
```

## Example: Research System Integration

```csharp
// On research node button
[SerializeField] private ResearchNode researchNode;
[SerializeField] private int tierIndex = 0;

private void Start()
{
    // Add notification integration
    var integration = gameObject.AddComponent<ResearchNotificationIntegration>();
    integration.researchNode = researchNode;
    integration.tierIndex = tierIndex;
    integration.researchButton = GetComponent<Button>();
}
```

## Example: Hero Search Integration

```csharp
// On hero search button
[SerializeField] private bool isAdvancedSearch = false;

private void Start()
{
    // Add notification integration
    var integration = gameObject.AddComponent<HeroSearchNotificationIntegration>();
    integration.isAdvancedSearch = isAdvancedSearch;
    integration.searchButton = GetComponent<Button>();
}
```

## Example: Hero Upgrade Integration

```csharp
// On hero upgrade button
[SerializeField] private int heroId;
[SerializeField] private HeroUpgradeNotificationIntegration.HeroUpgradeType upgradeType;
[SerializeField] private int currentLevel;
[SerializeField] private int maxLevel;
[SerializeField] private int goldCost;

private void Start()
{
    // Add notification integration
    var integration = gameObject.AddComponent<HeroUpgradeNotificationIntegration>();
    integration.heroId = heroId;
    integration.upgradeType = upgradeType;
    integration.upgradeButton = GetComponent<Button>();

    // Set levels and costs
    integration.SetLevels(currentLevel, maxLevel);
    integration.SetCosts(goldCost);

    // For skill upgrades
    if (upgradeType == HeroUpgradeNotificationIntegration.HeroUpgradeType.Skill)
    {
        integration.skillIndex = 0; // Set to the appropriate skill index
    }
}
```

## Custom Availability Checks

You can set custom availability checks for more complex conditions:

```csharp
NotificationSystem notificationSystem = GetComponent<NotificationSystem>();
notificationSystem.SetCustomAvailabilityCheck(() => {
    // Custom logic here
    return someCondition && anotherCondition;
});
```

## Troubleshooting

- **No notifications appear**: Make sure the NotificationManager is in the scene and the NotificationIndicatorPrefab is assigned
- **Notifications don't update**: Check if processes are being properly registered and unregistered
- **Resource checks not working**: Ensure GameManager is accessible and resource methods are working correctly
