Shader "Custom/UIEnergyLine"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _GlowColor ("Glow Color", Color) = (0,1,1,1)
        _GlowSize ("Glow Size", Range(0.1, 5)) = 1
        _GlowIntensity ("Glow Intensity", Range(0.1, 5)) = 1
        _FlowTime ("Flow Time", Float) = 0
        _WaveFrequency ("Wave Frequency", Range(1, 20)) = 10
        _WaveAmplitude ("Wave Amplitude", Range(0, 0.5)) = 0.2
        _BloomIntensity ("Bloom Intensity", Range(1, 10)) = 2
    }
    
    SubShader
    {
        Tags { "Queue"="Transparent" "RenderType"="Transparent" }
        Blend One OneMinusSrcAlpha
        
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #include "UnityCG.cginc"
            
            float _FlowTime;
            float _SpawnOffset;
            float4 _GlowColor;
            float _GlowSize;
            float _GlowIntensity;
            float _WaveFrequency;
            float _WaveAmplitude;
            float _BloomIntensity;
            
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };
            
            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };
            
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                return o;
            }
            
            fixed4 frag(v2f i) : SV_Target
            {
                float wave = sin(i.uv.x * _WaveFrequency + _FlowTime) * _WaveAmplitude;
                float distanceToWave = abs(i.uv.y - (0.5 + wave));
                float glow = saturate(1 - distanceToWave * _GlowSize);

                // Combine glow with color and ensure proper alpha
                fixed4 finalColor = _GlowColor * glow * _GlowIntensity * _BloomIntensity;
                finalColor.a = saturate(glow); // Set alpha based on glow intensity
                return finalColor;
            }
            ENDCG
        }
    }
}
