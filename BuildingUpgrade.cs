using UnityEngine;
using System.Collections; // Required for IEnumerator
using System.Collections.Generic; // Required for List<T>
using TMPro; // Import TextMeshPro namespace

public class BuildingUpgrade : MonoBehaviour
{
    // Event for when the building is upgraded
    public event System.Action<int> OnBuildingUpgraded;

    [Header("Upgrade Settings")]
    public int maxLevel = 30;
    private int currentLevel = 1; // Ensure this exists!

    public enum BuildingType
    {
        Farm,
        LumberMill,
        Quarry,
        PowerPlant,
        NonGenerating
    }

    [Header("Building Type & Resource Generation")]
    public BuildingType buildingType;
    public string generatedResource; // "Food", "Wood", "Metal", "Energy"
    public float baseResourceGeneration = 10f;
    public float resourceGenerationGrowthFactor = 1.3f; // Separate from upgrade cost growth
    public float generationInterval = 5f;

    // Public getter to access currentLevel safely
    public int CurrentLevel => currentLevel;

    public TextMeshProUGUI levelText; // Reference to the level display
    public GameObject upgradeCountdownPrefab; // Assign prefab in Inspector
    private TextMeshProUGUI countdownText;
    public GameObject upgradeEffectPrefab; // Assign in Inspector


    [System.Serializable]
    public class RequiredBuilding
    {
        public BuildingUpgrade building; // Reference to the required building
        public int requiredLevel; // Level it needs to reach
    }

    [Header("Building Dependencies")]
    public List<RequiredBuilding> requiredBuildings; // Assign in Inspector

    [Header("Scaling Factors")]
    public float resourceGrowthFactor = 1.5f;
    public float timeGrowthFactor = 1.3f;
    public float battlePowerGrowthFactor = 1.2f;
    public float welfareGrowthFactor = 1.1f;

    [Header("Base Resource Costs")]
    public int baseFoodCost = 100;
    public int baseWoodCost = 100;
    public int baseMetalCost = 100;

    [Header("Base Values")]
    public float baseUpgradeTime = 10f;
    public int baseBattlePower = 5;
    public int baseWelfare = 3;

    [Header("Express Upgrade Costs")]
    public int baseGoldCost = 50;
    public float goldCostGrowthFactor = 1.4f;

    [System.Serializable]
    public class LevelPrefab
    {
        public int level;
        public GameObject prefab;
    }

    [Header("Prefabs for Each Level")]
    public List<LevelPrefab> levelPrefabs; // Prefabs for each level

    // Public property to access if it's an express upgrade
    public bool IsExpressUpgrade => currentLevel < maxLevel;

    // Method to calculate required upgrade time based on the level
    public float GetRequiredUpgradeTime(int currentLevel)
    {
        return Mathf.Max(baseUpgradeTime * Mathf.Pow(timeGrowthFactor, currentLevel - 1), 10f);
    }

    private void Start()
    {
        // Register this building with the GameManager
        GameManager.Instance.RegisterBuilding(gameObject);

        // Update the level display
        UpdateLevelDisplay();

        // Start resource generation if applicable
        if (buildingType != BuildingType.NonGenerating && !string.IsNullOrEmpty(generatedResource))
        {
            StartCoroutine(GenerateResources());
        }

        Debug.Log($"Building {gameObject.name} started and registered with GameManager at level {currentLevel}.");
    }

    private IEnumerator GenerateResources()
    {
        while (true)
        {
            yield return new WaitForSeconds(generationInterval);
            GenerateResource();

        }
    }

    public void ApplyBaseStats()
    {
        GameManager.Instance.AddBattlePower(baseBattlePower);
        GameManager.Instance.AddWelfare(baseWelfare);

        Debug.Log($"{gameObject.name} placed. Added BP: {baseBattlePower}, Added Welfare: {baseWelfare}");
    }

    private void GenerateResource()
    {
        int generatedAmount = Mathf.RoundToInt(baseResourceGeneration * Mathf.Pow(resourceGenerationGrowthFactor, currentLevel - 1));

        switch (generatedResource)
        {
            case "Food":
                GameManager.Instance.AddResources(generatedAmount, 0, 0);
                break;
            case "Wood":
                GameManager.Instance.AddResources(0, generatedAmount, 0);
                break;
            case "Metal":
                GameManager.Instance.AddResources(0, 0, generatedAmount);
                break;
            case "Energy":
                // Get the BuildingCapabilities component
                BuildingCapabilities buildingCapabilities = GetComponent<BuildingCapabilities>();
                if (buildingCapabilities != null)
                {
                    // Find the energy capability and update it
                    bool foundCapability = false;
                    foreach (var capability in buildingCapabilities.capabilities)
                    {
                        if (capability.capabilityType == BuildingCapabilityType.ResourceProduction_Energy)
                        {
                            capability.baseValue = generatedAmount;
                            foundCapability = true;
                            Debug.Log($"Updated Energy capability on {gameObject.name} to {generatedAmount}");
                            break;
                        }
                    }

                    // If no capability exists, add it
                    if (!foundCapability)
                    {
                        buildingCapabilities.capabilities.Add(new BuildingCapability
                        {
                            capabilityType = BuildingCapabilityType.ResourceProduction_Energy,
                            baseValue = generatedAmount,
                            growthFactor = 1.0f
                        });
                        Debug.Log($"Added Energy capability to {gameObject.name} with value {generatedAmount}");
                    }
                }
                else
                {
                    // If no BuildingCapabilities component, update via GameManager
                    GameManager.Instance.SetBuildingCapabilityValue(gameObject, BuildingCapabilityType.ResourceProduction_Energy, generatedAmount);
                    Debug.Log($"No BuildingCapabilities component on {gameObject.name}, updated via GameManager");
                }

                Debug.Log($"Generated {generatedAmount} Energy from {gameObject.name}");
                break;
        }

    }


    public void UpgradeBuilding(bool isExpress = false)
    {
        if (isExpress)
        {
            // Instant upgrade logic, adjusting for Express Upgrade
            currentLevel = Mathf.Min(currentLevel + 1, maxLevel);
        }
        else
        {
            // Normal upgrade
            currentLevel = Mathf.Min(currentLevel + 1, maxLevel);
        }

        // Trigger the OnBuildingUpgraded event
        OnBuildingUpgraded?.Invoke(currentLevel);

        // Calculate battle power and welfare increase
        int battlePowerIncrease = Mathf.RoundToInt(baseBattlePower * Mathf.Pow(battlePowerGrowthFactor, currentLevel - 1));
        int welfareIncrease = Mathf.RoundToInt(baseWelfare * Mathf.Pow(welfareGrowthFactor, currentLevel - 1));

        // Update GameManager values
        GameManager.Instance.AddBattlePower(battlePowerIncrease);
        GameManager.Instance.AddWelfare(welfareIncrease);

        // Update building level in GameManager
        GameManager.Instance.UpdateBuildingLevel(gameObject, currentLevel);

        // Update all capabilities based on building type and level
        BuildingCapabilities buildingCapabilities = GetComponent<BuildingCapabilities>();

        // First, handle resource production based on building type
        if (buildingType == BuildingType.PowerPlant || generatedResource == "Energy")
        {
            // Calculate new energy production based on level
            int newEnergyProduction = Mathf.RoundToInt(baseResourceGeneration *
                Mathf.Pow(resourceGenerationGrowthFactor, currentLevel - 1));

            Debug.Log($"Calculating new energy production for level {currentLevel}: {baseResourceGeneration} * {resourceGenerationGrowthFactor}^{currentLevel-1} = {newEnergyProduction}");

            if (buildingCapabilities != null)
            {
                // Update the energy production capability
                buildingCapabilities.EnsureCapability(BuildingCapabilityType.ResourceProduction_Energy, newEnergyProduction);

                // Also update the energyProduction field for compatibility
                buildingCapabilities.energyProduction = newEnergyProduction;

                // Force update the BuildingCapabilities instance
                BuildingCapabilities.Instance?.UpdateEnergyProduction();

                Debug.Log($"Updated energy production for {gameObject.name} to {newEnergyProduction} at level {currentLevel}");
            }
            else
            {
                // Update via GameManager if no BuildingCapabilities component
                GameManager.Instance.SetBuildingCapabilityValue(gameObject,
                    BuildingCapabilityType.ResourceProduction_Energy, newEnergyProduction);

                Debug.Log($"Updated energy production via GameManager for {gameObject.name} to {newEnergyProduction}");
            }
        }
        else if (buildingType == BuildingType.Farm || generatedResource == "Food")
        {
            // Calculate new food production based on level
            int newFoodProduction = Mathf.RoundToInt(baseResourceGeneration *
                Mathf.Pow(resourceGenerationGrowthFactor, currentLevel - 1));

            Debug.Log($"Calculating new food production for level {currentLevel}: {baseResourceGeneration} * {resourceGenerationGrowthFactor}^{currentLevel-1} = {newFoodProduction}");

            if (buildingCapabilities != null)
            {
                buildingCapabilities.EnsureCapability(BuildingCapabilityType.ResourceProduction_Food, newFoodProduction);
                Debug.Log($"Updated food production for {gameObject.name} to {newFoodProduction} at level {currentLevel}");
            }
            else
            {
                GameManager.Instance.SetBuildingCapabilityValue(gameObject,
                    BuildingCapabilityType.ResourceProduction_Food, newFoodProduction);

                Debug.Log($"Updated food production via GameManager for {gameObject.name} to {newFoodProduction}");
            }
        }
        else if (buildingType == BuildingType.LumberMill || generatedResource == "Wood")
        {
            // Calculate new wood production based on level
            int newWoodProduction = Mathf.RoundToInt(baseResourceGeneration *
                Mathf.Pow(resourceGenerationGrowthFactor, currentLevel - 1));

            Debug.Log($"Calculating new wood production for level {currentLevel}: {baseResourceGeneration} * {resourceGenerationGrowthFactor}^{currentLevel-1} = {newWoodProduction}");

            if (buildingCapabilities != null)
            {
                buildingCapabilities.EnsureCapability(BuildingCapabilityType.ResourceProduction_Wood, newWoodProduction);
                Debug.Log($"Updated wood production for {gameObject.name} to {newWoodProduction} at level {currentLevel}");
            }
            else
            {
                GameManager.Instance.SetBuildingCapabilityValue(gameObject,
                    BuildingCapabilityType.ResourceProduction_Wood, newWoodProduction);

                Debug.Log($"Updated wood production via GameManager for {gameObject.name} to {newWoodProduction}");
            }
        }
        else if (buildingType == BuildingType.Quarry || generatedResource == "Metal")
        {
            // Calculate new metal production based on level
            int newMetalProduction = Mathf.RoundToInt(baseResourceGeneration *
                Mathf.Pow(resourceGenerationGrowthFactor, currentLevel - 1));

            Debug.Log($"Calculating new metal production for level {currentLevel}: {baseResourceGeneration} * {resourceGenerationGrowthFactor}^{currentLevel-1} = {newMetalProduction}");

            if (buildingCapabilities != null)
            {
                buildingCapabilities.EnsureCapability(BuildingCapabilityType.ResourceProduction_Metal, newMetalProduction);
                Debug.Log($"Updated metal production for {gameObject.name} to {newMetalProduction} at level {currentLevel}");
            }
            else
            {
                GameManager.Instance.SetBuildingCapabilityValue(gameObject,
                    BuildingCapabilityType.ResourceProduction_Metal, newMetalProduction);

                Debug.Log($"Updated metal production via GameManager for {gameObject.name} to {newMetalProduction}");
            }
        }

        // Now update other capabilities based on building name and type
        if (buildingCapabilities != null)
        {
            // Update all capabilities with growth factors
            foreach (var capability in buildingCapabilities.capabilities)
            {
                // Skip resource production capabilities as they're handled above
                if (capability.capabilityType == BuildingCapabilityType.ResourceProduction_Energy ||
                    capability.capabilityType == BuildingCapabilityType.ResourceProduction_Food ||
                    capability.capabilityType == BuildingCapabilityType.ResourceProduction_Wood ||
                    capability.capabilityType == BuildingCapabilityType.ResourceProduction_Metal)
                {
                    continue;
                }

                // Only update if the capability has a growth factor
                if (capability.growthFactor > 0)
                {
                    float baseValue = capability.baseValue / Mathf.Pow(capability.growthFactor, currentLevel - 2);
                    float newValue = baseValue * Mathf.Pow(capability.growthFactor, currentLevel - 1);

                    capability.baseValue = newValue;

                    Debug.Log($"Updated capability {capability.capabilityType} for {gameObject.name} to {newValue} at level {currentLevel}");

                    // Also update in GameManager
                    GameManager.Instance.SetBuildingCapabilityValue(gameObject, capability.capabilityType, newValue);
                }
            }
        }

        // Update the BuildingInfoUI values if it's open, but don't show it if it's not
        BuildingInfoUI infoUI = FindFirstObjectByType<BuildingInfoUI>();
        if (infoUI != null && infoUI.gameObject.activeInHierarchy && infoUI.IsShowingBuildingInfo(gameObject))
        {
            BuildingMenu buildingMenu = GetComponent<BuildingMenu>();
            if (buildingMenu != null)
            {
                Debug.Log("Updating BuildingInfoUI values without showing it");
                infoUI.UpdateBuildingInfoValues(buildingMenu);
            }
        }

        // Handle visual updates or other logic
        UpdateBuildingAppearance();
        UpdateLevelDisplay(); // Update level text after upgrading

        Debug.Log($"Building {gameObject.name} upgraded to level {currentLevel}. Updated GameManager building data.");
    }

    public void UpdateBuildingAppearance()
    {
        // This should instantiate the correct prefab for the current level
        foreach (LevelPrefab prefabInfo in levelPrefabs)
        {
            if (prefabInfo.level == currentLevel)
            {
                // Instantiate or switch the prefab for the new level
                GameObject currentPrefab = prefabInfo.prefab;
                if (currentPrefab != null)
                {
                    Instantiate(currentPrefab, transform.position, transform.rotation);
                }
            }
        }
    }

    private void UpdateLevelDisplay()
    {
        if (levelText != null)
        {
            levelText.text = $"{currentLevel}";
            Debug.Log($"Building Level Updated: {currentLevel}");
        }
    }

    public void StartUpgrade(float upgradeTime)
    {
        Debug.Log($"StartUpgrade called with duration: {upgradeTime} seconds");

        if (upgradeCountdownPrefab == null)
        {
            Debug.LogError("UpgradeCountdownPrefab is NOT assigned in the inspector!");
            return;
        }

        // Activate the existing countdown UI (do not instantiate)
        upgradeCountdownPrefab.SetActive(true);

        // Get the TextMeshPro component for countdown display
        countdownText = upgradeCountdownPrefab.GetComponentInChildren<TextMeshProUGUI>();
        if (countdownText == null)
        {
            Debug.LogError("No TextMeshProUGUI component found in Upgrade Countdown UI!");
            return;
        }

        Debug.Log("Starting upgrade countdown coroutine...");
        StartCoroutine(UpgradeCountdown(upgradeTime));
    }


    private IEnumerator UpgradeCountdown(float duration)
    {
        float remainingTime = duration;

        Debug.Log($"Upgrade countdown started: {remainingTime} seconds remaining.");

        while (remainingTime > 0)
        {
            if (countdownText != null)
            {
                countdownText.text = FormatTime(remainingTime);
            }
            else
            {
                Debug.LogError("Countdown Text is NULL!");
            }

            yield return new WaitForSeconds(1f);
            remainingTime -= 1f;
        }

        Debug.Log("Countdown reached 00:00:00, calling FinishUpgrade...");

        // Ensure it shows 00:00:00 before finishing
        if (countdownText != null)
        {
            countdownText.text = FormatTime(0);
        }

        FinishUpgrade();
    }



    private void FinishUpgrade()
    {
        Debug.Log("Upgrade completed! Playing effect...");

        // Play the upgrade effect
        if (upgradeEffectPrefab != null)
        {
            GameObject effectInstance = Instantiate(upgradeEffectPrefab, transform.position, Quaternion.identity);

            // Shrink the effect over 0.2 seconds
            LeanTween.scale(effectInstance, Vector3.zero, 0.05f)
                .setDelay(1.7f) // Delay the start of the shrinking
                .setOnComplete(() =>
                {
                    Destroy(effectInstance);
                });
        }
        else
        {
            Debug.LogError("UpgradeEffectPrefab is NOT assigned in Inspector!");
        }

        // Hide countdown panel
        if (upgradeCountdownPrefab != null)
        {
            upgradeCountdownPrefab.SetActive(false);
        }

        // Call UpgradeBuilding to perform the actual upgrade
        UpgradeBuilding(); // Ensure the building visually upgrades

        Debug.Log("Building upgrade completed successfully!");
    }




    private string FormatTime(float totalSeconds)
    {
        int hours = Mathf.FloorToInt(totalSeconds / 3600);
        int minutes = Mathf.FloorToInt((totalSeconds % 3600) / 60);
        int seconds = Mathf.FloorToInt(totalSeconds % 60);

        return $"{hours:00}:{minutes:00}:{seconds:00}";
    }

}
