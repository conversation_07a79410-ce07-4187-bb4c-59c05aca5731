using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

public class TrainingManager : MonoBehaviour
{
    private static TrainingManager _instance;
    public static TrainingManager Instance => _instance;

    [Header("Troop Data")]
    public List<TroopSO> infantryTroops = new List<TroopSO>();
    public List<TroopSO> riderTroops = new List<TroopSO>();
    public List<TroopSO> rangedTroops = new List<TroopSO>();

    [Header("Training Settings")]
    public int baseTrainingCapacity = 5;
    private int additionalTrainingCapacity = 0; // From research and buildings
    private int currentTrainingCount = 0;

    [Header("Training Queue")]
    private List<TrainingQueueItem> trainingQueue = new List<TrainingQueueItem>();

    [Header("Troop Counts")]
    private Dictionary<TroopType, Dictionary<int, int>> troopCounts = new Dictionary<TroopType, Dictionary<int, int>>();

    [Header("Building Levels")]
    public int infantryBarracksLevel = 1;
    public int riderBarracksLevel = 1;
    public int rangedBarracksLevel = 1;

    // Event for when training is completed
    public event Action<TroopSO, int> OnTrainingCompleted;
    // Event for when training is started
    public event Action<TroopSO, int, float> OnTrainingStarted;
    // Event for when training is cancelled
    public event Action<TroopSO> OnTrainingCancelled;
    // Event for when training is sped up
    public event Action<TroopSO, float> OnTrainingSpeededUp;

    private void Awake()
    {
        Debug.Log("TrainingManager.Awake() called");

        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            Debug.Log("TrainingManager singleton instance set");

            Debug.Log("Initializing troop counts");
            InitializeTroopCounts();

            // Log successful initialization
            Debug.Log($"TrainingManager initialized successfully. Instance ID: {GetInstanceID()}");

            // Initialize troop data if it's empty
            Debug.Log($"Infantry troops: {infantryTroops.Count}, Rider troops: {riderTroops.Count}, Ranged troops: {rangedTroops.Count}");
            if (infantryTroops.Count == 0 || riderTroops.Count == 0 || rangedTroops.Count == 0)
            {
                Debug.LogWarning("TrainingManager: Troop data is empty, attempting to load from Resources");
                LoadTroopDataFromResources();
            }

            // Log troop data after initialization
            Debug.Log($"After initialization - Infantry troops: {infantryTroops.Count}, Rider troops: {riderTroops.Count}, Ranged troops: {rangedTroops.Count}");

            // Log details about each troop
            LogTroopDetails();
        }
        else
        {
            Debug.LogWarning($"Duplicate TrainingManager detected. Destroying duplicate. Original instance ID: {_instance.GetInstanceID()}, Duplicate ID: {GetInstanceID()}");
            Destroy(gameObject);
        }
    }

    private void LogTroopDetails()
    {
        Debug.Log("Logging troop details:");

        Debug.Log("Infantry troops:");
        foreach (var troop in infantryTroops)
        {
            Debug.Log($"  {troop.Name}, Level: {troop.Level}, Image: {(troop.Image != null ? troop.Image.name : "null")}, Thumbnail: {(troop.Thumbnail != null ? troop.Thumbnail.name : "null")}");
        }

        Debug.Log("Rider troops:");
        foreach (var troop in riderTroops)
        {
            Debug.Log($"  {troop.Name}, Level: {troop.Level}, Image: {(troop.Image != null ? troop.Image.name : "null")}, Thumbnail: {(troop.Thumbnail != null ? troop.Thumbnail.name : "null")}");
        }

        Debug.Log("Ranged troops:");
        foreach (var troop in rangedTroops)
        {
            Debug.Log($"  {troop.Name}, Level: {troop.Level}, Image: {(troop.Image != null ? troop.Image.name : "null")}, Thumbnail: {(troop.Thumbnail != null ? troop.Thumbnail.name : "null")}");
        }
    }

    private void LoadTroopDataFromResources()
    {
        Debug.Log("LoadTroopDataFromResources called");

        // Try to load troop data from Resources folder
        Debug.Log("Attempting to load troops from Resources folder");
        TroopSO[] infantryTroopsArray = Resources.LoadAll<TroopSO>("Troops/Infantry");
        TroopSO[] riderTroopsArray = Resources.LoadAll<TroopSO>("Troops/rider"); // Fixed lowercase "rider"
        TroopSO[] rangedTroopsArray = Resources.LoadAll<TroopSO>("Troops/Ranged");

        // Log the paths we're trying to load from
        Debug.Log("Loading from paths: Troops/Infantry, Troops/rider, Troops/Ranged");

        // If we didn't find any troops, try alternative paths with different casing
        if (infantryTroopsArray.Length == 0)
        {
            Debug.Log("Trying alternative path for infantry troops: Troops/infantry");
            infantryTroopsArray = Resources.LoadAll<TroopSO>("Troops/infantry");
        }

        if (riderTroopsArray.Length == 0)
        {
            Debug.Log("Trying alternative path for rider troops: Troops/Rider");
            riderTroopsArray = Resources.LoadAll<TroopSO>("Troops/Rider");
        }

        if (rangedTroopsArray.Length == 0)
        {
            Debug.Log("Trying alternative path for ranged troops: Troops/ranged");
            rangedTroopsArray = Resources.LoadAll<TroopSO>("Troops/ranged");
        }

        Debug.Log($"Found {infantryTroopsArray.Length} infantry troops, {riderTroopsArray.Length} rider troops, {rangedTroopsArray.Length} ranged troops in Resources");

        if (infantryTroopsArray.Length > 0)
        {
            infantryTroops.AddRange(infantryTroopsArray);
            Debug.Log($"Added {infantryTroopsArray.Length} infantry troops to the list");

            // Log details about each troop
            foreach (var troop in infantryTroopsArray)
            {
                Debug.Log($"Infantry troop: {troop.Name}, Level: {troop.Level}, Image: {(troop.Image != null ? troop.Image.name : "null")}, Thumbnail: {(troop.Thumbnail != null ? troop.Thumbnail.name : "null")}");
            }
        }
        else
        {
            Debug.LogWarning("No infantry troops found in Resources");
        }

        if (riderTroopsArray.Length > 0)
        {
            riderTroops.AddRange(riderTroopsArray);
            Debug.Log($"Added {riderTroopsArray.Length} rider troops to the list");

            // Log details about each troop
            foreach (var troop in riderTroopsArray)
            {
                Debug.Log($"Rider troop: {troop.Name}, Level: {troop.Level}, Image: {(troop.Image != null ? troop.Image.name : "null")}, Thumbnail: {(troop.Thumbnail != null ? troop.Thumbnail.name : "null")}");
            }
        }
        else
        {
            Debug.LogWarning("No rider troops found in Resources");
        }

        if (rangedTroopsArray.Length > 0)
        {
            rangedTroops.AddRange(rangedTroopsArray);
            Debug.Log($"Added {rangedTroopsArray.Length} ranged troops to the list");

            // Log details about each troop
            foreach (var troop in rangedTroopsArray)
            {
                Debug.Log($"Ranged troop: {troop.Name}, Level: {troop.Level}, Image: {(troop.Image != null ? troop.Image.name : "null")}, Thumbnail: {(troop.Thumbnail != null ? troop.Thumbnail.name : "null")}");
            }
        }
        else
        {
            Debug.LogWarning("No ranged troops found in Resources");
        }

        // If we still don't have any troops, create some placeholder troops for testing
        if (infantryTroops.Count == 0 && riderTroops.Count == 0 && rangedTroops.Count == 0)
        {
            Debug.LogWarning("TrainingManager: Could not load troop data from Resources, creating placeholder troops");
            CreatePlaceholderTroops();
        }
        else
        {
            Debug.Log($"Successfully loaded troops: Infantry: {infantryTroops.Count}, Rider: {riderTroops.Count}, Ranged: {rangedTroops.Count}");
        }
    }

    private void CreatePlaceholderTroops()
    {
        // This is just for testing purposes when no troop data is available
        Debug.LogWarning("TrainingManager: Creating placeholder troops for testing");
        Debug.Log("Starting to create placeholder troops");

        // Try to load default sprites for troops
        Sprite defaultImage = Resources.Load<Sprite>("DefaultTroopImage");
        Sprite defaultThumbnail = Resources.Load<Sprite>("DefaultTroopThumbnail");

        if (defaultImage == null)
        {
            Debug.LogWarning("TrainingManager: Could not load default troop image from Resources, creating a default white sprite");

            // Create a default white sprite
            Texture2D texture = new Texture2D(100, 100);
            Color[] colors = new Color[100 * 100];
            for (int i = 0; i < colors.Length; i++)
            {
                colors[i] = Color.white;
            }
            texture.SetPixels(colors);
            texture.Apply();
            defaultImage = Sprite.Create(texture, new Rect(0, 0, 100, 100), new Vector2(0.5f, 0.5f));
        }

        if (defaultThumbnail == null)
        {
            Debug.LogWarning("TrainingManager: Could not load default troop thumbnail from Resources, using the default image");
            defaultThumbnail = defaultImage; // Use the same sprite for both image and thumbnail
        }

        // Create a placeholder infantry troop
        TroopSO infantryTroop = ScriptableObject.CreateInstance<TroopSO>();
        infantryTroop.Name = "Infantry Soldier";
        infantryTroop.Type = TroopType.Infantry;
        infantryTroop.Level = 1;
        // RequiredBuildingLevel is a read-only property calculated from Level
        infantryTroop.FoodCost = 100;
        infantryTroop.WoodCost = 50;
        infantryTroop.MetalCost = 25;
        infantryTroop.TrainingTime = 60; // 1 minute
        infantryTroop.BattlePower = 10;
        infantryTroop.Attack = 5;
        infantryTroop.Defense = 5;
        infantryTroop.Health = 100;
        infantryTroop.Description = "Basic infantry unit";

        // Set image and thumbnail if available
        if (defaultImage != null)
        {
            infantryTroop.Image = defaultImage;
        }

        if (defaultThumbnail != null)
        {
            infantryTroop.Thumbnail = defaultThumbnail;
        }

        // Calculate all stats and requirements
        infantryTroop.CalculateAll();
        infantryTroops.Add(infantryTroop);

        // Create a placeholder rider troop
        TroopSO riderTroop = ScriptableObject.CreateInstance<TroopSO>();
        riderTroop.Name = "Cavalry Rider";
        riderTroop.Type = TroopType.Rider;
        riderTroop.Level = 1;
        // RequiredBuildingLevel is a read-only property calculated from Level
        riderTroop.FoodCost = 150;
        riderTroop.WoodCost = 75;
        riderTroop.MetalCost = 50;
        riderTroop.TrainingTime = 120; // 2 minutes
        riderTroop.BattlePower = 20;
        riderTroop.Attack = 10;
        riderTroop.Defense = 5;
        riderTroop.Health = 150;
        riderTroop.Description = "Basic cavalry unit";

        // Set image and thumbnail if available
        if (defaultImage != null)
        {
            riderTroop.Image = defaultImage;
        }

        if (defaultThumbnail != null)
        {
            riderTroop.Thumbnail = defaultThumbnail;
        }

        // Calculate all stats and requirements
        riderTroop.CalculateAll();
        riderTroops.Add(riderTroop);

        // Create a placeholder ranged troop
        TroopSO rangedTroop = ScriptableObject.CreateInstance<TroopSO>();
        rangedTroop.Name = "Archer";
        rangedTroop.Type = TroopType.Ranged;
        rangedTroop.Level = 1;
        // RequiredBuildingLevel is a read-only property calculated from Level
        rangedTroop.FoodCost = 125;
        rangedTroop.WoodCost = 100;
        rangedTroop.MetalCost = 25;
        rangedTroop.TrainingTime = 90; // 1.5 minutes
        rangedTroop.BattlePower = 15;
        rangedTroop.Attack = 15;
        rangedTroop.Defense = 2;
        rangedTroop.Health = 75;
        rangedTroop.Description = "Basic ranged unit";

        // Set image and thumbnail if available
        if (defaultImage != null)
        {
            rangedTroop.Image = defaultImage;
        }

        if (defaultThumbnail != null)
        {
            rangedTroop.Thumbnail = defaultThumbnail;
        }

        // Calculate all stats and requirements
        rangedTroop.CalculateAll();
        rangedTroops.Add(rangedTroop);

        Debug.Log("Finished creating placeholder troops");
        Debug.Log($"Created troops: Infantry: {infantryTroops.Count}, Rider: {riderTroops.Count}, Ranged: {rangedTroops.Count}");
    }

    private void Start()
    {
        // Start checking the training queue
        StartCoroutine(ProcessTrainingQueue());
    }

    private void InitializeTroopCounts()
    {
        // Initialize dictionaries for each troop type
        troopCounts[TroopType.Infantry] = new Dictionary<int, int>();
        troopCounts[TroopType.Rider] = new Dictionary<int, int>();
        troopCounts[TroopType.Ranged] = new Dictionary<int, int>();

        // Initialize counts for each level (1-10)
        for (int i = 1; i <= 10; i++)
        {
            troopCounts[TroopType.Infantry][i] = 0;
            troopCounts[TroopType.Rider][i] = 0;
            troopCounts[TroopType.Ranged][i] = 0;
        }

        // Load saved troop counts if available
        LoadTroopCounts();
    }

    private void LoadTroopCounts()
    {
        // Load from PlayerPrefs or other save system
        for (int i = 1; i <= 10; i++)
        {
            int infantryCount = PlayerPrefs.GetInt($"InfantryCount_Level{i}", 0);
            int riderCount = PlayerPrefs.GetInt($"RiderCount_Level{i}", 0);
            int rangedCount = PlayerPrefs.GetInt($"RangedCount_Level{i}", 0);

            troopCounts[TroopType.Infantry][i] = infantryCount;
            troopCounts[TroopType.Rider][i] = riderCount;
            troopCounts[TroopType.Ranged][i] = rangedCount;
        }
    }

    private void SaveTroopCounts()
    {
        // Save to PlayerPrefs or other save system
        for (int i = 1; i <= 10; i++)
        {
            PlayerPrefs.SetInt($"InfantryCount_Level{i}", troopCounts[TroopType.Infantry][i]);
            PlayerPrefs.SetInt($"RiderCount_Level{i}", troopCounts[TroopType.Rider][i]);
            PlayerPrefs.SetInt($"RangedCount_Level{i}", troopCounts[TroopType.Ranged][i]);
        }
        PlayerPrefs.Save();
    }

    public int GetTroopCount(TroopType type, int level)
    {
        if (troopCounts.ContainsKey(type) && troopCounts[type].ContainsKey(level))
        {
            return troopCounts[type][level];
        }
        return 0;
    }

    public void AddTroops(TroopType type, int level, int count)
    {
        if (troopCounts.ContainsKey(type) && troopCounts[type].ContainsKey(level))
        {
            troopCounts[type][level] += count;
            SaveTroopCounts();
        }
    }

    public int GetTrainingCapacity()
    {
        return baseTrainingCapacity + additionalTrainingCapacity;
    }

    public int GetRemainingTrainingCapacity()
    {
        return GetTrainingCapacity() - currentTrainingCount;
    }

    public void SetAdditionalTrainingCapacity(int capacity)
    {
        additionalTrainingCapacity = capacity;
        Debug.Log($"Training capacity updated: Base {baseTrainingCapacity} + Additional {additionalTrainingCapacity} = Total {GetTrainingCapacity()}");
    }

    // Get training speed modifier from building capabilities
    public float GetTrainingSpeedModifier()
    {
        // Check if BuildingCapabilitiesManager exists
        if (BuildingCapabilitiesManager.Instance == null)
        {
            return 0f;
        }

        // Get training speed bonus from BuildingCapabilitiesManager
        return BuildingCapabilitiesManager.Instance.GetCapabilityValue(BuildingCapabilityType.TrainingSpeed);
    }

    public bool IsTroopUnlocked(TroopSO troop)
    {
        Debug.Log($"IsTroopUnlocked called for troop: {troop.Name}, Level: {troop.Level}");

        int buildingLevel = GetBuildingLevelForTroopType(troop.Type);
        int requiredLevel = troop.RequiredBuildingLevel;
        bool isUnlocked = buildingLevel >= requiredLevel;

        Debug.Log($"Building level: {buildingLevel}, Required level: {requiredLevel}, Is unlocked: {isUnlocked}");

        return isUnlocked;
    }

    private int GetBuildingLevelForTroopType(TroopType type)
    {
        Debug.Log($"GetBuildingLevelForTroopType called with type: {type}");

        int level;
        switch (type)
        {
            case TroopType.Infantry:
                level = infantryBarracksLevel;
                Debug.Log($"Infantry barracks level: {level}");
                break;
            case TroopType.Rider:
                level = riderBarracksLevel;
                Debug.Log($"Rider barracks level: {level}");
                break;
            case TroopType.Ranged:
                level = rangedBarracksLevel;
                Debug.Log($"Ranged barracks level: {level}");
                break;
            default:
                level = 0;
                Debug.LogWarning($"Unknown troop type: {type}, returning level 0");
                break;
        }

        return level;
    }

    public List<TroopSO> GetTroopsForType(TroopType type)
    {
        Debug.Log($"GetTroopsForType called with type: {type}");

        List<TroopSO> result;
        switch (type)
        {
            case TroopType.Infantry:
                result = infantryTroops;
                Debug.Log($"Returning {infantryTroops.Count} infantry troops");
                break;
            case TroopType.Rider:
                result = riderTroops;
                Debug.Log($"Returning {riderTroops.Count} rider troops");
                break;
            case TroopType.Ranged:
                result = rangedTroops;
                Debug.Log($"Returning {rangedTroops.Count} ranged troops");
                break;
            default:
                result = new List<TroopSO>();
                Debug.LogWarning($"Unknown troop type: {type}, returning empty list");
                break;
        }

        // Log details about each troop
        foreach (var troop in result)
        {
            Debug.Log($"Troop: {troop.Name}, Level: {troop.Level}, Type: {troop.Type}, Image: {(troop.Image != null ? troop.Image.name : "null")}, Thumbnail: {(troop.Thumbnail != null ? troop.Thumbnail.name : "null")}");
        }

        return result;
    }

    public TroopSO GetHighestUnlockedTroop(TroopType type)
    {
        Debug.Log($"GetHighestUnlockedTroop called with type: {type}");

        List<TroopSO> troops = GetTroopsForType(type);
        Debug.Log($"Got {troops.Count} troops for type {type}");

        TroopSO highestUnlocked = null;
        int highestLevel = 0;

        foreach (TroopSO troop in troops)
        {
            bool isUnlocked = IsTroopUnlocked(troop);
            Debug.Log($"Checking troop: {troop.Name}, Level: {troop.Level}, Unlocked: {isUnlocked}");

            if (isUnlocked && troop.Level > highestLevel)
            {
                highestUnlocked = troop;
                highestLevel = troop.Level;
                Debug.Log($"New highest unlocked troop: {troop.Name}, Level: {troop.Level}");
            }
        }

        if (highestUnlocked != null)
        {
            Debug.Log($"Returning highest unlocked troop: {highestUnlocked.Name}, Level: {highestLevel}");
        }
        else
        {
            Debug.LogWarning($"No unlocked troops found for type: {type}");
        }

        return highestUnlocked;
    }

    public bool CanTrainTroop(TroopSO troop, int count)
    {
        // Check if troop is unlocked
        if (!IsTroopUnlocked(troop))
        {
            Debug.Log($"Cannot train {troop.Name}: Troop is locked");
            return false;
        }

        // Check if there's enough training capacity
        if (GetRemainingTrainingCapacity() < count)
        {
            Debug.Log($"Cannot train {troop.Name}: Not enough training capacity");
            return false;
        }

        // Check if there are enough resources
        if (!GameManager.Instance.HasEnoughResources(troop.FoodCost * count, troop.WoodCost * count, troop.MetalCost * count))
        {
            Debug.Log($"Cannot train {troop.Name}: Not enough resources");
            return false;
        }

        return true;
    }

    public void StartTraining(TroopSO troop, int count)
    {
        if (!CanTrainTroop(troop, count))
        {
            return;
        }

        // Spend resources
        GameManager.Instance.SpendResources(troop.FoodCost * count, troop.WoodCost * count, troop.MetalCost * count);

        // Debug the troop object before calculation
        Debug.Log($"CRITICAL DEBUG - Troop before calculation: Name={troop.name}, Level={troop.Level}, Type={troop.Type}, TrainingTime={troop.TrainingTime}");

        // Force recalculation of the troop's training time
        troop.CalculateTrainingTime();

        // Debug the troop object after calculation
        Debug.Log($"CRITICAL DEBUG - Troop after calculation: Name={troop.name}, Level={troop.Level}, Type={troop.Type}, TrainingTime={troop.TrainingTime}");

        // Get the base training time per troop from the TroopSO
        float baseTimePerTroop = troop.TrainingTime;

        // If the training time is still 0, calculate it manually
        if (baseTimePerTroop <= 0f)
        {
            Debug.LogWarning($"CRITICAL WARNING: Troop.TrainingTime is still 0 after calculation. Calculating manually.");
            baseTimePerTroop = 6f * troop.Level * troop.Level + 6f;
            Debug.Log($"CRITICAL DEBUG - Manual calculation: 6 * {troop.Level} * {troop.Level} + 6 = {baseTimePerTroop}");
        }

        // Calculate total training time based on the number of troops (scales linearly)
        float totalTrainingTime = baseTimePerTroop * count;

        // Ensure the total training time is at least 12 seconds (minimum value from formula with level 1)
        totalTrainingTime = Mathf.Max(12f, totalTrainingTime);

        Debug.Log($"Training time calculation: {troop.Name} (Level {troop.Level}) x{count}");
        Debug.Log($"Base training time from TroopSO: {baseTimePerTroop} seconds per troop");
        Debug.Log($"Total training time: {totalTrainingTime} seconds (with minimum adjustment)");

        // We're enforcing a minimum time of 12 seconds to prevent instant completion

        // Apply training speed bonus from building capabilities if available
        float trainingSpeedModifier = GetTrainingSpeedModifier();
        if (trainingSpeedModifier > 0)
        {
            // Convert percentage bonus to time reduction (e.g., 20% speed = 20% less time)
            float timeReduction = totalTrainingTime * (trainingSpeedModifier / 100f);
            totalTrainingTime -= timeReduction;
            Debug.Log($"Applied training speed bonus: {trainingSpeedModifier}%. Reduced time by {timeReduction} seconds.");
        }

        Debug.Log($"Final training time: {totalTrainingTime} seconds");

        // CRITICAL FIX: Ensure the training time is correct before creating the queue item
        // Calculate the training time directly using the formula
        float directlyCalculatedTime = 6f * Mathf.Max(1, troop.Level) * Mathf.Max(1, troop.Level) + 6f;
        float directTotalTime = directlyCalculatedTime * count;

        // Use the directly calculated time if totalTrainingTime is 0 or negative
        if (totalTrainingTime <= 0f)
        {
            Debug.LogWarning($"CRITICAL WARNING: totalTrainingTime is {totalTrainingTime}, using directly calculated time: {directTotalTime}");
            totalTrainingTime = directTotalTime;
        }

        // Ensure the training time is at least 12 seconds
        totalTrainingTime = Mathf.Max(12f, totalTrainingTime);

        Debug.Log($"FINAL CHECK: Training time before creating queue item: {totalTrainingTime} seconds");

        // Add to training queue
        TrainingQueueItem queueItem = new TrainingQueueItem
        {
            Troop = troop,
            Count = count,
            RemainingTime = totalTrainingTime,
            TotalTime = totalTrainingTime
        };

        Debug.Log($"CRITICAL DEBUG: Created new TrainingQueueItem with RemainingTime = {queueItem.RemainingTime}, TotalTime = {queueItem.TotalTime}");

        // Final safety check - ensure the training time is at least 12 seconds
        if (queueItem.RemainingTime < 12f)
        {
            Debug.LogWarning($"CRITICAL WARNING: Queue item training time is too short ({queueItem.RemainingTime} seconds), forcing to 12 seconds minimum");
            queueItem.RemainingTime = 12f;
            queueItem.TotalTime = 12f;
        }

        trainingQueue.Add(queueItem);
        currentTrainingCount += count;

        // Log the state of the queue immediately after adding the item
        Debug.Log("Queue state immediately after adding new item:");
        LogTrainingQueueState();

        // Trigger event
        OnTrainingStarted?.Invoke(troop, count, totalTrainingTime);

        Debug.Log($"Started training {count} {troop.Name} troops. Time: {totalTrainingTime} seconds");
        Debug.Log($"Training queue now contains {trainingQueue.Count} items");

        // Log the state of the training queue
        LogTrainingQueueState();
    }

    public void CancelTraining(TrainingQueueItem queueItem)
    {
        if (trainingQueue.Contains(queueItem))
        {
            // Refund resources
            GameManager.Instance.AddResources(
                queueItem.Troop.FoodCost * queueItem.Count,
                queueItem.Troop.WoodCost * queueItem.Count,
                queueItem.Troop.MetalCost * queueItem.Count
            );

            // Remove from queue
            trainingQueue.Remove(queueItem);
            currentTrainingCount -= queueItem.Count;

            // Trigger event
            OnTrainingCancelled?.Invoke(queueItem.Troop);

            Debug.Log($"Cancelled training {queueItem.Count} {queueItem.Troop.Name} troops");
        }
    }

    public void SpeedUpTraining(TrainingQueueItem queueItem, float minutes)
    {
        if (trainingQueue.Contains(queueItem))
        {
            // Convert minutes to seconds
            float secondsToReduce = minutes * 60f;

            // Store old remaining time for logging
            float oldRemainingTime = queueItem.RemainingTime;

            // Reduce remaining time but ensure it's at least 1 second
            // This is critical - we never want to set it to 0 or below here
            queueItem.RemainingTime = Mathf.Max(1f, queueItem.RemainingTime - secondsToReduce);

            Debug.Log($"SpeedUpTraining: Reduced remaining time from {oldRemainingTime} to {queueItem.RemainingTime} seconds");

            // Trigger event
            OnTrainingSpeededUp?.Invoke(queueItem.Troop, minutes);

            Debug.Log($"Sped up training of {queueItem.Count} {queueItem.Troop.Name} troops by {minutes} minutes");

            // Log the state of the training queue after speed up
            Debug.Log("Training queue state after speed up:");
            LogTrainingQueueState();

            // IMPORTANT: We never complete training directly here
            // Always let the ProcessTrainingQueue coroutine handle completion
            // This ensures we don't modify the collection during iteration
            if (queueItem.RemainingTime <= 1f)
            {
                Debug.Log($"Training is almost complete after speed up, but letting ProcessTrainingQueue handle the completion");

                // Set remaining time to exactly 1 second to ensure it completes in the next ProcessTrainingQueue update
                queueItem.RemainingTime = 1f;
                Debug.Log($"Set remaining time to 1 second to ensure it completes in the next ProcessTrainingQueue update");
            }
        }
        else
        {
            Debug.LogWarning($"SpeedUpTraining: Queue item not found in training queue");
        }
    }

    private void CompleteTraining(TrainingQueueItem queueItem)
    {
        // Add troops to the count
        AddTroops(queueItem.Troop.Type, queueItem.Troop.Level, queueItem.Count);

        // Add battle power
        int totalBP = queueItem.Troop.BattlePower * queueItem.Count;
        GameManager.Instance.AddBattlePower(totalBP);

        // Remove from queue
        trainingQueue.Remove(queueItem);
        currentTrainingCount -= queueItem.Count;

        // Trigger event
        OnTrainingCompleted?.Invoke(queueItem.Troop, queueItem.Count);

        Debug.Log($"Completed training {queueItem.Count} {queueItem.Troop.Name} troops. Added {totalBP} Battle Power");
    }

    private IEnumerator ProcessTrainingQueue()
    {
        Debug.Log("ProcessTrainingQueue coroutine started");

        // Wait for a short time to ensure everything is initialized
        yield return new WaitForSeconds(0.5f);

        // Add a flag to track the first iteration
        bool isFirstIteration = true;

        while (true)
        {
            // Wait for 1 second between updates - this is similar to how BuildingUpgrade.UpgradeCountdown works
            yield return new WaitForSeconds(1f);

            Debug.Log($"ProcessTrainingQueue iteration - isFirstIteration: {isFirstIteration}");

            // Process each item in the queue
            if (trainingQueue.Count > 0)
            {
                Debug.Log($"Processing training queue. Items in queue: {trainingQueue.Count}, isFirstIteration: {isFirstIteration}");
                LogTrainingQueueState();

                // If this is the first iteration, ensure all training times are correct
                if (isFirstIteration)
                {
                    Debug.Log("First iteration of ProcessTrainingQueue, ensuring all training times are correct");

                    // Create a copy of the queue to safely iterate over
                    List<TrainingQueueItem> firstIterationCopy = new List<TrainingQueueItem>(trainingQueue);

                    // Check each item and ensure it has the correct training time
                    foreach (TrainingQueueItem item in firstIterationCopy)
                    {
                        // Calculate the correct training time directly
                        float correctBaseTime = 6f * Mathf.Max(1, item.Troop.Level) * Mathf.Max(1, item.Troop.Level) + 6f;
                        float correctTotalTime = correctBaseTime * item.Count;

                        // Ensure the training time is at least 12 seconds
                        correctTotalTime = Mathf.Max(12f, correctTotalTime);

                        // Check if the current time is significantly different from the correct time
                        if (item.TotalTime < 10f || Mathf.Abs(item.TotalTime - correctTotalTime) > 5f)
                        {
                            float oldTotalTime = item.TotalTime;
                            float oldRemainingTime = item.RemainingTime;

                            // Calculate what percentage of the training is complete
                            float progressPercentage = (oldTotalTime > 0f) ? (1f - oldRemainingTime / oldTotalTime) : 0f;

                            // Set the correct total time
                            item.TotalTime = correctTotalTime;

                            // Set the remaining time based on the progress percentage
                            item.RemainingTime = (1f - progressPercentage) * correctTotalTime;

                            // Ensure the remaining time is at least 12 seconds
                            item.RemainingTime = Mathf.Max(12f, item.RemainingTime);

                            Debug.LogWarning($"First iteration: Fixed training time for {item.Troop.Name} x{item.Count}. " +
                                            $"Total: {oldTotalTime} -> {item.TotalTime}, " +
                                            $"Remaining: {oldRemainingTime} -> {item.RemainingTime}");
                        }
                    }

                    // Set the flag to false after the first iteration
                    isFirstIteration = false;

                    // Log the state of the queue after fixing training times
                    Debug.Log("Queue state after fixing training times:");
                    LogTrainingQueueState();
                }

                // Create a list to collect items that need to be completed
                List<TrainingQueueItem> itemsToComplete = new List<TrainingQueueItem>();

                // Create a copy of the queue to safely iterate over
                List<TrainingQueueItem> queueCopy = new List<TrainingQueueItem>(trainingQueue);

                // First pass: update remaining times
                foreach (TrainingQueueItem item in queueCopy)
                {
                    if (item == null) continue;

                    // Store the old remaining time for logging
                    float oldRemainingTime = item.RemainingTime;

                    // Reduce the remaining time by 1 second
                    item.RemainingTime -= 1f;

                    // Ensure it doesn't go below 0
                    item.RemainingTime = Mathf.Max(0f, item.RemainingTime);

                    // Log the remaining time for each item
                    Debug.Log($"Training item: {item.Troop.Name} x{item.Count}, Remaining time: {oldRemainingTime} -> {item.RemainingTime}");

                    // Check if training is complete
                    if (item.RemainingTime <= 0 && trainingQueue.Contains(item))
                    {
                        Debug.Log($"Training complete for {item.Troop.Name} x{item.Count}, adding to completion list");
                        itemsToComplete.Add(item);
                    }
                }

                // Second pass: complete training for finished items
                if (itemsToComplete.Count > 0)
                {
                    Debug.Log($"Found {itemsToComplete.Count} items to complete");

                    // Process each completed item
                    foreach (TrainingQueueItem item in itemsToComplete)
                    {
                        // Only process if the item is still in the queue
                        if (trainingQueue.Contains(item))
                        {
                            Debug.Log($"Completing training for {item.Troop.Name} x{item.Count}");

                            // Mark the process as completed in NotificationManager
                            if (NotificationManager.Instance != null)
                            {
                                string processId = $"Training_{item.Troop.Type}_{item.Troop.Level}";
                                Debug.Log($"Marking process as completed in NotificationManager: {processId}");
                                NotificationManager.Instance.MarkProcessCompleted(processId);
                            }

                            // Complete the training (this will remove the item from the queue)
                            CompleteTraining(item);
                        }
                    }

                    // Log the state of the queue after processing
                    Debug.Log("Queue state after processing completions:");
                    LogTrainingQueueState();
                }
            }
        }
    }

    public List<TrainingQueueItem> GetTrainingQueue()
    {
        return trainingQueue;
    }

    // Debug method to log the current state of the training queue
    public void LogTrainingQueueState()
    {
        Debug.Log($"=== TRAINING QUEUE STATE ===");
        Debug.Log($"Queue contains {trainingQueue.Count} items");

        for (int i = 0; i < trainingQueue.Count; i++)
        {
            TrainingQueueItem item = trainingQueue[i];
            if (item != null)
            {
                Debug.Log($"Item {i}: {item.Troop.Name} x{item.Count}, Remaining: {item.RemainingTime}/{item.TotalTime} seconds, Progress: {item.Progress * 100:F1}%");
            }
            else
            {
                Debug.Log($"Item {i}: NULL");
            }
        }

        Debug.Log($"=== END QUEUE STATE ===");
    }

    // Troop upgrade methods
    public bool CanUpgradeTroop(TroopSO currentTroop, TroopSO targetTroop, int count)
    {
        // Check if we have enough troops to upgrade
        if (GetTroopCount(currentTroop.Type, currentTroop.Level) < count)
        {
            Debug.Log($"Cannot upgrade {currentTroop.Name}: Not enough troops");
            return false;
        }

        // Check if the target troop is unlocked
        if (!IsTroopUnlocked(targetTroop))
        {
            Debug.Log($"Cannot upgrade to {targetTroop.Name}: Troop is locked");
            return false;
        }

        // Calculate upgrade cost (percentage of the target troop's cost)
        float upgradeCostFactor = CalculateUpgradeCost(currentTroop, targetTroop) / 100f;
        int foodCost = Mathf.RoundToInt(targetTroop.FoodCost * upgradeCostFactor);
        int woodCost = Mathf.RoundToInt(targetTroop.WoodCost * upgradeCostFactor);
        int metalCost = Mathf.RoundToInt(targetTroop.MetalCost * upgradeCostFactor);

        // Check if there are enough resources
        if (!GameManager.Instance.HasEnoughResources(foodCost, woodCost, metalCost))
        {
            Debug.Log($"Cannot upgrade to {targetTroop.Name}: Not enough resources");
            return false;
        }

        return true;
    }

    public float CalculateUpgradeCost(TroopSO currentTroop, TroopSO targetTroop)
    {
        // Calculate upgrade cost as a percentage of the target troop's cost
        // For example, upgrading from level 1 to level 2 might cost 50% of the level 2 troop's cost
        int levelDifference = targetTroop.Level - currentTroop.Level;

        if (levelDifference <= 0)
        {
            Debug.LogError("Target troop level must be higher than current troop level");
            return 0f;
        }

        // Base cost is 50% of the target troop's cost, plus 10% for each additional level difference
        float costPercentage = 50f + (levelDifference - 1) * 10f;

        // Cap at 90%
        return Mathf.Min(costPercentage, 90f);
    }

    public void UpgradeTroop(TroopSO currentTroop, TroopSO targetTroop, int count)
    {
        if (!CanUpgradeTroop(currentTroop, targetTroop, count))
        {
            return;
        }

        // Calculate upgrade cost
        float upgradeCostFactor = CalculateUpgradeCost(currentTroop, targetTroop) / 100f;
        int foodCost = Mathf.RoundToInt(targetTroop.FoodCost * upgradeCostFactor);
        int woodCost = Mathf.RoundToInt(targetTroop.WoodCost * upgradeCostFactor);
        int metalCost = Mathf.RoundToInt(targetTroop.MetalCost * upgradeCostFactor);

        // Spend resources
        GameManager.Instance.SpendResources(foodCost, woodCost, metalCost);

        // Remove current troops
        troopCounts[currentTroop.Type][currentTroop.Level] -= count;

        // Add upgraded troops
        AddTroops(targetTroop.Type, targetTroop.Level, count);

        // Update battle power
        int oldBP = currentTroop.BattlePower * count;
        int newBP = targetTroop.BattlePower * count;
        int bpDifference = newBP - oldBP;

        if (bpDifference > 0)
        {
            GameManager.Instance.AddBattlePower(bpDifference);
        }

        Debug.Log($"Upgraded {count} {currentTroop.Name} to {targetTroop.Name}. BP difference: {bpDifference}");

        // Save troop counts
        SaveTroopCounts();
    }
}

[System.Serializable]
public class TrainingQueueItem
{
    public TroopSO Troop;
    public int Count;
    public float RemainingTime;
    public float TotalTime;
    public float Progress => 1f - (RemainingTime / TotalTime);
}
