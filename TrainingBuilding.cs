using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Handles the connection between a training building (barracks) and the training system.
/// This script should be attached to Infantry, Rider, and Ranged barracks buildings.
/// </summary>
public class TrainingBuilding : MonoBehaviour
{
    public enum BarracksType
    {
        Infantry,
        Rider,
        Ranged
    }

    [Header("Barracks Settings")]
    public BarracksType barracksType;

    [Header("Training Capabilities")]
    public float baseTrainingSpeedBonus = 5f; // Percentage bonus to training speed at level 1
    public float trainingSpeedBonusGrowthFactor = 1.2f; // How much the bonus grows per level
    public int baseTrainingCapacityBonus = 1; // Additional training capacity at level 1
    public float trainingCapacityBonusGrowthFactor = 1.5f; // How much the capacity bonus grows per level

    private BuildingUpgrade buildingUpgrade;
    private BuildingCapabilities buildingCapabilities;

    private void Awake()
    {
        buildingUpgrade = GetComponent<BuildingUpgrade>();
        buildingCapabilities = GetComponent<BuildingCapabilities>();

        if (buildingUpgrade == null)
        {
            Debug.LogError($"TrainingBuilding on {gameObject.name} requires a BuildingUpgrade component!");
            return;
        }

        if (buildingCapabilities == null)
        {
            Debug.LogError($"TrainingBuilding on {gameObject.name} requires a BuildingCapabilities component!");
            return;
        }
    }

    private void Start()
    {
        // Initialize capabilities
        InitializeCapabilities();

        // Update the TrainingManager with the current building level
        UpdateTrainingManager();

        // Subscribe to building upgrade events
        buildingUpgrade.OnBuildingUpgraded += HandleBuildingUpgraded;
    }

    private void OnDestroy()
    {
        // Unsubscribe from events
        if (buildingUpgrade != null)
        {
            buildingUpgrade.OnBuildingUpgraded -= HandleBuildingUpgraded;
        }
    }

    private void InitializeCapabilities()
    {
        // Add training speed capability
        buildingCapabilities.EnsureCapability(
            BuildingCapabilityType.TrainingSpeed,
            CalculateTrainingSpeedBonus(buildingUpgrade.CurrentLevel)
        );

        // Add training capacity capability
        buildingCapabilities.EnsureCapability(
            BuildingCapabilityType.TrainingCapacity,
            CalculateTrainingCapacityBonus(buildingUpgrade.CurrentLevel)
        );

        Debug.Log($"{gameObject.name} initialized with training speed bonus: {CalculateTrainingSpeedBonus(buildingUpgrade.CurrentLevel)}% " +
                 $"and training capacity bonus: {CalculateTrainingCapacityBonus(buildingUpgrade.CurrentLevel)}");
    }

    private void HandleBuildingUpgraded(int newLevel)
    {
        // Update capabilities based on new level
        UpdateCapabilities(newLevel);

        // Update the TrainingManager
        UpdateTrainingManager();

        Debug.Log($"{gameObject.name} upgraded to level {newLevel}. Updated training capabilities.");
    }

    private void UpdateCapabilities(int level)
    {
        // Update training speed capability
        float trainingSpeedBonus = CalculateTrainingSpeedBonus(level);
        foreach (var capability in buildingCapabilities.capabilities)
        {
            if (capability.capabilityType == BuildingCapabilityType.TrainingSpeed)
            {
                capability.baseValue = trainingSpeedBonus;
                break;
            }
        }

        // Update training capacity capability
        int trainingCapacityBonus = CalculateTrainingCapacityBonus(level);
        foreach (var capability in buildingCapabilities.capabilities)
        {
            if (capability.capabilityType == BuildingCapabilityType.TrainingCapacity)
            {
                capability.baseValue = trainingCapacityBonus;
                break;
            }
        }

        // Ensure the BuildingCapabilitiesManager is updated
        if (BuildingCapabilitiesManager.Instance != null)
        {
            BuildingCapabilitiesManager.Instance.RecalculateCapabilities();
        }
    }

    private void UpdateTrainingManager()
    {
        if (TrainingManager.Instance == null)
        {
            Debug.LogWarning("TrainingManager instance not found! Trying to find or create one...");

            // Try to find the Training Manager in the scene
            GameObject trainingManager = GameObject.Find("TrainingManager");
            if (trainingManager != null)
            {
                // Make sure it's active
                if (!trainingManager.activeSelf)
                {
                    trainingManager.SetActive(true);
                    Debug.Log("Found and activated TrainingManager");
                }
                else
                {
                    Debug.Log("Found TrainingManager, but it was already active");
                }
            }
            else
            {
                // Try to find by type
                TrainingManager existingManager = FindAnyObjectByType<TrainingManager>();
                if (existingManager != null)
                {
                    trainingManager = existingManager.gameObject;

                    // Make sure it's active
                    if (!trainingManager.activeSelf)
                    {
                        trainingManager.SetActive(true);
                        Debug.Log("Found and activated TrainingManager by type");
                    }
                    else
                    {
                        Debug.Log("Found TrainingManager by type, but it was already active");
                    }
                }
                else
                {
                    // Create a new TrainingManager
                    Debug.Log("Creating a new TrainingManager");
                    trainingManager = new GameObject("TrainingManager");
                    trainingManager.AddComponent<TrainingManager>();
                    DontDestroyOnLoad(trainingManager);
                }
            }

            // Check if we now have a TrainingManager instance
            if (TrainingManager.Instance == null)
            {
                Debug.LogError("Failed to find or create TrainingManager instance!");
                return;
            }
        }

        // Update the appropriate barracks level in the TrainingManager
        switch (barracksType)
        {
            case BarracksType.Infantry:
                TrainingManager.Instance.infantryBarracksLevel = buildingUpgrade.CurrentLevel;
                break;
            case BarracksType.Rider:
                TrainingManager.Instance.riderBarracksLevel = buildingUpgrade.CurrentLevel;
                break;
            case BarracksType.Ranged:
                TrainingManager.Instance.rangedBarracksLevel = buildingUpgrade.CurrentLevel;
                break;
        }

        // Update training capacity in TrainingManager based on all barracks
        UpdateTrainingCapacity();

        Debug.Log($"Updated TrainingManager with {barracksType} barracks level: {buildingUpgrade.CurrentLevel}");
    }

    private void UpdateTrainingCapacity()
    {
        // Check if TrainingManager instance exists
        if (TrainingManager.Instance == null)
        {
            Debug.LogWarning("TrainingManager instance not found in UpdateTrainingCapacity!");
            return;
        }

        // Get total training capacity bonus from all barracks
        int totalCapacityBonus = 0;

        // Find all training buildings in the scene
        TrainingBuilding[] trainingBuildings = FindObjectsByType<TrainingBuilding>(FindObjectsSortMode.None);
        foreach (var building in trainingBuildings)
        {
            if (building != null && building.buildingUpgrade != null)
            {
                // Calculate capacity bonus for each building
                totalCapacityBonus += building.CalculateTrainingCapacityBonus(building.buildingUpgrade.CurrentLevel);
            }
        }

        // Update the TrainingManager
        TrainingManager.Instance.SetAdditionalTrainingCapacity(totalCapacityBonus);

        Debug.Log($"Updated TrainingManager capacity bonus: {totalCapacityBonus}");
    }

    private float CalculateTrainingSpeedBonus(int level)
    {
        // Calculate training speed bonus based on level
        return baseTrainingSpeedBonus * Mathf.Pow(trainingSpeedBonusGrowthFactor, level - 1);
    }

    private int CalculateTrainingCapacityBonus(int level)
    {
        // Calculate training capacity bonus based on level
        return Mathf.RoundToInt(baseTrainingCapacityBonus * Mathf.Pow(trainingCapacityBonusGrowthFactor, level - 1));
    }
}
