using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Integrates the notification system with the hero search system.
/// Attach this to hero search buttons or objects that should show notifications.
/// </summary>
public class HeroSearchNotificationIntegration : MonoBehaviour
{
    [Header("Hero Search Settings")]
    [SerializeField] private Button searchButton;
    [SerializeField] private bool isAdvancedSearch = false;

    private NotificationSystem notificationSystem;
    private HeroSearch heroSearch;

    private void Awake()
    {
        // Add NotificationSystem component if it doesn't exist
        notificationSystem = GetComponent<NotificationSystem>();
        if (notificationSystem == null)
        {
            notificationSystem = gameObject.AddComponent<NotificationSystem>();
        }

        // Set process type to HeroSearch
        notificationSystem.SetProcessType("HeroSearch");

        // Set target ID based on search type
        notificationSystem.SetTargetId(isAdvancedSearch ? "HeroSearch_Advanced" : "HeroSearch_Basic");

        // Find HeroSearch component
        heroSearch = FindAnyObjectByType<HeroSearch>();
    }

    private void Start()
    {
        // Set custom availability check
        notificationSystem.SetCustomAvailabilityCheck(CheckHeroSearchAvailability);

        // Subscribe to button click event if button is assigned
        if (searchButton != null)
        {
            searchButton.onClick.AddListener(OnSearchButtonClicked);
        }

        // Subscribe to notification status changed event
        if (NotificationManager.Instance != null)
        {
            NotificationManager.Instance.OnNotificationStatusChanged += OnNotificationStatusChanged;
        }
    }

    private void OnDestroy()
    {
        // Unsubscribe from notification status changed event
        if (NotificationManager.Instance != null)
        {
            NotificationManager.Instance.OnNotificationStatusChanged -= OnNotificationStatusChanged;
        }

        // Unsubscribe from button click event
        if (searchButton != null)
        {
            searchButton.onClick.RemoveListener(OnSearchButtonClicked);
        }
    }

    /// <summary>
    /// Check if hero search is available
    /// </summary>
    private bool CheckHeroSearchAvailability()
    {
        if (heroSearch == null)
        {
            return false;
        }

        // Check if search is in progress using reflection or a property
        bool isSearchInProgress = false;
        try
        {
            // Try to get the IsSearchInProgress property using reflection
            var searchInProgressProperty = heroSearch.GetType().GetProperty("IsSearchInProgress");
            if (searchInProgressProperty != null)
            {
                isSearchInProgress = (bool)searchInProgressProperty.GetValue(heroSearch);
            }
            else
            {
                // Try to get a field or method if property doesn't exist
                var searchInProgressField = heroSearch.GetType().GetField("isSearchInProgress",
                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (searchInProgressField != null)
                {
                    isSearchInProgress = (bool)searchInProgressField.GetValue(heroSearch);
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"Failed to check if hero search is in progress: {e.Message}");
            // Default to false if we can't determine
            isSearchInProgress = false;
        }

        if (isSearchInProgress)
        {
            return false;
        }

        // Check if we have enough shards
        if (isAdvancedSearch)
        {
            // Check for advanced search shards
            int advancedShards = InventorySystem.Instance.GetItemQuantityByTag("StableShard");
            return advancedShards > 0;
        }
        else
        {
            // Check for basic search shards or free searches
            int basicShards = InventorySystem.Instance.GetItemQuantityByTag("CorruptedShard");

            // Try to get remaining basic searches using reflection
            int remainingBasicSearches = 0;
            try
            {
                var remainingSearchesProperty = heroSearch.GetType().GetProperty("RemainingBasicSearches");
                if (remainingSearchesProperty != null)
                {
                    remainingBasicSearches = (int)remainingSearchesProperty.GetValue(heroSearch);
                }
                else
                {
                    // Try to get a field if property doesn't exist
                    var remainingSearchesField = heroSearch.GetType().GetField("remainingBasicSearches",
                        System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (remainingSearchesField != null)
                    {
                        remainingBasicSearches = (int)remainingSearchesField.GetValue(heroSearch);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to get remaining basic searches: {e.Message}");
                // Default to 0 if we can't determine
                remainingBasicSearches = 0;
            }

            return basicShards > 0 || remainingBasicSearches > 0;
        }
    }

    /// <summary>
    /// Handle search button click
    /// </summary>
    private void OnSearchButtonClicked()
    {
        // Force check notification status after button click
        notificationSystem.ForceCheckNotification();
    }

    /// <summary>
    /// Handle notification status changed event
    /// </summary>
    private void OnNotificationStatusChanged(string targetId, bool isActive)
    {
        // Only handle events for this target
        if (targetId != notificationSystem.TargetId)
        {
            return;
        }

        // Update button interactable state if button is assigned
        if (searchButton != null)
        {
            searchButton.interactable = isActive;
        }
    }
}
