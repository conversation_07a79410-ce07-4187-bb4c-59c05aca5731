using UnityEngine;
using TMPro;

[ExecuteInEditMode] // Allows the script to run in the Editor without Play Mode
public class ApplyDefaultTMPFont : MonoBehaviour
{
    [ContextMenu("Apply Default TMP Font")] // Adds an option in the component's right-click menu
    void ApplyFont()
    {
        // Get the default TMP font asset from Project Settings
        TMP_FontAsset defaultFont = TMP_Settings.defaultFontAsset;

        if (defaultFont == null)
        {
            Debug.LogWarning("No default TMP font asset found! Set it in Project Settings > TextMeshPro.");
            return;
        }

        // Find all existing TMP_Text components in the scene
        TMP_Text[] texts = FindObjectsByType<TMP_Text>(FindObjectsSortMode.None);

        foreach (TMP_Text text in texts)
        {
            text.font = defaultFont;
        }

        Debug.Log($"Updated {texts.Length} TextMeshPro text elements to the default font.");
    }
}
