using UnityEngine;
using UnityEngine.UI;
using TMPro;
using HeroSystem;

public class HeroThumbnailItem : MonoBehaviour
{
    [SerializeField] private Image thumbnailImage;
    [SerializeField] private TextMeshProUGUI heroNameText;
    [SerializeField] private TextM<PERSON><PERSON><PERSON>UGUI expLevelText; // New UI element for hero's experience level
    [SerializeField] private Image rankImage; // New UI element for hero's rank image
    [SerializeField] private Button selectButton;
    [SerializeField] private Image lockIcon;
    [SerializeField] private Image frameImage;

    private HeroData heroData;
    private HeroSelector heroSelector;

    public void Initialize(HeroData heroData, HeroSelector heroSelector)
    {
        this.heroData = heroData;
        this.heroSelector = heroSelector;

        thumbnailImage.sprite = heroData.HeroThumbnail;
        heroNameText.text = heroData.HeroName;
        UpdateUI(); // Update the UI elements

        if (heroData.IsLocked)
        {
            thumbnailImage.color = Color.grey; // Grey out the thumbnail
            lockIcon.gameObject.SetActive(true);
        }
        else
        {
            thumbnailImage.color = Color.white;
            lockIcon.gameObject.SetActive(false);
        }

        selectButton.onClick.AddListener(OnThumbnailClicked);
    }

    private void OnThumbnailClicked()
    {
        heroSelector.OnHeroSelected(heroData, this);
    }

    public void SetSelected(bool isSelected)
    {
        frameImage.enabled = isSelected; // Enable or disable the frame
    }

    public void UpdateUI()
    {
        expLevelText.text = heroData.ExpLevel.ToString(); // Update the hero's experience level
        rankImage.sprite = GetRankImage(heroData.Rank); // Update the hero's rank image
    }

    private Sprite GetRankImage(int rank)
    {
        // Implement this method to return the appropriate rank image based on the hero's rank
        // For example, you can use a switch statement or an array of rank images
        switch (rank)
        {
            case 1:
                return Resources.Load<Sprite>("RankImages/Rank1");
            case 2:
                return Resources.Load<Sprite>("RankImages/Rank2");
            case 3:
                return Resources.Load<Sprite>("RankImages/Rank3");
            case 4:
                return Resources.Load<Sprite>("RankImages/Rank4");
            case 5:
                return Resources.Load<Sprite>("RankImages/Rank5");
            case 6:
                return Resources.Load<Sprite>("RankImages/Rank6");
            // Add more cases as needed
            default:
                return null;
        }
    }
}
