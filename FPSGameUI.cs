using UnityEngine;
using UnityEngine.UI;
using TMPro;
using UnityEngine.SceneManagement;
using System.Collections;
using HeroSystem;

public class FPSGameUI : MonoBehaviour
{
    [Header("Hero Info UI")]
    [SerializeField] private GameObject heroInfoPanel;
    [SerializeField] private Image heroThumbnail;
    [SerializeField] private TextMeshProUGUI heroNameText;
    [SerializeField] private Slider healthSlider;
    [SerializeField] private TextMeshProUGUI healthText;

    [Header("Skills UI")]
    [SerializeField] private GameObject skillsPanel;
    [SerializeField] private GameObject[] skillButtons; // Array of 3 skill buttons
    [SerializeField] private Image[] skillImages;
    [SerializeField] private TextMeshProUGUI[] skillLevelTexts;
    [SerializeField] private TextMeshProUGUI[] skillNameTexts;
    [SerializeField] private TextMeshProUGUI[] skillBonusTexts;
    [SerializeField] private Image[] skillCooldownImages;
    [SerializeField] private But<PERSON> exitButton; // Add this field

    [Header("Confirmation UI")]
    [SerializeField] private GameObject confirmationPanel;
    [SerializeField] private Button quitButton;
    [SerializeField] private Button returnButton;

    private HeroData currentHero;
    private PlayerHealth playerHealth;
    private FPS_Shooting shooting;
    private bool isPaused;
    private float[] skillCooldowns = new float[3];
    private bool[] skillsActive = new bool[3];

    private void Start()
    {
        // Get the current hero from GameManager
        currentHero = GameManager.Instance.SelectedHero;
        if (currentHero == null)
        {
            Debug.LogError("No hero selected in GameManager!");
            return;
        }

        // Validate UI components
        if (healthSlider == null)
        {
            Debug.LogError("Health Slider not assigned in Inspector!");
            return;
        }
        if (healthText == null)
        {
            Debug.LogError("Health Text not assigned in Inspector!");
            return;
        }

        // Get components
        playerHealth = FindFirstObjectByType<PlayerHealth>();
        if (playerHealth == null)
        {
            Debug.LogError("PlayerHealth component not found in scene!");
            return;
        }

        shooting = FindFirstObjectByType<FPS_Shooting>();
        if (shooting == null)
        {
            Debug.LogError("FPS_Shooting component not found in scene!");
            return;
        }

        // Initialize UI
        InitializeHeroUI();
        InitializeSkillsUI();

        // Add listener for exit button
        if (exitButton != null)
        {
            exitButton.onClick.AddListener(ShowConfirmationPanel);
        }
        else
        {
            Debug.LogError("Exit Button not assigned in Inspector!");
        }

        // Setup confirmation panel buttons
        quitButton.onClick.AddListener(QuitGame);
        returnButton.onClick.AddListener(ReturnToGame);

        // Hide panels initially
        skillsPanel.SetActive(false);
        confirmationPanel.SetActive(false);
    }

    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            ToggleSkillsUI();
        }

        // Only update UI if components are available
        if (playerHealth != null && healthSlider != null && healthText != null)
        {
            UpdateSkillCooldowns();
            UpdateHealthUI();
        }
    }

    private void InitializeHeroUI()
    {
        if (currentHero != null)
        {
            heroThumbnail.sprite = currentHero.HeroThumbnail;
            heroNameText.text = currentHero.HeroName;

            // Initialize PlayerHealth with hero's stats
            playerHealth.maxHealth = currentHero.BaseStats.HP;
            shooting.damage = currentHero.BaseStats.Attack;
        }
    }

    private void InitializeSkillsUI()
    {
        for (int i = 0; i < 3; i++)
        {
            if (i < currentHero.Skills.Length)
            {
                var skill = currentHero.Skills[i];
                skillImages[i].sprite = skill.skillIcon;
                skillLevelTexts[i].text = $"{currentHero.GetSkillLevel(i)}";
                skillNameTexts[i].text = skill.skillName;

                // Set bonus text
                string bonusText = "";
                if (skill.attackBonus > 0) bonusText += $"Attack +{skill.attackBonus}\n";
                if (skill.defenseBonus > 0) bonusText += $"Defense +{skill.defenseBonus}\n";
                if (skill.healthBonus > 0) bonusText += $"Health +{skill.healthBonus}";
                skillBonusTexts[i].text = bonusText;

                int index = i; // Capture for lambda
                skillButtons[i].GetComponent<Button>().onClick.AddListener(() => ActivateSkill(index));
            }
        }
    }

    private void ToggleSkillsUI()
    {
        isPaused = !isPaused;
        Time.timeScale = isPaused ? 0 : 1;
        skillsPanel.SetActive(isPaused);

        // Disable/enable shooting when UI is open/closed
        shooting.SetCanShoot(!isPaused);

        // Optional: Show/hide cursor when UI is open/closed
        Cursor.visible = isPaused;
        Cursor.lockState = isPaused ? CursorLockMode.None : CursorLockMode.Locked;
    }


    private void ActivateSkill(int skillIndex)
    {
        if (skillCooldowns[skillIndex] <= 0 && !skillsActive[skillIndex])
        {
            var skill = currentHero.Skills[skillIndex];

            // Apply bonuses
            playerHealth.maxHealth += (int)skill.healthBonus;
            shooting.damage += (int)skill.attackBonus;

            // Start cooldown
            skillCooldowns[skillIndex] = 30f; // 30 seconds cooldown
            skillsActive[skillIndex] = true;

            StartCoroutine(DeactivateSkillAfterDuration(skillIndex, 10f)); // 10 seconds duration
        }
    }

    private IEnumerator DeactivateSkillAfterDuration(int skillIndex, float duration)
    {
        yield return new WaitForSeconds(duration);

        var skill = currentHero.Skills[skillIndex];
        playerHealth.maxHealth -= (int)skill.healthBonus;
        shooting.damage -= (int)skill.attackBonus;

        skillsActive[skillIndex] = false;
    }

    private void UpdateSkillCooldowns()
    {
        for (int i = 0; i < skillCooldowns.Length; i++)
        {
            if (skillCooldowns[i] > 0)
            {
                skillCooldowns[i] -= Time.deltaTime;
                skillCooldownImages[i].fillAmount = skillCooldowns[i] / 30f;
            }
        }
    }

    private void UpdateHealthUI()
    {
        if (playerHealth == null || healthSlider == null || healthText == null)
        {
            return;
        }

        healthSlider.value = (float)playerHealth.CurrentHealth / playerHealth.maxHealth;
        healthText.text = $"{playerHealth.CurrentHealth}/{playerHealth.maxHealth}";
    }

    private void QuitGame()
    {
        // Reset time scale before loading new scene
        Time.timeScale = 1f;

        // Reset cursor state
        Cursor.visible = true;
        Cursor.lockState = CursorLockMode.None;

        // Level stays uncleared since we're quitting early
        SceneManager.LoadScene("Settlement");
    }

    private void ShowConfirmationPanel()
    {
        confirmationPanel.SetActive(true);
    }

    private void ReturnToGame()
    {
        confirmationPanel.SetActive(false);
        ToggleSkillsUI();
    }
}
