using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Integrates the notification system with the hero upgrade system.
/// Attach this to hero upgrade buttons or objects that should show notifications.
/// </summary>
public class HeroUpgradeNotificationIntegration : MonoBehaviour
{
    public enum HeroUpgradeType
    {
        Experience,
        Rank,
        Skill
    }

    [Header("Hero Upgrade Settings")]
    [SerializeField] private HeroUpgradeType upgradeType = HeroUpgradeType.Experience;
    [SerializeField] private Button upgradeButton;
    [SerializeField] private int heroId; // ID of the hero
    [SerializeField] private int currentLevel; // Current level of hero/rank/skill
    [SerializeField] private int maxLevel; // Max level of hero/rank/skill
    [SerializeField] private int skillIndex = -1; // For skill upgrades, which skill to upgrade

    [Header("Resource Requirements")]
    [SerializeField] private int goldCost;
    [SerializeField] private int gemCost;

    private NotificationSystem notificationSystem;

    private void Awake()
    {
        // Add NotificationSystem component if it doesn't exist
        notificationSystem = GetComponent<NotificationSystem>();
        if (notificationSystem == null)
        {
            notificationSystem = gameObject.AddComponent<NotificationSystem>();
        }

        // All hero upgrades use the same process type
        notificationSystem.SetProcessType("HeroUpgrade");

        // Set target ID based on hero and upgrade type
        string targetId = $"Hero_{heroId}_{upgradeType}";

        // Add skill info if applicable
        if (upgradeType == HeroUpgradeType.Skill && skillIndex >= 0)
        {
            targetId += $"_Skill{skillIndex}";
        }

        notificationSystem.SetTargetId(targetId);
    }

    private void Start()
    {
        // Set custom availability check
        notificationSystem.SetCustomAvailabilityCheck(CheckHeroUpgradeAvailability);

        // Subscribe to button click event if button is assigned
        if (upgradeButton != null)
        {
            upgradeButton.onClick.AddListener(OnUpgradeButtonClicked);
        }

        // Subscribe to notification status changed event
        if (NotificationManager.Instance != null)
        {
            NotificationManager.Instance.OnNotificationStatusChanged += OnNotificationStatusChanged;
        }
    }

    private void OnDestroy()
    {
        // Unsubscribe from notification status changed event
        if (NotificationManager.Instance != null)
        {
            NotificationManager.Instance.OnNotificationStatusChanged -= OnNotificationStatusChanged;
        }

        // Unsubscribe from button click event
        if (upgradeButton != null)
        {
            upgradeButton.onClick.RemoveListener(OnUpgradeButtonClicked);
        }
    }

    /// <summary>
    /// Check if hero upgrade is available
    /// </summary>
    private bool CheckHeroUpgradeAvailability()
    {
        // Check if already at max level
        if (currentLevel >= maxLevel)
        {
            return false;
        }

        // Check if player has enough resources
        bool hasEnoughGold = GameManager.Instance.HasEnoughGold(goldCost);
        bool hasEnoughGems = true; // Assume gems are handled separately or not used

        if (gemCost > 0)
        {
            // If you have a gem system, check for gems here
            // hasEnoughGems = GameManager.Instance.HasEnoughGems(gemCost);
        }

        return hasEnoughGold && hasEnoughGems;
    }

    /// <summary>
    /// Handle upgrade button click
    /// </summary>
    private void OnUpgradeButtonClicked()
    {
        // Force check notification status after button click
        notificationSystem.ForceCheckNotification();
    }

    /// <summary>
    /// Handle notification status changed event
    /// </summary>
    private void OnNotificationStatusChanged(string targetId, bool isActive)
    {
        // Only handle events for this target
        if (targetId != notificationSystem.TargetId)
        {
            return;
        }

        // Update button interactable state if button is assigned
        if (upgradeButton != null)
        {
            upgradeButton.interactable = isActive;
        }
    }

    /// <summary>
    /// Set the current and max levels
    /// </summary>
    public void SetLevels(int current, int max)
    {
        currentLevel = current;
        maxLevel = max;

        // Force check notification status
        notificationSystem.ForceCheckNotification();
    }

    /// <summary>
    /// Set the resource costs
    /// </summary>
    public void SetCosts(int gold, int gems = 0)
    {
        goldCost = gold;
        gemCost = gems;

        // Force check notification status
        notificationSystem.ForceCheckNotification();
    }
}
