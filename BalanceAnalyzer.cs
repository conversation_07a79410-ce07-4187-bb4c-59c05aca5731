using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using HeroSystem;

public class BalanceAnalyzer : MonoBehaviour
{
    public GameBalanceData balanceData;

    [Header("Analysis Parameters")]
    [Range(0.1f, 10f)]
    public float resourceToTimeRatio = 1.0f;
    [Range(0.1f, 10f)]
    public float powerToResourceRatio = 1.0f;
    [Range(0.1f, 10f)]
    public float heroProgressionRate = 1.0f;
    [Range(0.1f, 10f)]
    public float buildingProgressionRate = 1.0f;
    [Range(0.1f, 10f)]
    public float researchProgressionRate = 1.0f;

    [Header("Target Progression Times (hours)")]
    public float earlyGameProgression = 24;
    public float midGameProgression = 72;
    public float lateGameProgression = 168;

    [Header("Analysis Results")]
    public List<BalanceIssue> detectedIssues = new List<BalanceIssue>();

    // Analysis methods
    public void AnalyzeGameBalance()
    {
        detectedIssues.Clear();

        AnalyzeHeroBalance();
        AnalyzeBuildingBalance();
        AnalyzeResearchBalance();
        AnalyzeResourceGeneration();
        AnalyzeProgressionCurve();

        Debug.Log($"Balance analysis complete. Found {detectedIssues.Count} potential issues.");
    }

    private void AnalyzeHeroBalance()
    {
        // Check for power outliers
        var heroes = balanceData.heroBalanceData;
        if (heroes.Count == 0) return;

        float avgPower = (float)heroes.Average(h => h.initialPower);
        float stdDevPower = CalculateStandardDeviation(heroes.Select(h => (float)h.initialPower).ToList());

        // Find heroes that are significantly stronger or weaker
        foreach (var hero in heroes)
        {
            float zScore = (hero.initialPower - avgPower) / stdDevPower;

            if (Mathf.Abs(zScore) > 2.0f)
            {
                detectedIssues.Add(new BalanceIssue
                {
                    issueType = BalanceIssueType.HeroPower,
                    severity = Mathf.Abs(zScore) - 1.5f,
                    description = $"Hero {hero.heroName} power ({hero.initialPower}) is {(zScore > 0 ? "significantly higher" : "significantly lower")} than average ({avgPower}).",
                    suggestion = $"Consider {(zScore > 0 ? "decreasing" : "increasing")} {hero.heroName}'s initial power to around {Mathf.RoundToInt(avgPower + (zScore > 0 ? stdDevPower : -stdDevPower))}."
                });
            }
        }

        // Check for troop modifier balance
        AnalyzeTroopModifiers(heroes);

        // Check for skill balance
        AnalyzeSkillBalance(heroes);
    }

    private void AnalyzeTroopModifiers(List<HeroBalanceData> heroes)
    {
        // Group heroes by type
        var infantryHeroes = heroes.Where(h => h.heroType == HeroType.Infantry).ToList();
        var riderHeroes = heroes.Where(h => h.heroType == HeroType.Rider).ToList();
        var rangedHeroes = heroes.Where(h => h.heroType == HeroType.Ranged).ToList();

        // Check if any type is significantly stronger
        float avgInfantryPower = infantryHeroes.Count > 0 ? (float)infantryHeroes.Average(h => h.initialPower) : 0;
        float avgRiderPower = riderHeroes.Count > 0 ? (float)riderHeroes.Average(h => h.initialPower) : 0;
        float avgRangedPower = rangedHeroes.Count > 0 ? (float)rangedHeroes.Average(h => h.initialPower) : 0;

        float maxAvgPower = Mathf.Max(avgInfantryPower, avgRiderPower, avgRangedPower);
        float minAvgPower = Mathf.Min(avgInfantryPower, avgRiderPower, avgRangedPower);

        if (maxAvgPower > 0 && minAvgPower > 0 && (maxAvgPower / minAvgPower) > 1.5f)
        {
            string strongestType = avgInfantryPower == maxAvgPower ? "Infantry" :
                                  (avgRiderPower == maxAvgPower ? "Rider" : "Ranged");

            string weakestType = avgInfantryPower == minAvgPower ? "Infantry" :
                                (avgRiderPower == minAvgPower ? "Rider" : "Ranged");

            detectedIssues.Add(new BalanceIssue
            {
                issueType = BalanceIssueType.TroopTypeBalance,
                severity = (maxAvgPower / minAvgPower) - 1.0f,
                description = $"{strongestType} heroes are significantly stronger than {weakestType} heroes on average.",
                suggestion = $"Consider boosting {weakestType} heroes' power or reducing {strongestType} heroes' power to achieve better balance."
            });
        }
    }

    private void AnalyzeSkillBalance(List<HeroBalanceData> heroes)
    {
        // Analyze skill effects and bonuses
        foreach (var hero in heroes)
        {
            foreach (var skill in hero.skills)
            {
                // Check for overpowered effects
                foreach (var effect in skill.effects)
                {
                    if (effect.effectType == SkillEffectType.Stun && effect.duration > 2)
                    {
                        detectedIssues.Add(new BalanceIssue
                        {
                            issueType = BalanceIssueType.SkillEffect,
                            severity = effect.duration - 1.5f,
                            description = $"Hero {hero.heroName}'s skill '{skill.skillName}' has a long stun duration of {effect.duration} turns.",
                            suggestion = $"Consider reducing the stun duration to 1-2 turns for better balance."
                        });
                    }

                    if (effect.effectType == SkillEffectType.AdditionalDamage && effect.effectValue > 200)
                    {
                        detectedIssues.Add(new BalanceIssue
                        {
                            issueType = BalanceIssueType.SkillEffect,
                            severity = (effect.effectValue - 150) / 50,
                            description = $"Hero {hero.heroName}'s skill '{skill.skillName}' has high additional damage of {effect.effectValue}.",
                            suggestion = $"Consider reducing the damage to around 150-200 for better balance."
                        });
                    }
                }
            }
        }
    }

    private void AnalyzeBuildingBalance()
    {
        var buildings = balanceData.buildingBalanceData;
        if (buildings.Count == 0) return;

        // Check for resource generation balance
        var resourceBuildings = buildings.Where(b => b.buildingType != BuildingUpgrade.BuildingType.NonGenerating).ToList();

        if (resourceBuildings.Count > 0)
        {
            float avgGeneration = resourceBuildings.Average(b => b.baseResourceGeneration);
            float avgGrowthFactor = resourceBuildings.Average(b => b.resourceGenerationGrowthFactor);

            // Check for buildings with significantly higher resource generation
            foreach (var building in resourceBuildings)
            {
                if (building.resourceGenerationGrowthFactor > avgGrowthFactor * 1.5f)
                {
                    detectedIssues.Add(new BalanceIssue
                    {
                        issueType = BalanceIssueType.ResourceGeneration,
                        severity = building.resourceGenerationGrowthFactor / avgGrowthFactor - 1.0f,
                        description = $"Building {building.buildingName} has a significantly higher resource generation growth factor ({building.resourceGenerationGrowthFactor}) than average ({avgGrowthFactor}).",
                        suggestion = $"Consider reducing the growth factor to around {avgGrowthFactor * 1.2f} for better balance."
                    });
                }
            }
        }

        // Check for upgrade cost vs. benefit balance
        foreach (var building in buildings)
        {
            float costGrowth = building.resourceGrowthFactor;
            float benefitGrowth = Mathf.Max(building.battlePowerGrowthFactor, building.welfareGrowthFactor, building.resourceGenerationGrowthFactor);

            if (costGrowth > benefitGrowth * 1.5f)
            {
                detectedIssues.Add(new BalanceIssue
                {
                    issueType = BalanceIssueType.UpgradeCostBenefit,
                    severity = costGrowth / benefitGrowth - 1.0f,
                    description = $"Building {building.buildingName} has costs growing faster ({costGrowth}) than benefits ({benefitGrowth}).",
                    suggestion = $"Consider reducing cost growth factor or increasing benefit growth factors for better progression."
                });
            }
        }
    }

    private void AnalyzeResearchBalance()
    {
        var researches = balanceData.researchBalanceData;
        if (researches.Count == 0) return;

        // Check for research time vs. benefit balance
        foreach (var research in researches)
        {
            // Calculate average bonus per tier
            float avgBonus = 0;
            if (research.tiers.Count > 0)
            {
                avgBonus = (float)research.tiers.Average(t => t.bonus);
            }

            // Check if research time is too high for the bonus
            float timeToBonus = research.startingTime / (avgBonus > 0 ? avgBonus : 1);
            float avgTimeToBonus = (float)researches.Average(r =>
                r.startingTime / (r.tiers.Count > 0 ? (float)r.tiers.Average(t => t.bonus) : 1));

            if (timeToBonus > avgTimeToBonus * 2)
            {
                detectedIssues.Add(new BalanceIssue
                {
                    issueType = BalanceIssueType.ResearchTimeValue,
                    severity = timeToBonus / avgTimeToBonus - 1.0f,
                    description = $"Research {research.researchName} has a high time-to-benefit ratio compared to other researches.",
                    suggestion = $"Consider reducing research time or increasing the bonus effects for better player satisfaction."
                });
            }
        }
    }

    private void AnalyzeResourceGeneration()
    {
        // Analyze if resource generation rates are balanced
        var resGen = balanceData.resourceGeneration;

        float avgGenRate = (resGen.foodGenerationRate + resGen.woodGenerationRate +
                           resGen.metalGenerationRate + resGen.goldGenerationRate) / 4;

        // Check for significant imbalances in generation rates
        if (resGen.foodGenerationRate < avgGenRate * 0.5f)
        {
            detectedIssues.Add(new BalanceIssue
            {
                issueType = BalanceIssueType.ResourceGeneration,
                severity = 1.0f - (resGen.foodGenerationRate / avgGenRate),
                description = $"Food generation rate ({resGen.foodGenerationRate}) is significantly lower than average ({avgGenRate}).",
                suggestion = $"Consider increasing food generation rate to around {avgGenRate * 0.8f} for better resource balance."
            });
        }

        if (resGen.woodGenerationRate < avgGenRate * 0.5f)
        {
            detectedIssues.Add(new BalanceIssue
            {
                issueType = BalanceIssueType.ResourceGeneration,
                severity = 1.0f - (resGen.woodGenerationRate / avgGenRate),
                description = $"Wood generation rate ({resGen.woodGenerationRate}) is significantly lower than average ({avgGenRate}).",
                suggestion = $"Consider increasing wood generation rate to around {avgGenRate * 0.8f} for better resource balance."
            });
        }

        if (resGen.metalGenerationRate < avgGenRate * 0.5f)
        {
            detectedIssues.Add(new BalanceIssue
            {
                issueType = BalanceIssueType.ResourceGeneration,
                severity = 1.0f - (resGen.metalGenerationRate / avgGenRate),
                description = $"Metal generation rate ({resGen.metalGenerationRate}) is significantly lower than average ({avgGenRate}).",
                suggestion = $"Consider increasing metal generation rate to around {avgGenRate * 0.8f} for better resource balance."
            });
        }
    }

    private void AnalyzeProgressionCurve()
    {
        // Analyze overall progression curve
        var upgradeScaling = balanceData.upgradeScaling;

        // Check if any scaling factor is too aggressive
        if (upgradeScaling.buildingResourceCostScaling > 1.8f)
        {
            detectedIssues.Add(new BalanceIssue
            {
                issueType = BalanceIssueType.ProgressionCurve,
                severity = upgradeScaling.buildingResourceCostScaling - 1.5f,
                description = $"Building resource cost scaling ({upgradeScaling.buildingResourceCostScaling}) is very steep.",
                suggestion = $"Consider reducing to around 1.4-1.6 for smoother progression curve."
            });
        }

        if (upgradeScaling.researchTimeScaling > 1.5f)
        {
            detectedIssues.Add(new BalanceIssue
            {
                issueType = BalanceIssueType.ProgressionCurve,
                severity = upgradeScaling.researchTimeScaling - 1.3f,
                description = $"Research time scaling ({upgradeScaling.researchTimeScaling}) is very steep.",
                suggestion = $"Consider reducing to around 1.2-1.4 for smoother progression curve."
            });
        }

        if (upgradeScaling.heroRankScaling > 4.0f)
        {
            detectedIssues.Add(new BalanceIssue
            {
                issueType = BalanceIssueType.ProgressionCurve,
                severity = upgradeScaling.heroRankScaling - 3.5f,
                description = $"Hero rank scaling ({upgradeScaling.heroRankScaling}) is very steep.",
                suggestion = $"Consider reducing to around 3.0-3.5 for smoother hero progression."
            });
        }
    }

    // Helper methods
    private float CalculateStandardDeviation(List<float> values)
    {
        if (values.Count <= 1) return 0;

        float avg = values.Average();
        float sumOfSquaresOfDifferences = values.Sum(val => (val - avg) * (val - avg));
        float standardDeviation = Mathf.Sqrt(sumOfSquaresOfDifferences / (values.Count - 1));

        return standardDeviation;
    }

    // Generate balance suggestions
    public List<BalanceSuggestion> GenerateBalanceSuggestions()
    {
        List<BalanceSuggestion> suggestions = new List<BalanceSuggestion>();

        // Sort issues by severity
        var sortedIssues = detectedIssues.OrderByDescending(i => i.severity).ToList();

        // Generate suggestions for top issues
        foreach (var issue in sortedIssues.Take(10))
        {
            suggestions.Add(new BalanceSuggestion
            {
                issueType = issue.issueType,
                description = issue.description,
                suggestion = issue.suggestion,
                priority = issue.severity > 2.0f ? SuggestionPriority.High :
                          (issue.severity > 1.0f ? SuggestionPriority.Medium : SuggestionPriority.Low)
            });
        }

        return suggestions;
    }
}

[Serializable]
public class BalanceIssue
{
    public BalanceIssueType issueType;
    public float severity; // 0-5 scale, higher is more severe
    public string description;
    public string suggestion;
}

[Serializable]
public class BalanceSuggestion
{
    public BalanceIssueType issueType;
    public string description;
    public string suggestion;
    public SuggestionPriority priority;
}

// Enums moved to BalanceEnums.cs
