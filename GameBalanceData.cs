using UnityEngine;
using System;
using System.Collections.Generic;
using HeroSystem;

[CreateAssetMenu(fileName = "GameBalanceData", menuName = "Game Balance/Balance Data")]
public class GameBalanceData : ScriptableObject
{
    [Header("Global Balance Parameters")]
    public float globalResourceMultiplier = 1.0f;
    public float globalTimeMultiplier = 1.0f;
    public float globalPowerMultiplier = 1.0f;

    [Header("Hero Balance")]
    public List<HeroBalanceData> heroBalanceData = new List<HeroBalanceData>();

    [Header("Building Balance")]
    public List<BuildingBalanceData> buildingBalanceData = new List<BuildingBalanceData>();

    [Header("Research Balance")]
    public List<ResearchBalanceData> researchBalanceData = new List<ResearchBalanceData>();

    [Header("Resource Generation")]
    public ResourceGenerationData resourceGeneration = new ResourceGenerationData();

    [Header("Upgrade Scaling")]
    public UpgradeScalingData upgradeScaling = new UpgradeScalingData();
}

[Serializable]
public class HeroBalanceData
{
    public string heroName;
    public HeroRarity rarity;
    public HeroType heroType;
    public int initialPower;
    public BaseStats baseStats;
    public TroopModifiers troopModifiers;
    public List<SkillBalanceData> skills = new List<SkillBalanceData>();

    // Progression parameters
    public float expScalingFactor = 1.0f;
    public float rankScalingFactor = 1.0f;
    public float powerScalingFactor = 1.0f;
}

[Serializable]
public class SkillBalanceData
{
    public string skillName;
    public float attackBonus;
    public float damageBonus;
    public float defenseBonus;
    public float healthBonus;
    public float marchCapacityBonus;
    public float rallyCapacityBonus;
    public List<SkillEffectData> effects = new List<SkillEffectData>();
}

[Serializable]
public class SkillEffectData
{
    public SkillEffectType effectType;
    public float effectValue;
    public float effectChance;
    public int duration;
}

[Serializable]
public class BuildingBalanceData
{
    public string buildingName;
    public BuildingUpgrade.BuildingType buildingType;
    public int maxLevel;

    // Base costs
    public int baseFoodCost;
    public int baseWoodCost;
    public int baseMetalCost;
    public int baseGoldCost;

    // Base values
    public float baseUpgradeTime;
    public int baseBattlePower;
    public int baseWelfare;
    public float baseResourceGeneration;

    // Scaling factors
    public float resourceGrowthFactor;
    public float timeGrowthFactor;
    public float battlePowerGrowthFactor;
    public float welfareGrowthFactor;
    public float resourceGenerationGrowthFactor;
}

[Serializable]
public class ResearchBalanceData
{
    public string researchName;
    public ResearchNode.NodeType nodeType;
    public ResearchNode.BonusType bonusType;
    public bool isPercentageBonus;

    // Base costs
    public int startingFoodCost;
    public int startingWoodCost;
    public int startingMetalCost;
    public int startingGoldCost;

    // Base values
    public float startingTime;
    public int startingPower;

    // Tiers
    public List<ResearchTierBalanceData> tiers = new List<ResearchTierBalanceData>();
}

[Serializable]
public class ResearchTierBalanceData
{
    public int tierNumber;
    public int maxLevel;
    public int bonus;
    public int requiredLabLevel;
}

[Serializable]
public class ResourceGenerationData
{
    public float foodGenerationRate = 1.0f;
    public float woodGenerationRate = 1.0f;
    public float metalGenerationRate = 1.0f;
    public float goldGenerationRate = 1.0f;
}

[Serializable]
public class UpgradeScalingData
{
    public float heroRankScaling = 3.6f;
    public float buildingUpgradeTimeScaling = 1.3f;
    public float buildingResourceCostScaling = 1.5f;
    public float researchTimeScaling = 1.3f;
    public float researchCostScaling = 1.3f;
}
