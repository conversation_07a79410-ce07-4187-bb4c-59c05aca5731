using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;


public class SpriteShakeAndScatter : MonoBehaviour
{
    [SerializeField] private Material glowMaterial; // Assign your glow material in inspector
    [SerializeField] private float minGlow = 0.5f;
    [SerializeField] private float maxGlow = 2f;
    [SerializeField] private float glowSpeed = 1f;
    [SerializeField] private Color glowColor = Color.white; // Added glow color property
    [SerializeField] private float glowOffset = 4f; // Added glow offset property
    [SerializeField] private float glowPower = 2f; // Added glow power property
    [SerializeField] private int glowingSpritesCount = 10; // Number of sprites that glow at once
    [SerializeField] private float glowCycleDuration = 0.5f; // How long before switching to new random sprites
    public float shakeDuration = 0.5f;
    public float shakeIntensity = 10f;
    public float scatterForce = 500f;
    
    private RectTransform[] childImages;
    private bool isEffectRunning = false;
    private Vector3[] originalPositions;
    private Quaternion[] originalRotations;
    private Vector3[] originalScales;
    private Vector2 originalAnchoredPosition;
    private Vector3 originalLocalPosition;
    private Vector2 originalSizeDelta;
    private Vector2 originalAnchorMin;
    private Vector2 originalAnchorMax;
    private Vector3 originalScale;
    private RectTransform parentRect;
    private Vector3 originalAnchoredPosition3D;
    private List<Coroutine> activeGlowCoroutines = new List<Coroutine>();

    void Awake()
    {
        parentRect = GetComponent<RectTransform>();
        originalAnchoredPosition3D = parentRect.anchoredPosition3D;
        originalLocalPosition = parentRect.localPosition;
        originalSizeDelta = parentRect.sizeDelta;
        originalAnchorMin = parentRect.anchorMin;
        originalAnchorMax = parentRect.anchorMax;
        originalScale = parentRect.localScale;
        RectTransform[] children = transform.GetComponentsInChildren<RectTransform>();
        childImages = new RectTransform[children.Length - 1];
        originalPositions = new Vector3[children.Length - 1];
        originalRotations = new Quaternion[children.Length - 1];
        originalScales = new Vector3[children.Length - 1];
        
        int index = 0;
        foreach (RectTransform child in children)
        {
            if (child != transform)
            {
                childImages[index] = child;
                originalPositions[index] = child.anchoredPosition3D;
                originalRotations[index] = child.localRotation;
                originalScales[index] = child.localScale;
                index++;
            }
        }

        foreach (RectTransform child in childImages)
        {
            Image image = child.GetComponent<Image>();
            if (image != null)
            {
                Material newMaterial = new Material(glowMaterial);
                newMaterial.SetColor("_GlowColor", glowColor);
                newMaterial.SetFloat("_GlowOffset", glowOffset);
                newMaterial.SetFloat("_GlowPower", glowPower);
                newMaterial.SetFloat("_GlowIntensity", 0f); // Start with no glow
                image.material = newMaterial;
            }
        }
    }

    private void Start()
    {
        StartCoroutine(CycleRandomGlows());
    }

    public void StartEffect()
    {
        if (!isEffectRunning)
        {
            isEffectRunning = true;
            StartCoroutine(ShakeThenScatter());
        }
    }

    private IEnumerator ShakeThenScatter()
    {
        Vector3 shakeStartPosition = parentRect.anchoredPosition3D;
        
        // Shake the parent object
        float elapsed = 0f;
        while (elapsed < shakeDuration)
        {
            parentRect.anchoredPosition3D = shakeStartPosition + (Vector3)Random.insideUnitCircle * shakeIntensity;
            yield return new WaitForEndOfFrame();
            elapsed += Time.deltaTime;
        }

        // Reset parent position
        parentRect.anchoredPosition3D = originalAnchoredPosition3D;
        yield return new WaitForSeconds(0.1f);

        // Scatter the pieces
        foreach (RectTransform child in childImages)
        {
            Vector2 randomDirection = Random.insideUnitCircle.normalized;
            StartCoroutine(ScatterPiece(child, randomDirection * scatterForce));
        }

        // Wait for scatter duration before resetting
        yield return new WaitForSeconds(1.5f);
        ResetEffect();
        
        isEffectRunning = false;
    }

    private void OnEnable()
    {
        ResetEffect();
    }

    private IEnumerator ScatterPiece(Transform piece, Vector2 force)
    {
        float duration = 1.5f;
        float elapsed = 0f;
        Vector3 startPos = piece.localPosition;
        Vector3 targetPos = startPos + (Vector3)force;
        Image image = piece.GetComponent<Image>();

        while (elapsed < duration)
        {
            float t = elapsed / duration;
            piece.localPosition = Vector3.Lerp(startPos, targetPos, t);
            if (image != null)
            {
                Color color = image.color;
                color.a = Mathf.Lerp(1f, 0f, t);
                image.color = color;
            }
            elapsed += Time.deltaTime;
            yield return null;
        }

        piece.gameObject.SetActive(false);
    }

    public void ResetEffect()
    {
        StopAllCoroutines();
        isEffectRunning = false;
        
        // Reset all transforms and other properties
        parentRect.anchoredPosition3D = originalAnchoredPosition3D;
        parentRect.sizeDelta = originalSizeDelta;
        parentRect.anchorMin = originalAnchorMin;
        parentRect.anchorMax = originalAnchorMax;
        parentRect.localScale = originalScale;

        gameObject.SetActive(true);
        
        for (int i = 0; i < childImages.Length; i++)
        {
            childImages[i].gameObject.SetActive(true);
            childImages[i].localPosition = originalPositions[i];
            childImages[i].localRotation = originalRotations[i];
            childImages[i].localScale = originalScales[i];
            
            Image image = childImages[i].GetComponent<Image>();
            if (image != null)
            {
                // Reset color alpha
                Color color = image.color;
                color.a = 0f;
                image.color = color;
                
                // Reset glow intensity
                if (image.material != null)
                {
                    image.material.SetFloat("_GlowIntensity", 0f);
                }
            }
        }
        
        StartCoroutine(FadeInPieces());
    }
    private IEnumerator FadeInPieces()
    {
        float duration = 0.5f;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            float t = elapsed / duration;
            foreach (Transform child in childImages)
            {
                Image image = child.GetComponent<Image>();
                if (image != null)
                {
                    Color color = image.color;
                    color.a = Mathf.Lerp(0f, 1f, t);
                    image.color = color;
                }
            }
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        // Start the random cycling glow effect after fade in
        StartCoroutine(CycleRandomGlows());
    }

    private void StartContinuousGlowEffect()
    {
        // Start glow effect for all sprites
        foreach (RectTransform child in childImages)
        {
            Image image = child.GetComponent<Image>();
            if (image != null && image.material != null)
            {
                StartCoroutine(FluctuateGlow(image));
            }
        }
    }
    private IEnumerator FluctuateGlow(Image image)
    {
        if (image == null || image.material == null) yield break;

        Material mat = image.material;
        float offset = Random.Range(0f, 2f * Mathf.PI);
        
        while (true)
        {
            // Make the glow completely off at its minimum
            float glow = Mathf.PingPong(Time.time * glowSpeed + offset, maxGlow);
            mat.SetFloat("_GlowIntensity", glow);
            yield return null;
        }
    }
    private IEnumerator CycleRandomGlows()
    {
        while (true)
        {
            // Stop previous glow coroutines
            foreach (var coroutine in activeGlowCoroutines)
            {
                if (coroutine != null)
                    StopCoroutine(coroutine);
            }
            activeGlowCoroutines.Clear();

            // Completely turn off glow for ALL sprites
            foreach (RectTransform child in childImages)
            {
                Image image = child.GetComponent<Image>();
                if (image != null && image.material != null)
                {
                    image.material.SetFloat("_GlowIntensity", 0f);
                    image.material.SetFloat("_GlowOffset", 0f);
                    image.material.SetFloat("_GlowPower", 0f);
                }
            }

            // Get random indices for glowing sprites
            int[] randomIndices = GetRandomIndices(childImages.Length, glowingSpritesCount);
            
            // Start glow effects only for selected sprites
            foreach (int index in randomIndices)
            {
                Image image = childImages[index].GetComponent<Image>();
                if (image != null && image.material != null)
                {
                    // Reset glow parameters for selected sprites
                    image.material.SetFloat("_GlowOffset", glowOffset);
                    image.material.SetFloat("_GlowPower", glowPower);
                    Coroutine glowCoroutine = StartCoroutine(FluctuateGlow(image));
                    activeGlowCoroutines.Add(glowCoroutine);
                }
            }

            yield return new WaitForSeconds(glowCycleDuration);
        }
    }

    private int[] GetRandomIndices(int totalCount, int selectCount)
    {
        List<int> indices = new List<int>();
        for (int i = 0; i < totalCount; i++)
        {
            indices.Add(i);
        }
        
        int[] selectedIndices = new int[Mathf.Min(selectCount, totalCount)];
        for (int i = 0; i < selectedIndices.Length; i++)
        {
            int randomIndex = Random.Range(0, indices.Count);
            selectedIndices[i] = indices[randomIndex];
            indices.RemoveAt(randomIndex);
        }
        
        return selectedIndices;
    }

    private void OnDestroy()
    {
        // Clean up materials to prevent memory leaks
        foreach (RectTransform child in childImages)
        {
            Image image = child.GetComponent<Image>();
            if (image != null && image.material != null)
            {
                Destroy(image.material);
            }
        }
    }
}
