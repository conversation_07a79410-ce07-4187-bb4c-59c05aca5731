using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections;

public class OverlayMenuController : MonoBehaviour
{
    [SerializeField] private GameObject menu; // Assign the menu (parent object)
    [SerializeField] private GameObject overlay; // Assign the overlay (background)
    public bool hideOtherUIs = true; // Checkbox to hide other UIs
    public string uiToHideName; // The name of the UI to hide
    private bool menuClosed = false; // Track if the menu is closing

    private void Start()
    {
        if (overlay != null)
        {
            // Add a click event to the overlay to close the menu
            EventTrigger trigger = overlay.GetComponent<EventTrigger>();
            if (trigger == null) trigger = overlay.AddComponent<EventTrigger>();

            EventTrigger.Entry entry = new EventTrigger.Entry
            {
                eventID = EventTriggerType.PointerClick
            };
            entry.callback.AddListener((data) => { CloseMenu(); });

            trigger.triggers.Add(entry);
        }
    }

    private void Update()
    {
        // Close menu when pressing ESC
        if (Input.GetKeyDown(KeyCode.Escape) && menu.activeSelf)
        {
            CloseMenu();
        }
    }

    private void OnEnable()
    {
        // Hide the specified UI when the menu is opened
        if (hideOtherUIs && !string.IsNullOrEmpty(uiToHideName) && !menuClosed)
        {
            HideMenuByName(uiToHideName);
        }
    }

    private void CloseMenu()
    {
        if (menu != null)
        {
            menu.SetActive(false); // Deactivate the menu
        }

        // Optionally, deactivate the overlay or other UI elements if needed
        if (overlay != null)
        {
            overlay.SetActive(false);
        }

        // Start reactivating other UI after a short delay
        StartCoroutine(ReactivateOtherUI());
    }

    private IEnumerator ReactivateOtherUI()
    {
        yield return new WaitForSeconds(0.2f); // Delay before reactivating UI

        if (hideOtherUIs && !string.IsNullOrEmpty(uiToHideName))
        {
            ShowMenuByName(uiToHideName);
        }

        menuClosed = false; // Reset flag after menu is closed
    }

    private void HideMenuByName(string menuName)
    {
        // Find the UI object by its name in the scene
        GameObject menuObject = GameObject.Find(menuName);

        if (menuObject != null)
        {
            menuObject.SetActive(false); // Deactivate the UI by name
        }
    }

    private void ShowMenuByName(string menuName)
    {
        // Find the UI object by its name in the scene and reactivate it
        GameObject menuObject = GameObject.Find(menuName);

        if (menuObject != null)
        {
            menuObject.SetActive(true); // Activate the UI by name
        }
    }
}
