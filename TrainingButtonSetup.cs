using UnityEngine;
using UnityEngine.UI;
using System.Collections;


/// <summary>
/// Helper class to set up training type selection buttons
/// </summary>
public class TrainingButtonSetup : MonoBehaviour
{
    [Header("Type Selection Buttons")]
    public Button infantryButton;
    public Button riderButton;
    public Button rangedButton;

    [<PERSON><PERSON>("Training UIs")]
    [SerializeField] private GameObject infantryTrainingUI; // Reference to the Infantry Training UI
    [SerializeField] private GameObject riderTrainingUI; // Reference to the Rider Training UI
    [SerializeField] private GameObject rangedTrainingUI; // Reference to the Ranged Training UI

    private void Start()
    {
        Debug.Log("TrainingButtonSetup: Start called");

        // Find the Infantry Training UI in the scene if not assigned
        if (infantryTrainingUI == null)
        {
            // Try to find by type
            InfantryTrainingUI uiComponent = FindAnyObjectByType<InfantryTrainingUI>();
            if (uiComponent != null)
            {
                infantryTrainingUI = uiComponent.gameObject;
                Debug.Log($"TrainingButtonSetup: Found InfantryTrainingUI: {infantryTrainingUI.name}");
            }
            else
            {
                // Try to find inactive objects
                InfantryTrainingUI[] allUIs = Resources.FindObjectsOfTypeAll<InfantryTrainingUI>();
                if (allUIs.Length > 0)
                {
                    infantryTrainingUI = allUIs[0].gameObject;
                    Debug.Log($"TrainingButtonSetup: Found InfantryTrainingUI (inactive): {infantryTrainingUI.name}");
                }
                else
                {
                    Debug.LogError("TrainingButtonSetup: InfantryTrainingUI not found in scene! Please assign it in the inspector.");
                }
            }
        }
        else
        {
            // Check if the assigned GameObject has the InfantryTrainingUI component
            if (infantryTrainingUI.GetComponent<InfantryTrainingUI>() == null)
            {
                Debug.LogError($"TrainingButtonSetup: The assigned GameObject '{infantryTrainingUI.name}' does not have an InfantryTrainingUI component!");

                // Try to find by type
                InfantryTrainingUI uiComponent = FindAnyObjectByType<InfantryTrainingUI>();
                if (uiComponent != null)
                {
                    infantryTrainingUI = uiComponent.gameObject;
                    Debug.Log($"TrainingButtonSetup: Found InfantryTrainingUI: {infantryTrainingUI.name}");
                }
            }
            else
            {
                Debug.Log($"TrainingButtonSetup: Using assigned InfantryTrainingUI: {infantryTrainingUI.name}");
            }
        }

        // Find the Rider Training UI in the scene if not assigned
        if (riderTrainingUI == null)
        {
            // Try to find by type
            RiderTrainingUI uiComponent = FindAnyObjectByType<RiderTrainingUI>();
            if (uiComponent != null)
            {
                riderTrainingUI = uiComponent.gameObject;
                Debug.Log($"TrainingButtonSetup: Found RiderTrainingUI: {riderTrainingUI.name}");
            }
            else
            {
                // Try to find inactive objects
                RiderTrainingUI[] allUIs = Resources.FindObjectsOfTypeAll<RiderTrainingUI>();
                if (allUIs.Length > 0)
                {
                    riderTrainingUI = allUIs[0].gameObject;
                    Debug.Log($"TrainingButtonSetup: Found RiderTrainingUI (inactive): {riderTrainingUI.name}");
                }
                else
                {
                    Debug.LogError("TrainingButtonSetup: RiderTrainingUI not found in scene! Please assign it in the inspector.");
                }
            }
        }
        else
        {
            // Check if the assigned GameObject has the RiderTrainingUI component
            if (riderTrainingUI.GetComponent<RiderTrainingUI>() == null)
            {
                Debug.LogError($"TrainingButtonSetup: The assigned GameObject '{riderTrainingUI.name}' does not have a RiderTrainingUI component!");

                // Try to find by type
                RiderTrainingUI uiComponent = FindAnyObjectByType<RiderTrainingUI>();
                if (uiComponent != null)
                {
                    riderTrainingUI = uiComponent.gameObject;
                    Debug.Log($"TrainingButtonSetup: Found RiderTrainingUI: {riderTrainingUI.name}");
                }
            }
            else
            {
                Debug.Log($"TrainingButtonSetup: Using assigned RiderTrainingUI: {riderTrainingUI.name}");
            }
        }

        // Find the Ranged Training UI in the scene if not assigned
        if (rangedTrainingUI == null)
        {
            // Try to find by type
            RangedTrainingUI uiComponent = FindAnyObjectByType<RangedTrainingUI>();
            if (uiComponent != null)
            {
                rangedTrainingUI = uiComponent.gameObject;
                Debug.Log($"TrainingButtonSetup: Found RangedTrainingUI: {rangedTrainingUI.name}");
            }
            else
            {
                // Try to find inactive objects
                RangedTrainingUI[] allUIs = Resources.FindObjectsOfTypeAll<RangedTrainingUI>();
                if (allUIs.Length > 0)
                {
                    rangedTrainingUI = allUIs[0].gameObject;
                    Debug.Log($"TrainingButtonSetup: Found RangedTrainingUI (inactive): {rangedTrainingUI.name}");
                }
                else
                {
                    Debug.LogError("TrainingButtonSetup: RangedTrainingUI not found in scene! Please assign it in the inspector.");
                }
            }
        }
        else
        {
            // Check if the assigned GameObject has the RangedTrainingUI component
            if (rangedTrainingUI.GetComponent<RangedTrainingUI>() == null)
            {
                Debug.LogError($"TrainingButtonSetup: The assigned GameObject '{rangedTrainingUI.name}' does not have a RangedTrainingUI component!");

                // Try to find by type
                RangedTrainingUI uiComponent = FindAnyObjectByType<RangedTrainingUI>();
                if (uiComponent != null)
                {
                    rangedTrainingUI = uiComponent.gameObject;
                    Debug.Log($"TrainingButtonSetup: Found RangedTrainingUI: {rangedTrainingUI.name}");
                }
            }
            else
            {
                Debug.Log($"TrainingButtonSetup: Using assigned RangedTrainingUI: {rangedTrainingUI.name}");
            }
        }

        // Make sure the Training UIs are initially hidden
        if (infantryTrainingUI != null)
        {
            infantryTrainingUI.SetActive(false);
            Debug.Log("TrainingButtonSetup: Set InfantryTrainingUI inactive");
        }

        if (riderTrainingUI != null)
        {
            riderTrainingUI.SetActive(false);
            Debug.Log("TrainingButtonSetup: Set RiderTrainingUI inactive");
        }

        if (rangedTrainingUI != null)
        {
            rangedTrainingUI.SetActive(false);
            Debug.Log("TrainingButtonSetup: Set RangedTrainingUI inactive");
        }

        // Set up button listeners for opening the training UIs
        if (infantryButton != null)
        {
            infantryButton.onClick.RemoveAllListeners();
            infantryButton.onClick.AddListener(OpenInfantryTraining);
            Debug.Log("TrainingButtonSetup: Infantry button listener set up");
        }
        else
        {
            Debug.LogError("TrainingButtonSetup: Infantry button is not assigned! Please assign it in the inspector.");
        }

        if (riderButton != null)
        {
            riderButton.onClick.RemoveAllListeners();
            riderButton.onClick.AddListener(OpenRiderTraining);
            Debug.Log("TrainingButtonSetup: Rider button listener set up");
        }
        else
        {
            Debug.LogError("TrainingButtonSetup: Rider button is not assigned! Please assign it in the inspector.");
        }

        if (rangedButton != null)
        {
            rangedButton.onClick.RemoveAllListeners();
            rangedButton.onClick.AddListener(OpenRangedTraining);
            Debug.Log("TrainingButtonSetup: Ranged button listener set up");
        }
        else
        {
            Debug.LogError("TrainingButtonSetup: Ranged button is not assigned! Please assign it in the inspector.");
        }
    }

    private void OpenInfantryTraining()
    {
        Debug.Log("TrainingButtonSetup: OpenInfantryTraining called");

        // Check if we have a reference to the Infantry Training UI
        if (infantryTrainingUI == null)
        {
            Debug.LogError("TrainingButtonSetup: InfantryTrainingUI is not assigned!");

            // Try to find it one more time
            InfantryTrainingUI uiComponent = FindAnyObjectByType<InfantryTrainingUI>();
            if (uiComponent != null)
            {
                infantryTrainingUI = uiComponent.gameObject;
                Debug.Log($"TrainingButtonSetup: Found InfantryTrainingUI: {infantryTrainingUI.name}");
            }
            else
            {
                // Try to find inactive objects
                InfantryTrainingUI[] allUIs = Resources.FindObjectsOfTypeAll<InfantryTrainingUI>();
                if (allUIs.Length > 0)
                {
                    infantryTrainingUI = allUIs[0].gameObject;
                    Debug.Log($"TrainingButtonSetup: Found InfantryTrainingUI (inactive): {infantryTrainingUI.name}");
                }
                else
                {
                    Debug.LogError("TrainingButtonSetup: No InfantryTrainingUI found in scene! Please assign it in the inspector.");
                    return;
                }
            }
        }

        // Make sure the Training Manager is active
        GameObject trainingManager = GameObject.Find("TrainingManager");
        if (trainingManager != null && !trainingManager.activeSelf)
        {
            trainingManager.SetActive(true);
            Debug.Log("TrainingButtonSetup: Activated TrainingManager");
        }
        else if (trainingManager == null)
        {
            // Try to find the Training Manager by type
            TrainingManager managerComponent = FindAnyObjectByType<TrainingManager>();
            if (managerComponent != null)
            {
                trainingManager = managerComponent.gameObject;
                trainingManager.SetActive(true);
                Debug.Log($"TrainingButtonSetup: Found and activated TrainingManager: {trainingManager.name}");
            }
            else
            {
                Debug.LogWarning("TrainingButtonSetup: TrainingManager not found in scene!");
            }
        }

        // Hide other training UIs
        if (riderTrainingUI != null)
        {
            riderTrainingUI.SetActive(false);
        }

        if (rangedTrainingUI != null)
        {
            rangedTrainingUI.SetActive(false);
        }

        // Open the Infantry Training UI
        // First, make sure the GameObject is active
        infantryTrainingUI.SetActive(true);
        Debug.Log("TrainingButtonSetup: Activated InfantryTrainingUI GameObject");

        // Now get the component (it should be available since the GameObject is active)
        InfantryTrainingUI infantryUI = infantryTrainingUI.GetComponent<InfantryTrainingUI>();
        if (infantryUI != null)
        {
            // Make sure the Training Manager is active before opening the UI
            GameObject trainingManagerObj = GameObject.Find("TrainingManager");
            if (trainingManagerObj != null && !trainingManagerObj.activeSelf)
            {
                trainingManagerObj.SetActive(true);
                Debug.Log("TrainingButtonSetup: Activated TrainingManager");
            }

            // Wait a frame to ensure TrainingManager.Instance is initialized
            StartCoroutine(OpenInfantryTrainingDelayed(infantryUI));
        }
        else
        {
            Debug.LogError("TrainingButtonSetup: InfantryTrainingUI component not found on the assigned GameObject!");
            Debug.LogError("Please add the InfantryTrainingUI component to the GameObject in the Inspector and assign all required references.");
        }
    }

    private IEnumerator OpenInfantryTrainingDelayed(InfantryTrainingUI infantryUI)
    {
        yield return null; // Wait one frame

        if (infantryUI != null)
        {
            infantryUI.Open();
            Debug.Log("TrainingButtonSetup: Opened Infantry Training UI");
        }
    }

    private void OpenRiderTraining()
    {
        Debug.Log("TrainingButtonSetup: OpenRiderTraining called");

        // Check if we have a reference to the Rider Training UI
        if (riderTrainingUI == null)
        {
            Debug.LogError("TrainingButtonSetup: RiderTrainingUI is not assigned!");

            // Try to find it one more time
            RiderTrainingUI uiComponent = FindAnyObjectByType<RiderTrainingUI>();
            if (uiComponent != null)
            {
                riderTrainingUI = uiComponent.gameObject;
                Debug.Log($"TrainingButtonSetup: Found RiderTrainingUI: {riderTrainingUI.name}");
            }
            else
            {
                // Try to find inactive objects
                RiderTrainingUI[] allUIs = Resources.FindObjectsOfTypeAll<RiderTrainingUI>();
                if (allUIs.Length > 0)
                {
                    riderTrainingUI = allUIs[0].gameObject;
                    Debug.Log($"TrainingButtonSetup: Found RiderTrainingUI (inactive): {riderTrainingUI.name}");
                }
                else
                {
                    Debug.LogError("TrainingButtonSetup: No RiderTrainingUI found in scene! Please assign it in the inspector.");
                    return;
                }
            }
        }

        // Make sure the Training Manager is active
        GameObject trainingManager = GameObject.Find("TrainingManager");
        if (trainingManager != null && !trainingManager.activeSelf)
        {
            trainingManager.SetActive(true);
            Debug.Log("TrainingButtonSetup: Activated TrainingManager");
        }
        else if (trainingManager == null)
        {
            // Try to find the Training Manager by type
            TrainingManager managerComponent = FindAnyObjectByType<TrainingManager>();
            if (managerComponent != null)
            {
                trainingManager = managerComponent.gameObject;
                trainingManager.SetActive(true);
                Debug.Log($"TrainingButtonSetup: Found and activated TrainingManager: {trainingManager.name}");
            }
            else
            {
                Debug.LogWarning("TrainingButtonSetup: TrainingManager not found in scene!");
            }
        }

        // Hide other training UIs
        if (infantryTrainingUI != null)
        {
            infantryTrainingUI.SetActive(false);
        }

        if (rangedTrainingUI != null)
        {
            rangedTrainingUI.SetActive(false);
        }

        // Open the Rider Training UI
        // First, make sure the GameObject is active
        riderTrainingUI.SetActive(true);
        Debug.Log("TrainingButtonSetup: Activated RiderTrainingUI GameObject");

        // Now get the component (it should be available since the GameObject is active)
        RiderTrainingUI riderUI = riderTrainingUI.GetComponent<RiderTrainingUI>();
        if (riderUI != null)
        {
            // Make sure the Training Manager is active before opening the UI
            GameObject trainingManagerObj = GameObject.Find("TrainingManager");
            if (trainingManagerObj != null && !trainingManagerObj.activeSelf)
            {
                trainingManagerObj.SetActive(true);
                Debug.Log("TrainingButtonSetup: Activated TrainingManager");
            }

            // Wait a frame to ensure TrainingManager.Instance is initialized
            StartCoroutine(OpenRiderTrainingDelayed(riderUI));
        }
        else
        {
            Debug.LogError("TrainingButtonSetup: RiderTrainingUI component not found on the assigned GameObject!");
            Debug.LogError("Please add the RiderTrainingUI component to the GameObject in the Inspector and assign all required references.");
        }
    }

    private IEnumerator OpenRiderTrainingDelayed(RiderTrainingUI riderUI)
    {
        yield return null; // Wait one frame

        if (riderUI != null)
        {
            riderUI.Open();
            Debug.Log("TrainingButtonSetup: Opened Rider Training UI");
        }
    }

    private void OpenRangedTraining()
    {
        Debug.Log("TrainingButtonSetup: OpenRangedTraining called");

        // Check if we have a reference to the Ranged Training UI
        if (rangedTrainingUI == null)
        {
            Debug.LogError("TrainingButtonSetup: RangedTrainingUI is not assigned!");

            // Try to find it one more time
            RangedTrainingUI uiComponent = FindAnyObjectByType<RangedTrainingUI>();
            if (uiComponent != null)
            {
                rangedTrainingUI = uiComponent.gameObject;
                Debug.Log($"TrainingButtonSetup: Found RangedTrainingUI: {rangedTrainingUI.name}");
            }
            else
            {
                // Try to find inactive objects
                RangedTrainingUI[] allUIs = Resources.FindObjectsOfTypeAll<RangedTrainingUI>();
                if (allUIs.Length > 0)
                {
                    rangedTrainingUI = allUIs[0].gameObject;
                    Debug.Log($"TrainingButtonSetup: Found RangedTrainingUI (inactive): {rangedTrainingUI.name}");
                }
                else
                {
                    Debug.LogError("TrainingButtonSetup: No RangedTrainingUI found in scene! Please assign it in the inspector.");
                    return;
                }
            }
        }

        // Make sure the Training Manager is active
        GameObject trainingManager = GameObject.Find("TrainingManager");
        if (trainingManager != null && !trainingManager.activeSelf)
        {
            trainingManager.SetActive(true);
            Debug.Log("TrainingButtonSetup: Activated TrainingManager");
        }
        else if (trainingManager == null)
        {
            // Try to find the Training Manager by type
            TrainingManager managerComponent = FindAnyObjectByType<TrainingManager>();
            if (managerComponent != null)
            {
                trainingManager = managerComponent.gameObject;
                trainingManager.SetActive(true);
                Debug.Log($"TrainingButtonSetup: Found and activated TrainingManager: {trainingManager.name}");
            }
            else
            {
                Debug.LogWarning("TrainingButtonSetup: TrainingManager not found in scene!");
            }
        }

        // Hide other training UIs
        if (infantryTrainingUI != null)
        {
            infantryTrainingUI.SetActive(false);
        }

        if (riderTrainingUI != null)
        {
            riderTrainingUI.SetActive(false);
        }

        // Open the Ranged Training UI
        // First, make sure the GameObject is active
        rangedTrainingUI.SetActive(true);
        Debug.Log("TrainingButtonSetup: Activated RangedTrainingUI GameObject");

        // Now get the component (it should be available since the GameObject is active)
        RangedTrainingUI rangedUI = rangedTrainingUI.GetComponent<RangedTrainingUI>();
        if (rangedUI != null)
        {
            // Make sure the Training Manager is active before opening the UI
            GameObject trainingManagerObj = GameObject.Find("TrainingManager");
            if (trainingManagerObj != null && !trainingManagerObj.activeSelf)
            {
                trainingManagerObj.SetActive(true);
                Debug.Log("TrainingButtonSetup: Activated TrainingManager");
            }

            // Wait a frame to ensure TrainingManager.Instance is initialized
            StartCoroutine(OpenRangedTrainingDelayed(rangedUI));
        }
        else
        {
            Debug.LogError("TrainingButtonSetup: RangedTrainingUI component not found on the assigned GameObject!");
            Debug.LogError("Please add the RangedTrainingUI component to the GameObject in the Inspector and assign all required references.");
        }
    }

    private IEnumerator OpenRangedTrainingDelayed(RangedTrainingUI rangedUI)
    {
        yield return null; // Wait one frame

        if (rangedUI != null)
        {
            rangedUI.Open();
            Debug.Log("TrainingButtonSetup: Opened Ranged Training UI");
        }
    }
}
