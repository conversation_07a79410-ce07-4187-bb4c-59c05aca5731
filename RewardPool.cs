using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using HeroSystem;

public class RewardPool : MonoBehaviour
{
    public class RewardType
    {
        public float chance;
        public string type;
        public List<InventoryItemSO> possibleRewards;
    }

    private void Start()
    {
        InitializeBasicSearchPool();
        Debug.Log("Basic Search Pool Initialized");

    }

    private List<RewardType> basicSearchPool;

    private void InitializeBasicSearchPool()
    {
        basicSearchPool = new List<RewardType>();

        // Rare Hero Fragments (40%)
        var rareHeroes = Resources.LoadAll<HeroData>("Heroes")
            .Where(h => h.Rarity == HeroRarity.Rare)
            .OrderByDescending(h => h.HeroGeneration);
        Debug.Log($"Found {rareHeroes.Count()} rare heroes");

        var rareFragments = rareHeroes
            .SelectMany(h => InventorySystem.Instance.GetItemsByTag(h.HeroName))
            .ToList();
        Debug.Log($"Found {rareFragments.Count()} rare hero fragments");

        basicSearchPool.Add(new RewardType {
            chance = 40f,
            type = "RareFragment",
            possibleRewards = rareFragments.Select(i => i.itemSO).ToList()
        });
        Debug.Log($"Added rare fragment pool with {rareFragments.Count()} possible rewards");

        // Elite Hero Fragments (10%)
        var eliteHeroes = Resources.LoadAll<HeroData>("Heroes")
            .Where(h => h.Rarity == HeroRarity.Elite)
            .OrderByDescending(h => h.HeroGeneration);

        var eliteFragments = eliteHeroes
            .SelectMany(h => InventorySystem.Instance.GetItemsByTag(h.HeroName))
            .ToList();
        basicSearchPool.Add(new RewardType {
            chance = 10f,
            type = "EliteFragment",
            possibleRewards = eliteFragments.Select(i => i.itemSO).ToList()
        });

        // EXP Books (20%)
        var books = InventorySystem.Instance.GetItemsByTag("Book")
            .Where(b => new[] {100, 500, 1000}.Contains(b.itemSO.Value));
        basicSearchPool.Add(new RewardType {
            chance = 20f,
            type = "Book",
            possibleRewards = books.Select(i => i.itemSO).ToList()
        });

        // Badges (15%)
        var rareBadges = InventorySystem.Instance.GetItemsByTag("Rare Badge");
        var eliteBadges = InventorySystem.Instance.GetItemsByTag("Elite Badge");
        var badges = rareBadges.Concat(eliteBadges)
            .Where(b => new[] {100, 400, 800}.Contains(b.itemSO.Value))
            .OrderBy(b => b.itemSO.Value);
        basicSearchPool.Add(new RewardType {
            chance = 15f,
            type = "Badge",
            possibleRewards = badges.Select(i => i.itemSO).ToList()
        });

        // Speedups (15%)
        var speedups = InventorySystem.Instance.GetItemsByCategory(ItemCategory.SpeedUp)
            .Where(s => new[] {1, 5, 15}.Contains(s.itemSO.Value));
        basicSearchPool.Add(new RewardType {
            chance = 15f,
            type = "Speedup",
            possibleRewards = speedups.Select(i => i.itemSO).ToList()
        });
    }

    public RewardItem GenerateBasicReward()
    {
        Debug.Log($"Starting reward generation with {basicSearchPool.Count} pools");
        float roll = Random.Range(0f, 100f);
        Debug.Log($"Roll value: {roll}");

        float currentChance = 0f;
        foreach (var pool in basicSearchPool)
        {
            currentChance += pool.chance;
            Debug.Log($"Checking pool {pool.type} (chance up to {currentChance})");
            if (roll <= currentChance && pool.possibleRewards.Count > 0)
            {
                InventoryItemSO selectedItem = pool.possibleRewards[Random.Range(0, pool.possibleRewards.Count)];
                Debug.Log($"Selected reward: {selectedItem.name}");
                return new RewardItem(selectedItem);
            }
        }

        // Find first non-empty pool as fallback
        var fallbackPool = basicSearchPool.FirstOrDefault(p => p.possibleRewards.Count > 0);
        if (fallbackPool != null)
        {
            return new RewardItem(fallbackPool.possibleRewards[0]);
        }

        Debug.LogError("No valid rewards available in any pool");
        return null;
    }

}
