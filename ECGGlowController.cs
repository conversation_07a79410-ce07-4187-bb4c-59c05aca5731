using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Additional glow controller for ECG wave that adds outer glow effects
/// Attach this to the same GameObject as ECGWaveUI for enhanced visual effects
/// </summary>
[RequireComponent(typeof(ECGWaveUI))]
public class ECGGlowController : MonoBehaviour
{
    [Header("Outer Glow Settings")]
    public bool enableOuterGlow = true;
    public Color outerGlowColor = new Color(0, 1, 0, 0.3f);
    public float outerGlowSize = 1.5f;
    public float outerGlowFalloff = 1f;
    
    [Header("Pulse Effect")]
    public bool enablePulseEffect = true;
    public float pulseSpeed = 2f;
    public float pulseIntensity = 0.2f;
    
    [Header("Heart Rate Response")]
    public bool respondToHeartRate = true;
    public float minGlowIntensity = 0.5f;
    public float maxGlowIntensity = 2f;
    
    private ECGWaveUI ecgWave;
    private Image outerGlowImage;
    private Material outerGlowMaterial;
    private RectTransform rectTransform;
    private float baseGlowIntensity;

    void Start()
    {
        ecgWave = GetComponent<ECGWaveUI>();
        rectTransform = GetComponent<RectTransform>();
        baseGlowIntensity = outerGlowColor.a;
        
        if (enableOuterGlow)
        {
            CreateOuterGlow();
        }
    }

    void CreateOuterGlow()
    {
        // Create a child object for the outer glow
        GameObject glowObj = new GameObject("ECG_OuterGlow");
        glowObj.transform.SetParent(transform);
        glowObj.transform.SetSiblingIndex(0); // Behind the main ECG wave
        
        // Set transform
        glowObj.transform.localPosition = Vector3.zero;
        glowObj.transform.localRotation = Quaternion.identity;
        glowObj.transform.localScale = Vector3.one;
        
        // Add Image component
        outerGlowImage = glowObj.AddComponent<Image>();
        outerGlowImage.color = Color.white; // Color will be controlled by shader
        
        // Try to find and apply outer glow shader
        Shader glowShader = Shader.Find("Custom/UIOuterGlow");
        if (glowShader == null)
            glowShader = Shader.Find("Custom/SpriteGlow");
            
        if (glowShader != null)
        {
            outerGlowMaterial = new Material(glowShader);
            outerGlowImage.material = outerGlowMaterial;
            
            // Set initial shader properties
            outerGlowMaterial.SetColor("_GlowColor", outerGlowColor);
            outerGlowMaterial.SetFloat("_GlowSize", outerGlowSize);
            
            if (outerGlowMaterial.HasProperty("_GlowFalloff"))
                outerGlowMaterial.SetFloat("_GlowFalloff", outerGlowFalloff);
        }
        
        // Set up RectTransform to cover the ECG area with some padding
        RectTransform glowRect = glowObj.GetComponent<RectTransform>();
        glowRect.anchorMin = new Vector2(-0.1f, -0.1f);
        glowRect.anchorMax = new Vector2(1.1f, 1.1f);
        glowRect.offsetMin = Vector2.zero;
        glowRect.offsetMax = Vector2.zero;
    }

    void Update()
    {
        if (!enableOuterGlow || outerGlowMaterial == null)
            return;
            
        float currentIntensity = baseGlowIntensity;
        
        // Apply pulse effect
        if (enablePulseEffect)
        {
            float pulseValue = (Mathf.Sin(Time.time * pulseSpeed) + 1f) * 0.5f;
            currentIntensity *= (1f + pulseValue * pulseIntensity);
        }
        
        // Respond to heart rate
        if (respondToHeartRate && ecgWave != null)
        {
            // Higher heart rate = more intense glow
            float heartRateIntensity = Mathf.Lerp(minGlowIntensity, maxGlowIntensity, 
                Mathf.Clamp01(ecgWave.frequency / 2f)); // Assuming normal range is 0-2 Hz
            currentIntensity *= heartRateIntensity;
        }
        
        // Update glow color with new intensity
        Color glowColor = outerGlowColor;
        glowColor.a = currentIntensity;
        outerGlowMaterial.SetColor("_GlowColor", glowColor);
    }

    /// <summary>
    /// Enable or disable the outer glow effect
    /// </summary>
    public void SetOuterGlowEnabled(bool enabled)
    {
        enableOuterGlow = enabled;
        if (outerGlowImage != null)
        {
            outerGlowImage.gameObject.SetActive(enabled);
        }
    }
    
    /// <summary>
    /// Set the outer glow color
    /// </summary>
    public void SetOuterGlowColor(Color color)
    {
        outerGlowColor = color;
        baseGlowIntensity = color.a;
        
        if (outerGlowMaterial != null)
        {
            outerGlowMaterial.SetColor("_GlowColor", color);
        }
    }
    
    /// <summary>
    /// Set the outer glow size
    /// </summary>
    public void SetOuterGlowSize(float size)
    {
        outerGlowSize = size;
        
        if (outerGlowMaterial != null)
        {
            outerGlowMaterial.SetFloat("_GlowSize", size);
        }
    }
    
    /// <summary>
    /// Trigger a special glow pulse (useful for alerts or events)
    /// </summary>
    public void TriggerGlowPulse(float duration = 1f, float intensity = 2f)
    {
        if (outerGlowMaterial != null)
        {
            StartCoroutine(GlowPulseCoroutine(duration, intensity));
        }
    }
    
    private System.Collections.IEnumerator GlowPulseCoroutine(float duration, float intensity)
    {
        float originalIntensity = baseGlowIntensity;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            
            // Create a pulse curve (up then down)
            float pulseValue = Mathf.Sin(progress * Mathf.PI);
            float currentIntensity = originalIntensity + (pulseValue * intensity);
            
            Color glowColor = outerGlowColor;
            glowColor.a = currentIntensity;
            outerGlowMaterial.SetColor("_GlowColor", glowColor);
            
            yield return null;
        }
        
        // Reset to original intensity
        Color resetColor = outerGlowColor;
        resetColor.a = originalIntensity;
        outerGlowMaterial.SetColor("_GlowColor", resetColor);
    }
}
