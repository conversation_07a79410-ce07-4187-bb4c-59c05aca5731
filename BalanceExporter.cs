using UnityEngine;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using HeroSystem;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class BalanceExporter : MonoBehaviour
{
    public GameBalanceData balanceData;

    [Header("Export Settings")]
    public string exportFileName = "game_balance_data.json";
    public bool prettyPrint = true;

    [Header("Import Settings")]
    public TextAsset importJsonFile;

    // Export the current balance data to JSON
    public void ExportBalanceData()
    {
        if (balanceData == null)
        {
            Debug.LogError("No balance data assigned to export!");
            return;
        }

        // Create a serializable version of the data
        BalanceDataSerializable serializableData = ConvertToSerializable(balanceData);

        // Convert to JSON
        string jsonData = JsonUtility.ToJson(serializableData, prettyPrint);

        // Save to file
        string filePath = Path.Combine(Application.dataPath, exportFileName);
        File.WriteAllText(filePath, jsonData);

        Debug.Log($"Balance data exported to: {filePath}");

        #if UNITY_EDITOR
        AssetDatabase.Refresh();
        #endif
    }

    // Import balance data from JSON
    public void ImportBalanceData()
    {
        if (importJsonFile == null)
        {
            Debug.LogError("No JSON file assigned to import!");
            return;
        }

        // Parse JSON
        BalanceDataSerializable serializableData = JsonUtility.FromJson<BalanceDataSerializable>(importJsonFile.text);

        if (serializableData == null)
        {
            Debug.LogError("Failed to parse JSON data!");
            return;
        }

        // Apply to balance data
        ApplySerializableData(serializableData, balanceData);

        Debug.Log("Balance data imported successfully!");

        #if UNITY_EDITOR
        EditorUtility.SetDirty(balanceData);
        AssetDatabase.SaveAssets();
        #endif
    }

    // Collect balance data from the current game state
    public void CollectCurrentGameData()
    {
        if (balanceData == null)
        {
            Debug.LogError("No balance data assigned to collect data into!");
            return;
        }

        // Clear existing data
        balanceData.heroBalanceData.Clear();
        balanceData.buildingBalanceData.Clear();
        balanceData.researchBalanceData.Clear();

        // Collect hero data
        CollectHeroData();

        // Collect standalone skill data
        CollectStandaloneSkillData();

        // Collect building data
        CollectBuildingData();

        // Collect research data
        CollectResearchData();

        // Collect resource generation data
        CollectResourceData();

        Debug.Log("Game data collected successfully!");

        #if UNITY_EDITOR
        EditorUtility.SetDirty(balanceData);
        AssetDatabase.SaveAssets();
        #endif
    }

    private void CollectStandaloneSkillData()
    {
        // Find all skill data assets from the specific Skills folder
        SkillData[] allSkills = Resources.LoadAll<SkillData>("Skills");

        // If no skills found in Resources, try FindObjectsOfTypeAll as fallback
        if (allSkills == null || allSkills.Length == 0)
        {
            Debug.Log("No standalone skills found in Resources folder. Trying FindObjectsOfTypeAll...");
            allSkills = Resources.FindObjectsOfTypeAll<SkillData>();
        }

        Debug.Log($"Found {allSkills.Length} standalone skills to analyze.");

        // Process skills that might not be attached to heroes
        foreach (var skill in allSkills)
        {
            // Check if this skill is already included in a hero's skill list
            bool isAlreadyIncluded = false;
            foreach (var heroBalance in balanceData.heroBalanceData)
            {
                if (heroBalance.skills.Any(s => s.skillName == skill.skillName))
                {
                    isAlreadyIncluded = true;
                    break;
                }
            }

            // If the skill is not already included, add it to the appropriate hero or create a placeholder hero
            if (!isAlreadyIncluded)
            {
                // Try to determine which hero this skill belongs to
                string heroName = DetermineHeroNameFromSkill(skill);

                // Find or create hero balance data
                HeroBalanceData heroBalance = balanceData.heroBalanceData.Find(h => h.heroName == heroName);

                if (heroBalance == null)
                {
                    // Create a placeholder hero for this skill
                    heroBalance = new HeroBalanceData
                    {
                        heroName = heroName,
                        rarity = HeroRarity.Rare, // Default rarity
                        heroType = HeroType.Infantry, // Default type
                        initialPower = 100, // Default power
                        baseStats = new BaseStats(), // Default stats
                        troopModifiers = new TroopModifiers(), // Default modifiers
                        skills = new List<SkillBalanceData>()
                    };

                    balanceData.heroBalanceData.Add(heroBalance);
                }

                // Create skill balance data
                SkillBalanceData skillBalance = new SkillBalanceData
                {
                    skillName = skill.skillName,
                    attackBonus = skill.attackBonus,
                    damageBonus = skill.damageBonus,
                    defenseBonus = skill.defenseBonus,
                    healthBonus = skill.healthBonus,
                    marchCapacityBonus = skill.marchCapacityBonus,
                    rallyCapacityBonus = skill.rallyCapacityBonus,
                    effects = new List<SkillEffectData>()
                };

                // Collect skill effects
                if (skill.skillEffects != null)
                {
                    foreach (var effect in skill.skillEffects)
                    {
                        SkillEffectData effectData = new SkillEffectData
                        {
                            effectType = effect.effectType,
                            effectValue = effect.effectValue,
                            effectChance = effect.effectChance,
                            duration = effect.turns
                        };

                        skillBalance.effects.Add(effectData);
                    }
                }

                heroBalance.skills.Add(skillBalance);
            }
        }
    }

    // Helper method to determine which hero a skill belongs to
    private string DetermineHeroNameFromSkill(SkillData skill)
    {
        // Try to extract hero name from skill name or other properties
        // This is a simple implementation - you may need to customize this based on your naming conventions

        // Example: If skill name is "Aragorn's Sword Strike", extract "Aragorn"
        if (skill.skillName.Contains("'"))
        {
            int apostropheIndex = skill.skillName.IndexOf("'");
            if (apostropheIndex > 0)
            {
                return skill.skillName.Substring(0, apostropheIndex);
            }
        }

        // Example: If skill name is "Fireball (Gandalf)", extract "Gandalf"
        if (skill.skillName.Contains("(") && skill.skillName.Contains(")"))
        {
            int openParenIndex = skill.skillName.IndexOf("(");
            int closeParenIndex = skill.skillName.IndexOf(")");
            if (openParenIndex > 0 && closeParenIndex > openParenIndex)
            {
                return skill.skillName.Substring(openParenIndex + 1, closeParenIndex - openParenIndex - 1);
            }
        }

        // If we can't determine the hero name, use a placeholder
        return "Unknown Hero (" + skill.skillName + ")";
    }

    private void CollectHeroData()
    {
        // Find all hero data assets from the specific Heroes folder
        HeroData[] allHeroes = Resources.LoadAll<HeroData>("Heroes");

        // If no heroes found in Resources, try FindObjectsOfTypeAll as fallback
        if (allHeroes == null || allHeroes.Length == 0)
        {
            Debug.Log("No heroes found in Resources folder. Trying FindObjectsOfTypeAll...");
            allHeroes = Resources.FindObjectsOfTypeAll<HeroData>();
        }

        Debug.Log($"Found {allHeroes.Length} heroes to analyze.");

        foreach (var hero in allHeroes)
        {
            HeroBalanceData heroBalance = new HeroBalanceData
            {
                heroName = hero.HeroName,
                rarity = hero.Rarity,
                heroType = hero.CurrentHeroType,
                initialPower = hero.InitialPower,
                baseStats = hero.BaseStats,
                troopModifiers = hero.TroopModifiers,
                skills = new List<SkillBalanceData>()
            };

            // Collect skill data
            if (hero.Skills != null)
            {
                foreach (var skill in hero.Skills)
                {
                    if (skill != null)
                    {
                        SkillBalanceData skillBalance = new SkillBalanceData
                        {
                            skillName = skill.skillName,
                            attackBonus = skill.attackBonus,
                            damageBonus = skill.damageBonus,
                            defenseBonus = skill.defenseBonus,
                            healthBonus = skill.healthBonus,
                            marchCapacityBonus = skill.marchCapacityBonus,
                            rallyCapacityBonus = skill.rallyCapacityBonus,
                            effects = new List<SkillEffectData>()
                        };

                        // Collect skill effects
                        if (skill.skillEffects != null)
                        {
                            foreach (var effect in skill.skillEffects)
                            {
                                SkillEffectData effectData = new SkillEffectData
                                {
                                    effectType = effect.effectType,
                                    effectValue = effect.effectValue,
                                    effectChance = effect.effectChance,
                                    duration = effect.turns
                                };

                                skillBalance.effects.Add(effectData);
                            }
                        }

                        heroBalance.skills.Add(skillBalance);
                    }
                }
            }

            balanceData.heroBalanceData.Add(heroBalance);
        }
    }

    private void CollectBuildingData()
    {
        // Find all building prefabs or instances
        BuildingUpgrade[] allBuildings = Resources.FindObjectsOfTypeAll<BuildingUpgrade>();

        foreach (var building in allBuildings)
        {
            // Skip duplicates
            if (balanceData.buildingBalanceData.Any(b => b.buildingName == building.name))
                continue;

            BuildingBalanceData buildingBalance = new BuildingBalanceData
            {
                buildingName = building.name,
                buildingType = building.buildingType,
                maxLevel = building.maxLevel,

                // Base costs
                baseFoodCost = building.baseFoodCost,
                baseWoodCost = building.baseWoodCost,
                baseMetalCost = building.baseMetalCost,
                baseGoldCost = building.baseGoldCost,

                // Base values
                baseUpgradeTime = building.baseUpgradeTime,
                baseBattlePower = building.baseBattlePower,
                baseWelfare = building.baseWelfare,
                baseResourceGeneration = building.baseResourceGeneration,

                // Scaling factors
                resourceGrowthFactor = building.resourceGrowthFactor,
                timeGrowthFactor = building.timeGrowthFactor,
                battlePowerGrowthFactor = building.battlePowerGrowthFactor,
                welfareGrowthFactor = building.welfareGrowthFactor,
                resourceGenerationGrowthFactor = building.resourceGenerationGrowthFactor
            };

            balanceData.buildingBalanceData.Add(buildingBalance);
        }
    }

    private void CollectResearchData()
    {
        // Find all research node assets
        ResearchNode[] allResearch = Resources.FindObjectsOfTypeAll<ResearchNode>();

        foreach (var research in allResearch)
        {
            ResearchBalanceData researchBalance = new ResearchBalanceData
            {
                researchName = research.name,
                nodeType = research.nodeType,
                bonusType = research.bonusType,
                isPercentageBonus = research.isPercentageBonus,

                // Base costs
                startingFoodCost = research.startingFoodCost,
                startingWoodCost = research.startingWoodCost,
                startingMetalCost = research.startingMetalCost,
                startingGoldCost = research.startingGoldCost,

                // Base values
                startingTime = research.startingTime,
                startingPower = research.startingPower,

                // Tiers
                tiers = new List<ResearchTierBalanceData>()
            };

            // Collect tier data
            if (research.tiers != null)
            {
                foreach (var tier in research.tiers)
                {
                    ResearchTierBalanceData tierData = new ResearchTierBalanceData
                    {
                        tierNumber = tier.tierNumber,
                        maxLevel = tier.maxLevel,
                        bonus = tier.bonus,
                        requiredLabLevel = tier.requiredLabLevel
                    };

                    researchBalance.tiers.Add(tierData);
                }
            }

            balanceData.researchBalanceData.Add(researchBalance);
        }
    }

    private void CollectResourceData()
    {
        // Get resource generation rates from GameManager or other sources
        // This is a placeholder - you'll need to adapt this to your actual game data
        GameManager gameManager = UnityEngine.Object.FindFirstObjectByType<GameManager>();

        if (gameManager != null)
        {
            // Example of collecting resource data - adapt to your actual implementation
            balanceData.resourceGeneration.foodGenerationRate = 1.0f; // Replace with actual values
            balanceData.resourceGeneration.woodGenerationRate = 1.0f;
            balanceData.resourceGeneration.metalGenerationRate = 1.0f;
            balanceData.resourceGeneration.goldGenerationRate = 0.5f;
        }

        // Collect upgrade scaling data
        // This is a placeholder - you'll need to adapt this to your actual game data
        balanceData.upgradeScaling.heroRankScaling = 3.6f; // From HeroData.CalculateRequiredPtsForNextRank
        balanceData.upgradeScaling.buildingUpgradeTimeScaling = 1.3f; // From BuildingUpgrade.timeGrowthFactor
        balanceData.upgradeScaling.buildingResourceCostScaling = 1.5f; // From BuildingUpgrade.resourceGrowthFactor
        balanceData.upgradeScaling.researchTimeScaling = 1.3f; // From ResearchNode calculations
        balanceData.upgradeScaling.researchCostScaling = 1.3f; // From ResearchNode calculations
    }

    // Helper methods for serialization
    private BalanceDataSerializable ConvertToSerializable(GameBalanceData data)
    {
        BalanceDataSerializable result = new BalanceDataSerializable
        {
            globalResourceMultiplier = data.globalResourceMultiplier,
            globalTimeMultiplier = data.globalTimeMultiplier,
            globalPowerMultiplier = data.globalPowerMultiplier,

            heroBalanceData = data.heroBalanceData,
            buildingBalanceData = data.buildingBalanceData,
            researchBalanceData = data.researchBalanceData,
            resourceGeneration = data.resourceGeneration,
            upgradeScaling = data.upgradeScaling
        };

        return result;
    }

    private void ApplySerializableData(BalanceDataSerializable source, GameBalanceData target)
    {
        target.globalResourceMultiplier = source.globalResourceMultiplier;
        target.globalTimeMultiplier = source.globalTimeMultiplier;
        target.globalPowerMultiplier = source.globalPowerMultiplier;

        // Apply hero data
        target.heroBalanceData = source.heroBalanceData;

        // Apply building data
        target.buildingBalanceData = source.buildingBalanceData;

        // Apply research data
        target.researchBalanceData = source.researchBalanceData;

        // Apply resource generation data
        target.resourceGeneration = source.resourceGeneration;

        // Apply upgrade scaling data
        target.upgradeScaling = source.upgradeScaling;
    }
}

// Serializable version of GameBalanceData
[System.Serializable]
public class BalanceDataSerializable
{
    public float globalResourceMultiplier = 1.0f;
    public float globalTimeMultiplier = 1.0f;
    public float globalPowerMultiplier = 1.0f;

    public List<HeroBalanceData> heroBalanceData = new List<HeroBalanceData>();
    public List<BuildingBalanceData> buildingBalanceData = new List<BuildingBalanceData>();
    public List<ResearchBalanceData> researchBalanceData = new List<ResearchBalanceData>();
    public ResourceGenerationData resourceGeneration = new ResourceGenerationData();
    public UpgradeScalingData upgradeScaling = new UpgradeScalingData();
}
