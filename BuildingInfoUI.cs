using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

public class BuildingInfoUI : MonoBehaviour
{
    [Header("UI References")]
    public GameObject infoPanel;
    public Image buildingImage;
    public TextMeshProUGUI buildingNameText;
    public TextMeshProUGUI buildingLevelText;
    public TextMeshProUGUI buildingDescriptionText;
    public Transform capabilitiesContainer;
    public GameObject capabilityPrefab;
    public Button closeButton;

    private List<GameObject> instantiatedCapabilities = new List<GameObject>();

    private void Awake()
    {
        // Make sure we have all the required UI elements
        if (infoPanel == null)
        {
            Debug.LogError("InfoPanel is not assigned in the BuildingInfoUI component. Please assign it in the Inspector.");
        }

        if (buildingImage == null)
        {
            Debug.LogError("Building Image is not assigned in the BuildingInfoUI component. Please assign it in the Inspector.");
        }

        if (buildingNameText == null)
        {
            Debug.LogError("Building Name Text is not assigned in the BuildingInfoUI component. Please assign it in the Inspector.");
        }

        if (buildingLevelText == null)
        {
            Debug.LogError("Building Level Text is not assigned in the BuildingInfoUI component. Please assign it in the Inspector.");
        }

        if (buildingDescriptionText == null)
        {
            Debug.LogError("Building Description Text is not assigned in the BuildingInfoUI component. Please assign it in the Inspector.");
        }

        if (capabilitiesContainer == null)
        {
            Debug.LogError("Capabilities Container is not assigned in the BuildingInfoUI component. Please assign it in the Inspector.");
        }

        if (capabilityPrefab == null)
        {
            Debug.LogError("Capability Prefab is not assigned in the BuildingInfoUI component. Please assign it in the Inspector.");
        }

        // Add close button listener
        if (closeButton != null)
        {
            closeButton.onClick.RemoveAllListeners();
            closeButton.onClick.AddListener(ClosePanel);
        }
        else
        {
            Debug.LogError("Close Button is not assigned in the BuildingInfoUI component. Please assign it in the Inspector.");
        }

        // Hide panel by default
        if (infoPanel != null)
        {
            infoPanel.SetActive(false);
        }
    }

    private void CreateUI()
    {
        // Create canvas if it doesn't exist
        Canvas canvas = FindFirstObjectByType<Canvas>();
        if (canvas == null)
        {
            GameObject canvasObj = new GameObject("Canvas");
            canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasObj.AddComponent<CanvasScaler>();
            canvasObj.AddComponent<GraphicRaycaster>();
        }

        // Create panel
        GameObject panelObj = new GameObject("BuildingInfoPanel");
        panelObj.transform.SetParent(canvas.transform, false);
        infoPanel = panelObj;

        // Add panel components
        Image panelImage = panelObj.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.8f);
        RectTransform panelRect = panelObj.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0.5f, 0.5f);
        panelRect.anchorMax = new Vector2(0.5f, 0.5f);
        panelRect.pivot = new Vector2(0.5f, 0.5f);
        panelRect.sizeDelta = new Vector2(600, 800);

        // Create building image
        GameObject imageObj = new GameObject("BuildingImage");
        imageObj.transform.SetParent(panelObj.transform, false);
        buildingImage = imageObj.AddComponent<Image>();
        RectTransform imageRect = imageObj.GetComponent<RectTransform>();
        imageRect.anchorMin = new Vector2(0.5f, 0.85f);
        imageRect.anchorMax = new Vector2(0.5f, 0.85f);
        imageRect.pivot = new Vector2(0.5f, 0.5f);
        imageRect.sizeDelta = new Vector2(200, 200);

        // Create building name text
        GameObject nameObj = new GameObject("BuildingNameText");
        nameObj.transform.SetParent(panelObj.transform, false);
        buildingNameText = nameObj.AddComponent<TextMeshProUGUI>();
        buildingNameText.fontSize = 24;
        buildingNameText.alignment = TextAlignmentOptions.Center;
        RectTransform nameRect = nameObj.GetComponent<RectTransform>();
        nameRect.anchorMin = new Vector2(0.5f, 0.75f);
        nameRect.anchorMax = new Vector2(0.5f, 0.75f);
        nameRect.pivot = new Vector2(0.5f, 0.5f);
        nameRect.sizeDelta = new Vector2(500, 50);

        // Create building level text
        GameObject levelObj = new GameObject("BuildingLevelText");
        levelObj.transform.SetParent(panelObj.transform, false);
        buildingLevelText = levelObj.AddComponent<TextMeshProUGUI>();
        buildingLevelText.fontSize = 18;
        buildingLevelText.alignment = TextAlignmentOptions.Center;
        RectTransform levelRect = levelObj.GetComponent<RectTransform>();
        levelRect.anchorMin = new Vector2(0.5f, 0.7f);
        levelRect.anchorMax = new Vector2(0.5f, 0.7f);
        levelRect.pivot = new Vector2(0.5f, 0.5f);
        levelRect.sizeDelta = new Vector2(500, 40);

        // Create building description text
        GameObject descObj = new GameObject("BuildingDescriptionText");
        descObj.transform.SetParent(panelObj.transform, false);
        buildingDescriptionText = descObj.AddComponent<TextMeshProUGUI>();
        buildingDescriptionText.fontSize = 16;
        buildingDescriptionText.alignment = TextAlignmentOptions.TopLeft;
        RectTransform descRect = descObj.GetComponent<RectTransform>();
        descRect.anchorMin = new Vector2(0.1f, 0.5f);
        descRect.anchorMax = new Vector2(0.9f, 0.65f);
        descRect.pivot = new Vector2(0.5f, 0.5f);
        descRect.sizeDelta = new Vector2(500, 200);

        // Create capabilities container
        GameObject capContainerObj = new GameObject("CapabilitiesContainer");
        capContainerObj.transform.SetParent(panelObj.transform, false);
        capabilitiesContainer = capContainerObj.transform;
        VerticalLayoutGroup layout = capContainerObj.AddComponent<VerticalLayoutGroup>();
        layout.childAlignment = TextAnchor.UpperCenter;
        layout.spacing = 10;
        layout.padding = new RectOffset(20, 20, 10, 10);
        ContentSizeFitter fitter = capContainerObj.AddComponent<ContentSizeFitter>();
        fitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
        RectTransform capContainerRect = capContainerObj.GetComponent<RectTransform>();
        capContainerRect.anchorMin = new Vector2(0.1f, 0.2f);
        capContainerRect.anchorMax = new Vector2(0.9f, 0.45f);
        capContainerRect.pivot = new Vector2(0.5f, 0.5f);

        // Create close button
        GameObject closeButtonObj = new GameObject("CloseButton");
        closeButtonObj.transform.SetParent(panelObj.transform, false);
        closeButton = closeButtonObj.AddComponent<Button>();
        Image closeButtonImage = closeButtonObj.AddComponent<Image>();
        closeButtonImage.color = new Color(0.8f, 0.2f, 0.2f);
        RectTransform closeButtonRect = closeButtonObj.GetComponent<RectTransform>();
        closeButtonRect.anchorMin = new Vector2(0.5f, 0.1f);
        closeButtonRect.anchorMax = new Vector2(0.5f, 0.1f);
        closeButtonRect.pivot = new Vector2(0.5f, 0.5f);
        closeButtonRect.sizeDelta = new Vector2(200, 50);

        // Add text to close button
        GameObject closeTextObj = new GameObject("CloseText");
        closeTextObj.transform.SetParent(closeButtonObj.transform, false);
        TextMeshProUGUI closeText = closeTextObj.AddComponent<TextMeshProUGUI>();
        closeText.text = "Close";
        closeText.fontSize = 18;
        closeText.alignment = TextAlignmentOptions.Center;
        RectTransform closeTextRect = closeTextObj.GetComponent<RectTransform>();
        closeTextRect.anchorMin = Vector2.zero;
        closeTextRect.anchorMax = Vector2.one;
        closeTextRect.sizeDelta = Vector2.zero;

        // Hide panel by default
        infoPanel.SetActive(false);
    }

    public void ShowBuildingInfo(BuildingMenu buildingMenu)
    {
        Debug.Log($"ShowBuildingInfo called for {buildingMenu.gameObject.name}");

        // Store reference to the current building
        currentBuilding = buildingMenu.gameObject;

        // Activate this GameObject if it's not already active
        if (!gameObject.activeSelf)
        {
            gameObject.SetActive(true);
            Debug.Log("Activated BuildingInfoUI GameObject");
        }

        if (infoPanel == null)
        {
            Debug.LogError("InfoPanel is not assigned. Cannot show building info.");
            return;
        }

        // Make sure the info panel is active
        if (!infoPanel.activeSelf)
        {
            infoPanel.SetActive(true);
            Debug.Log("Activated info panel");
        }

        // Get building data
        BuildingUpgrade buildingUpgrade = buildingMenu.GetComponent<BuildingUpgrade>();

        // Set building image
        if (buildingImage != null && buildingMenu.buildingImage != null)
        {
            buildingImage.sprite = buildingMenu.buildingImage;
            Debug.Log($"Set building image to {buildingMenu.buildingImage.name}");
        }
        else if (buildingImage != null)
        {
            Debug.LogWarning($"Building {buildingMenu.gameObject.name} has no image assigned.");
            buildingImage.sprite = null;
        }

        // Set building name
        if (buildingNameText != null)
        {
            buildingNameText.text = buildingMenu.buildingName;
            Debug.Log($"Set building name to {buildingMenu.buildingName}");
        }

        // Set building level
        if (buildingLevelText != null && buildingUpgrade != null)
        {
            buildingLevelText.text = $"Level {buildingUpgrade.CurrentLevel}";
            Debug.Log($"Set building level to {buildingUpgrade.CurrentLevel}");
        }
        else if (buildingLevelText != null)
        {
            buildingLevelText.text = "Level 1";
            Debug.LogWarning($"Building {buildingMenu.gameObject.name} has no BuildingUpgrade component. Defaulting to Level 1.");
        }

        // Set building description
        if (buildingDescriptionText != null)
        {
            buildingDescriptionText.text = buildingMenu.buildingDescription;
            Debug.Log($"Set building description to: {buildingMenu.buildingDescription}");
        }

        // Clear previous capabilities
        ClearCapabilities();

        // Add capabilities
        if (capabilitiesContainer != null && capabilityPrefab != null)
        {
            // Check if the capabilities container has a layout component
            if (capabilitiesContainer.GetComponent<LayoutGroup>() == null)
            {
                Debug.LogWarning("Capabilities container doesn't have a LayoutGroup component. Adding a VerticalLayoutGroup.");
                VerticalLayoutGroup layout = capabilitiesContainer.gameObject.AddComponent<VerticalLayoutGroup>();
                layout.spacing = 10;
                layout.childAlignment = TextAnchor.UpperLeft;
                layout.childForceExpandWidth = true;
                layout.childForceExpandHeight = false;
                layout.childControlWidth = true;
                layout.childControlHeight = false;
            }

            List<BuildingCapabilityType> capabilities = buildingMenu.GetCapabilities();
            Debug.Log($"Found {capabilities.Count} capabilities for {buildingMenu.gameObject.name}");

            foreach (BuildingCapabilityType capability in capabilities)
            {
                // Get the capability value from the BuildingMenu
                float value = buildingMenu.GetCapabilityValue(capability);

                AddCapability(capability.ToString(), value);
                Debug.Log($"Added capability: {capability} with value: {value}");
            }
        }

        // Show panel
        infoPanel.SetActive(true);
        Debug.Log("Building info panel activated");
    }

    private void AddCapability(string capabilityText, float value = 0)
    {
        Debug.Log($"AddCapability called with text: {capabilityText}, value: {value}");

        if (capabilitiesContainer == null)
        {
            Debug.LogError("Cannot add capability: capabilities container is null");
            return;
        }

        if (capabilityPrefab == null)
        {
            Debug.LogError("Cannot add capability: capability prefab is null");
            return;
        }

        // Instantiate capability prefab
        GameObject capabilityInstance = Instantiate(capabilityPrefab, capabilitiesContainer);
        Debug.Log($"Instantiated capability prefab: {capabilityInstance.name}");

        // Format the capability text to be more readable
        string formattedCapabilityText = FormatCapabilityName(capabilityText);

        // Set capability text
        TextMeshProUGUI nameText = capabilityInstance.GetComponentInChildren<TextMeshProUGUI>();
        if (nameText != null)
        {
            nameText.text = formattedCapabilityText;
            Debug.Log($"Set capability text to: {formattedCapabilityText}");
        }
        else
        {
            Debug.LogError("TextMeshProUGUI component not found in capability prefab");

            // Try to find a regular Text component
            Text regularText = capabilityInstance.GetComponentInChildren<Text>();
            if (regularText != null)
            {
                regularText.text = formattedCapabilityText;
                Debug.Log($"Set regular Text component text to: {formattedCapabilityText}");
            }
            else
            {
                Debug.LogError("No text component found in capability prefab");
            }
        }

        // Find and set the value text
        TextMeshProUGUI valueText = null;

        // Look for a child GameObject named "ValueText"
        Transform valueTextTransform = capabilityInstance.transform.Find("ValueText");
        if (valueTextTransform != null)
        {
            valueText = valueTextTransform.GetComponent<TextMeshProUGUI>();
            if (valueText == null)
            {
                valueText = valueTextTransform.GetComponentInChildren<TextMeshProUGUI>();
            }
        }

        // If not found by name, try to find a second TextMeshProUGUI component
        if (valueText == null)
        {
            TextMeshProUGUI[] textComponents = capabilityInstance.GetComponentsInChildren<TextMeshProUGUI>();
            if (textComponents.Length > 1 && textComponents[0] != nameText)
            {
                valueText = textComponents[0];
            }
            else if (textComponents.Length > 1)
            {
                valueText = textComponents[1];
            }
        }

        // Set the value text if found
        if (valueText != null)
        {
            // Format the value based on the capability type
            string formattedValue = FormatCapabilityValue(capabilityText, value);
            valueText.text = formattedValue;
            Debug.Log($"Set capability value text to: {formattedValue}");
        }
        else
        {
            Debug.LogWarning("Value text component not found in capability prefab");
        }

        instantiatedCapabilities.Add(capabilityInstance);
        Debug.Log($"Added capability to instantiated list. Total: {instantiatedCapabilities.Count}");
    }

    private string FormatCapabilityName(string capabilityText)
    {
        // Replace underscores with spaces
        string formatted = capabilityText.Replace('_', ' ');

        // Add spaces before capital letters (except the first one)
        for (int i = 1; i < formatted.Length; i++)
        {
            if (char.IsUpper(formatted[i]) && !char.IsWhiteSpace(formatted[i - 1]))
            {
                formatted = formatted.Insert(i, " ");
                i++; // Skip the space we just added
            }
        }

        return formatted;
    }

    private string FormatCapabilityValue(string capabilityType, float value)
    {
        // Format the value based on the capability type
        if (capabilityType.Contains("ResourceProduction_Energy"))
        {
            return $"{Mathf.RoundToInt(value)} kW";
        }
        else if (capabilityType.Contains("ResourceProduction"))
        {
            return $"{Mathf.RoundToInt(value)}";
        }
        else if (capabilityType.Contains("Speed"))
        {
            return $"+{value:F1}%";
        }
        else if (capabilityType.Contains("Capacity"))
        {
            return $"{Mathf.RoundToInt(value)}";
        }
        else if (capabilityType.Contains("BattlePower") || capabilityType.Contains("Welfare"))
        {
            return $"+{Mathf.RoundToInt(value)}";
        }
        else
        {
            // Default format
            return value < 1 ? $"{value:P0}" : $"{value:F1}";
        }
    }

    private void ClearCapabilities()
    {
        foreach (GameObject capability in instantiatedCapabilities)
        {
            Destroy(capability);
        }

        instantiatedCapabilities.Clear();
    }

    private void ClosePanel()
    {
        Debug.Log("Close button clicked");

        if (infoPanel != null)
        {
            infoPanel.SetActive(false);
            Debug.Log("Building info panel deactivated");

            // Clear the current building reference
            currentBuilding = null;
        }
        else
        {
            Debug.LogError("InfoPanel is null. Cannot close panel.");
        }
    }

    // Public method to close the panel from outside
    public void Close()
    {
        ClosePanel();
    }

    // Track which building is currently being shown
    private GameObject currentBuilding;

    // Check if the BuildingInfoUI is currently showing info for a specific building
    public bool IsShowingBuildingInfo(GameObject building)
    {
        return infoPanel != null && infoPanel.activeSelf && currentBuilding == building;
    }

    // Update the values in the BuildingInfoUI without showing it if it's not already visible
    public void UpdateBuildingInfoValues(BuildingMenu buildingMenu)
    {
        // If the panel isn't active or we're showing a different building, don't update
        if (!infoPanel.activeSelf || currentBuilding != buildingMenu.gameObject)
        {
            return;
        }

        Debug.Log($"Updating BuildingInfoUI values for {buildingMenu.gameObject.name} without showing the panel");

        // Get building data
        BuildingUpgrade buildingUpgrade = buildingMenu.GetComponent<BuildingUpgrade>();

        // Update building level
        if (buildingLevelText != null && buildingUpgrade != null)
        {
            buildingLevelText.text = $"Level {buildingUpgrade.CurrentLevel}";
            Debug.Log($"Updated building level to {buildingUpgrade.CurrentLevel}");
        }

        // Clear previous capabilities
        ClearCapabilities();

        // Add updated capabilities
        if (capabilitiesContainer != null && capabilityPrefab != null)
        {
            List<BuildingCapabilityType> capabilities = buildingMenu.GetCapabilities();
            Debug.Log($"Found {capabilities.Count} capabilities for {buildingMenu.gameObject.name}");

            foreach (BuildingCapabilityType capability in capabilities)
            {
                // Get the capability value from the BuildingMenu
                float value = buildingMenu.GetCapabilityValue(capability);

                AddCapability(capability.ToString(), value);
                Debug.Log($"Updated capability: {capability} with value: {value}");
            }
        }
    }
}
