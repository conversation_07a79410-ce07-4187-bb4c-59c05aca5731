using UnityEngine;
using UnityEditor;
using System.IO;
using HeroSystem;

[CustomEditor(typeof(GameBalanceData))]
public class GameBalanceDataEditor : Editor
{
    private bool showHeroFoldout = true;
    private bool showBuildingFoldout = true;
    private bool showResearchFoldout = true;
    private bool showResourceFoldout = true;
    private bool showScalingFoldout = true;

    public override void OnInspectorGUI()
    {
        GameBalanceData balanceData = (GameBalanceData)target;

        EditorGUILayout.Space();
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Game Balance Data", EditorStyles.boldLabel);
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space();

        // Global multipliers
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Global Balance Parameters", EditorStyles.boldLabel);
        balanceData.globalResourceMultiplier = EditorGUILayout.Slider("Resource Multiplier", balanceData.globalResourceMultiplier, 0.1f, 5f);
        balanceData.globalTimeMultiplier = EditorGUILayout.Slider("Time Multiplier", balanceData.globalTimeMultiplier, 0.1f, 5f);
        balanceData.globalPowerMultiplier = EditorGUILayout.Slider("Power Multiplier", balanceData.globalPowerMultiplier, 0.1f, 5f);
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space();

        // Hero balance data
        showHeroFoldout = EditorGUILayout.Foldout(showHeroFoldout, "Hero Balance Data", true);
        if (showHeroFoldout)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Heroes", EditorStyles.boldLabel, GUILayout.Width(100));
            if (GUILayout.Button("Add Hero", GUILayout.Width(100)))
            {
                balanceData.heroBalanceData.Add(new HeroBalanceData());
            }
            EditorGUILayout.EndHorizontal();

            for (int i = 0; i < balanceData.heroBalanceData.Count; i++)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"Hero {i+1}: {balanceData.heroBalanceData[i].heroName}", EditorStyles.boldLabel);
                if (GUILayout.Button("Remove", GUILayout.Width(80)))
                {
                    balanceData.heroBalanceData.RemoveAt(i);
                    i--;
                    continue;
                }
                EditorGUILayout.EndHorizontal();

                var hero = balanceData.heroBalanceData[i];
                hero.heroName = EditorGUILayout.TextField("Name", hero.heroName);
                hero.heroType = (HeroType)EditorGUILayout.EnumPopup("Type", hero.heroType);
                hero.rarity = (HeroRarity)EditorGUILayout.EnumPopup("Rarity", hero.rarity);
                hero.initialPower = EditorGUILayout.IntField("Initial Power", hero.initialPower);

                EditorGUILayout.LabelField("Base Stats", EditorStyles.boldLabel);
                hero.baseStats.Attack = EditorGUILayout.IntField("Attack", hero.baseStats.Attack);
                hero.baseStats.Defense = EditorGUILayout.IntField("Defense", hero.baseStats.Defense);
                hero.baseStats.HP = EditorGUILayout.IntField("HP", hero.baseStats.HP);
                hero.baseStats.MarchCapacity = EditorGUILayout.IntField("March Capacity", hero.baseStats.MarchCapacity);

                EditorGUILayout.LabelField("Troop Modifiers", EditorStyles.boldLabel);
                hero.troopModifiers.AttackBonus = EditorGUILayout.FloatField("Attack Bonus %", hero.troopModifiers.AttackBonus);
                hero.troopModifiers.DefenseBonus = EditorGUILayout.FloatField("Defense Bonus %", hero.troopModifiers.DefenseBonus);
                hero.troopModifiers.HpBonus = EditorGUILayout.FloatField("HP Bonus %", hero.troopModifiers.HpBonus);

                EditorGUILayout.LabelField("Progression", EditorStyles.boldLabel);
                hero.expScalingFactor = EditorGUILayout.FloatField("Exp Scaling Factor", hero.expScalingFactor);
                hero.rankScalingFactor = EditorGUILayout.FloatField("Rank Scaling Factor", hero.rankScalingFactor);
                hero.powerScalingFactor = EditorGUILayout.FloatField("Power Scaling Factor", hero.powerScalingFactor);

                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space();

        // Building balance data
        showBuildingFoldout = EditorGUILayout.Foldout(showBuildingFoldout, "Building Balance Data", true);
        if (showBuildingFoldout)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Buildings", EditorStyles.boldLabel, GUILayout.Width(100));
            if (GUILayout.Button("Add Building", GUILayout.Width(100)))
            {
                balanceData.buildingBalanceData.Add(new BuildingBalanceData());
            }
            EditorGUILayout.EndHorizontal();

            for (int i = 0; i < balanceData.buildingBalanceData.Count; i++)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"Building {i+1}: {balanceData.buildingBalanceData[i].buildingName}", EditorStyles.boldLabel);
                if (GUILayout.Button("Remove", GUILayout.Width(80)))
                {
                    balanceData.buildingBalanceData.RemoveAt(i);
                    i--;
                    continue;
                }
                EditorGUILayout.EndHorizontal();

                var building = balanceData.buildingBalanceData[i];
                building.buildingName = EditorGUILayout.TextField("Name", building.buildingName);
                building.buildingType = (BuildingUpgrade.BuildingType)EditorGUILayout.EnumPopup("Type", building.buildingType);
                building.maxLevel = EditorGUILayout.IntField("Max Level", building.maxLevel);

                EditorGUILayout.LabelField("Base Costs", EditorStyles.boldLabel);
                building.baseFoodCost = EditorGUILayout.IntField("Food Cost", building.baseFoodCost);
                building.baseWoodCost = EditorGUILayout.IntField("Wood Cost", building.baseWoodCost);
                building.baseMetalCost = EditorGUILayout.IntField("Metal Cost", building.baseMetalCost);
                building.baseGoldCost = EditorGUILayout.IntField("Gold Cost", building.baseGoldCost);

                EditorGUILayout.LabelField("Base Values", EditorStyles.boldLabel);
                building.baseUpgradeTime = EditorGUILayout.FloatField("Upgrade Time (s)", building.baseUpgradeTime);
                building.baseBattlePower = EditorGUILayout.IntField("Battle Power", building.baseBattlePower);
                building.baseWelfare = EditorGUILayout.IntField("Welfare", building.baseWelfare);
                building.baseResourceGeneration = EditorGUILayout.FloatField("Resource Generation", building.baseResourceGeneration);

                EditorGUILayout.LabelField("Scaling Factors", EditorStyles.boldLabel);
                building.resourceGrowthFactor = EditorGUILayout.FloatField("Resource Growth", building.resourceGrowthFactor);
                building.timeGrowthFactor = EditorGUILayout.FloatField("Time Growth", building.timeGrowthFactor);
                building.battlePowerGrowthFactor = EditorGUILayout.FloatField("BP Growth", building.battlePowerGrowthFactor);
                building.welfareGrowthFactor = EditorGUILayout.FloatField("Welfare Growth", building.welfareGrowthFactor);
                building.resourceGenerationGrowthFactor = EditorGUILayout.FloatField("Resource Gen Growth", building.resourceGenerationGrowthFactor);

                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space();

        // Research balance data
        showResearchFoldout = EditorGUILayout.Foldout(showResearchFoldout, "Research Balance Data", true);
        if (showResearchFoldout)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Research", EditorStyles.boldLabel, GUILayout.Width(100));
            if (GUILayout.Button("Add Research", GUILayout.Width(100)))
            {
                balanceData.researchBalanceData.Add(new ResearchBalanceData());
            }
            EditorGUILayout.EndHorizontal();

            for (int i = 0; i < balanceData.researchBalanceData.Count; i++)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"Research {i+1}: {balanceData.researchBalanceData[i].researchName}", EditorStyles.boldLabel);
                if (GUILayout.Button("Remove", GUILayout.Width(80)))
                {
                    balanceData.researchBalanceData.RemoveAt(i);
                    i--;
                    continue;
                }
                EditorGUILayout.EndHorizontal();

                var research = balanceData.researchBalanceData[i];
                research.researchName = EditorGUILayout.TextField("Name", research.researchName);
                research.nodeType = (ResearchNode.NodeType)EditorGUILayout.EnumPopup("Node Type", research.nodeType);
                research.bonusType = (ResearchNode.BonusType)EditorGUILayout.EnumPopup("Bonus Type", research.bonusType);
                research.isPercentageBonus = EditorGUILayout.Toggle("Is Percentage Bonus", research.isPercentageBonus);

                EditorGUILayout.LabelField("Base Costs", EditorStyles.boldLabel);
                research.startingFoodCost = EditorGUILayout.IntField("Food Cost", research.startingFoodCost);
                research.startingWoodCost = EditorGUILayout.IntField("Wood Cost", research.startingWoodCost);
                research.startingMetalCost = EditorGUILayout.IntField("Metal Cost", research.startingMetalCost);
                research.startingGoldCost = EditorGUILayout.IntField("Gold Cost", research.startingGoldCost);

                EditorGUILayout.LabelField("Base Values", EditorStyles.boldLabel);
                research.startingTime = EditorGUILayout.FloatField("Time (s)", research.startingTime);
                research.startingPower = EditorGUILayout.IntField("Power", research.startingPower);

                EditorGUILayout.LabelField("Tiers", EditorStyles.boldLabel);
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Tiers", GUILayout.Width(100));
                if (GUILayout.Button("Add Tier", GUILayout.Width(100)))
                {
                    research.tiers.Add(new ResearchTierBalanceData { tierNumber = research.tiers.Count + 1 });
                }
                EditorGUILayout.EndHorizontal();

                for (int j = 0; j < research.tiers.Count; j++)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField($"Tier {research.tiers[j].tierNumber}", GUILayout.Width(100));
                    research.tiers[j].maxLevel = EditorGUILayout.IntField("Max Level", research.tiers[j].maxLevel);
                    research.tiers[j].bonus = EditorGUILayout.IntField("Bonus", research.tiers[j].bonus);
                    research.tiers[j].requiredLabLevel = EditorGUILayout.IntField("Lab Level", research.tiers[j].requiredLabLevel);
                    if (GUILayout.Button("X", GUILayout.Width(30)))
                    {
                        research.tiers.RemoveAt(j);
                        j--;
                    }
                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space();

        // Resource generation data
        showResourceFoldout = EditorGUILayout.Foldout(showResourceFoldout, "Resource Generation", true);
        if (showResourceFoldout)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            balanceData.resourceGeneration.foodGenerationRate = EditorGUILayout.FloatField("Food Generation Rate", balanceData.resourceGeneration.foodGenerationRate);
            balanceData.resourceGeneration.woodGenerationRate = EditorGUILayout.FloatField("Wood Generation Rate", balanceData.resourceGeneration.woodGenerationRate);
            balanceData.resourceGeneration.metalGenerationRate = EditorGUILayout.FloatField("Metal Generation Rate", balanceData.resourceGeneration.metalGenerationRate);
            balanceData.resourceGeneration.goldGenerationRate = EditorGUILayout.FloatField("Gold Generation Rate", balanceData.resourceGeneration.goldGenerationRate);

            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space();

        // Upgrade scaling data
        showScalingFoldout = EditorGUILayout.Foldout(showScalingFoldout, "Upgrade Scaling", true);
        if (showScalingFoldout)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            balanceData.upgradeScaling.heroRankScaling = EditorGUILayout.FloatField("Hero Rank Scaling", balanceData.upgradeScaling.heroRankScaling);
            balanceData.upgradeScaling.buildingUpgradeTimeScaling = EditorGUILayout.FloatField("Building Time Scaling", balanceData.upgradeScaling.buildingUpgradeTimeScaling);
            balanceData.upgradeScaling.buildingResourceCostScaling = EditorGUILayout.FloatField("Building Cost Scaling", balanceData.upgradeScaling.buildingResourceCostScaling);
            balanceData.upgradeScaling.researchTimeScaling = EditorGUILayout.FloatField("Research Time Scaling", balanceData.upgradeScaling.researchTimeScaling);
            balanceData.upgradeScaling.researchCostScaling = EditorGUILayout.FloatField("Research Cost Scaling", balanceData.upgradeScaling.researchCostScaling);

            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space();

        // Action buttons
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("Export to JSON"))
        {
            ExportToJson(balanceData);
        }

        if (GUILayout.Button("Import from JSON"))
        {
            ImportFromJson(balanceData);
        }

        EditorGUILayout.EndHorizontal();

        // Apply changes
        if (GUI.changed)
        {
            EditorUtility.SetDirty(balanceData);
        }
    }

    private void ExportToJson(GameBalanceData balanceData)
    {
        string path = EditorUtility.SaveFilePanel(
            "Export Balance Data to JSON",
            "",
            "game_balance_data.json",
            "json"
        );

        if (!string.IsNullOrEmpty(path))
        {
            // Create a serializable version
            BalanceDataSerializable serializableData = new BalanceDataSerializable
            {
                globalResourceMultiplier = balanceData.globalResourceMultiplier,
                globalTimeMultiplier = balanceData.globalTimeMultiplier,
                globalPowerMultiplier = balanceData.globalPowerMultiplier,

                heroBalanceData = balanceData.heroBalanceData,
                buildingBalanceData = balanceData.buildingBalanceData,
                researchBalanceData = balanceData.researchBalanceData,
                resourceGeneration = balanceData.resourceGeneration,
                upgradeScaling = balanceData.upgradeScaling
            };

            // Convert to JSON
            string jsonData = JsonUtility.ToJson(serializableData, true);

            // Save to file
            File.WriteAllText(path, jsonData);

            Debug.Log($"Balance data exported to: {path}");
        }
    }

    private void ImportFromJson(GameBalanceData balanceData)
    {
        string path = EditorUtility.OpenFilePanel(
            "Import Balance Data from JSON",
            "",
            "json"
        );

        if (!string.IsNullOrEmpty(path))
        {
            string jsonData = File.ReadAllText(path);

            // Parse JSON
            BalanceDataSerializable serializableData = JsonUtility.FromJson<BalanceDataSerializable>(jsonData);

            if (serializableData != null)
            {
                // Apply to balance data
                balanceData.globalResourceMultiplier = serializableData.globalResourceMultiplier;
                balanceData.globalTimeMultiplier = serializableData.globalTimeMultiplier;
                balanceData.globalPowerMultiplier = serializableData.globalPowerMultiplier;

                balanceData.heroBalanceData = serializableData.heroBalanceData;
                balanceData.buildingBalanceData = serializableData.buildingBalanceData;
                balanceData.researchBalanceData = serializableData.researchBalanceData;
                balanceData.resourceGeneration = serializableData.resourceGeneration;
                balanceData.upgradeScaling = serializableData.upgradeScaling;

                EditorUtility.SetDirty(balanceData);

                Debug.Log($"Balance data imported from: {path}");
            }
            else
            {
                Debug.LogError("Failed to parse JSON data!");
            }
        }
    }
}
