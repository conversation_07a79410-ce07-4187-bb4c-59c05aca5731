using UnityEngine;

public class CameraZoom : MonoBehaviour
{
    [Header("Zoom Settings")]
    [SerializeField] private float zoomDuration = 1f;
    [SerializeField] private float zoomedSize = 5f;
    [SerializeField] private float defaultSize = 10f;
    [SerializeField] private Vector3 zoomedPosition = new Vector3(120, -150, 0);
    [SerializeField] private Vector3 defaultPosition = new Vector3(120, -150, 0);
    
    private Camera cam;
    private Vector3 targetPosition;
    private float targetSize;
    private float currentZoomTime;
    private bool isZooming;

    private void Awake()
    {
        cam = GetComponent<Camera>();
        targetPosition = defaultPosition;
        targetSize = defaultSize;
        cam.orthographicSize = defaultSize;
    }

    public void ZoomIn()
    {
        targetPosition = zoomedPosition;
        targetSize = zoomedSize;
        currentZoomTime = 0f;
        isZooming = true;
    }

    public void ZoomOut()
    {
        targetPosition = defaultPosition;
        targetSize = defaultSize;
        currentZoomTime = 0f;
        isZooming = true;
    }

    private void Update()
    {
        if (!isZooming) return;

        currentZoomTime += Time.deltaTime;
        float progress = currentZoomTime / zoomDuration;

        if (progress >= 1f)
        {
            transform.localPosition = targetPosition;
            cam.orthographicSize = targetSize;
            isZooming = false;
        }
        else
        {
            transform.localPosition = Vector3.Lerp(transform.localPosition, targetPosition, progress);
            cam.orthographicSize = Mathf.Lerp(cam.orthographicSize, targetSize, progress);
        }
    }
}
