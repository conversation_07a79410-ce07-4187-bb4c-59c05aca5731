# Training System Design Document

## Overview

This document outlines the **troop training system** featuring 3 troop types. The system allows players to train troops at various levels, upgrade them, and use speed-up items when necessary.

It includes:

- UI behavior
- Troop data structure
- Resource and time calculations
- Upgrade logic
- Integration with Inventory system, and resources system (in GameManager).
- Integration with (future) Combat system.
---

## Troop Types

There are **three troop types**, each with its own set of stats and progression requirements:

1. **Infantry**
2. **Rider**
3. **Ranged**

Each troop type has levels from **1 to 10**. The progression is based on the building level, with each level requiring a certain amount of resources and time to train.
Each consequent troop level unlocks when the corresponding building (Infantry Barracks, Rider Barracks, Ranged Barracks) level is upgraded 3 more levels: level 1 troops are unlocked by default (building level 1), level 2 troops are unlocked at building level 4, level 3 at building level 7, and so on.
---

## Data Structure: Troop Scriptable Object

Each troop is defined by a **Scriptable Object** containing the following fields:

| Field          | Description                       |
|----------------|-----------------------------------|
| `Type`         | Infantry / Rider / Ranged         |
| `Name`         | Name of the troop level           |
| `Description`  | Lore/functional description       |
| `Image`        | Sprite shown in detail panel      |
| `Thumbnail`    | Miniature version for scroll list |
| `Level`        | Level of the troop (1–10)         |

> ✅ This system enables easy expansion and balancing.

---

## UI Elements

### A. **Main UI Panels**

#### 1. Scroll View Panel
- Displays **thumbnails** of all levels of the selected troop type.
- Thumbnail prefab contains:
  - Button
  - Frame (only active for selected thumbnail)
  - Image
  - Lock image (for locked levels)
  - Level text

> By default, the **highest unlocked level** is selected.

#### 2. Troop Detail Panel
Displays:
- Troop type name
- Troop name
- Food, Wood, Metal, Time requirements
- Total trained quantity
- Slider + input field for training amount
- Train button
- Upgrade button *(only visible if higher level exists)*

#### 3. Countdown UI
Appears after starting training/upgrading:
- Shows remaining time
- Speed Up button *(visible only during active training)*
- Cancel button (Cancelling training/upgrading will refund resources) 

#### 4. Speed Up Confirmation UI
Allows player to:
- Confirm use of available speed-ups
- Use general speed-ups if needed
- Show required vs available speed-up durations
- Cancel button

#### 5. Completion UI
Shown briefly after completion:
- Displays gained Battle Power from completed training
- Lasts 2 seconds

#### 6. Troop Stats UI
Shown Stats for each troop type:
- It's triggered by Stats button
- Once triggered it hides the detail panel and shows the stats

#### 7. Image Background UI
Image changes based on selected troop type and level:
- Each troop type has different backgrounds.
- Each levels has a different background:
  - Image 1: Level 1-3
  - Image 2: Level 4-6
  - Image 3: Level 7-9
  - Image 4: Level 10

#### 8. Resources Replenishment UI
Offers option to replenish resources from inventory in case of unsufficient resources for training:
- If accepted, it uses enough resources from inventory to train the troop
- If rejected, it closes the UI
- If the player has no resources in the inventory, it closes the UI
- Cancel Replenishment button

#### 9. Close button
It closes the training UI.

---

## Core Mechanics

### 1. **Training Capacity**

- Base capacity: **5**
- Extra capacity granted via research (via GameManager)

Training Capacity = 5 +  training capacity gained from research (GameManager).

---

### 2. **Training Requirements**

If any resource requirement is **not met**, display the number in **red**, and **disable** the “Train” button.

---

### 3. **Upgrade Mechanic**

Upgrades follow this formula:

$$
\text{UpgradeCost}(a \to b) = \text{TrainingCost}(b) \times \left(1 - \frac{r^{b-a} - 1}{r^{\text{max}} - 1}\right)
$$

Where:
- $ a $ = current level
- $ b $ = target level
- $ r = 1.4 $
- $ \text{max} = 10 $

> ✅ Upgrading always costs **less than full training cost**  
> ✅ Smaller gap → larger discount  
> ✅ Larger gap → closer to full cost

---

### 4. **Speed-Up System**

#### Behavior:
- Clicking **Speed Up** opens confirmation UI
- Required speed-up duration is calculated based on remaining training time
- Prioritize using **training-specific speed-ups**
- If not enough, use **general speed-ups**
- Unused time is **converted back into speed-up minutes** (e.g., 60 min speed-up used with 40 min needed → 20 min returned as smaller units)

---

### 5. **Training Completion**

When training finishes:
- Add the trained count to the total for that level
- Add Battle Power (BP) to global BP pool (via GameManager)

---

## Combat Stats per Troop Type

Stats grow quadratically:

### 1. **Infantry**
- Attack: `level * level + 20 * level + 24`
- Defense: `level * level + 20 * level + 86`
- Health: `level * level + 20 * level + 87`

### 2. **Rider**
- Attack: `level * level + 20 * level + 28`
- Defense: `level * level + 20 * level + 85`
- Health: `level * level + 20 * level + 82`

### 3. **Ranged**
- Attack: `level * level + 20 * level + 35`
- Defense: `level * level + 20 * level + 74`
- Health: `level * level + 20 * level + 75`

---

## Time & Battle Power Formulas

### Training Time:
```csharp
time = 6 * level * level + 6;
```

### Battle Power:
```csharp
bp = (int)Math.Round(Math.Pow(1.5, level - 1));
```

---

## Resource Requirements

### 1. **Infantry**
```csharp
food = 40 * level + 20 * level * level;
wood = food; // Same as food
metal = level > 2 ? 25 * (level - 2) * (level - 2) : 0;
```

### 2. **Rider**
```csharp
food = level > 2 ? 30 * (level - 1) * (level - 1) : 0;
wood = 50 * level + 20 * level * level;
metal = level > 1 ? 25 * (level - 1) * (level - 1) : 0;
```

### 3. **Ranged**
```csharp
food = 50 * level + 20 * level * level;
wood = level <= 2 ? 30 * (3 - level) * (3 - level) : 0;
metal = level > 1 ? 25 * (level - 1) * (level - 1) : 0;
```

---

## UI Logic Summary

| Action                   | Behavior                                    |
|--------------------------|---------------------------------------------|
| Trigger UI from button   | Display respective troop type               |
| Select thumbnail         | Show details in training panel              |
| Locked thumbnail         | Show locked panel, hide info panel          |
| Train or Upgrade started | Show countdown text and Speed Up button     |
| Speed Up clicked         | Open Speed Up UI                            |
| Complete training        | Add to total, add BP, show completion UI    |

---
