# GameDataManager - Updated Implementation

## Overview

This is an updated implementation of the GameDataManager that addresses the issues with the original implementation. The main changes are:

1. Using `Mono.Data.Sqlite` instead of the custom SQLite plugin to avoid conflicts
2. Using an adapter pattern to integrate with the existing GameManager without modifying it
3. Fixing the partial class and namespace issues

## Files

- **GameDataManagerSqlite.cs**: The main implementation of the GameDataManager using Mono.Data.Sqlite
- **GameManagerAdapter.cs**: An adapter that connects the GameManager to the GameDataManager
- **GameManagerFix.cs**: A utility that fixes the GameManager class by adding the partial modifier

## How to Use

### 1. Add the Required References

Make sure you have the following references in your project:
- Mono.Data.Sqlite.dll
- System.Data.dll

You can add these references in Unity by going to:
1. Edit > Project Settings > Player
2. Under "Other Settings", find "Scripting Define Symbols"
3. Add `USE_MONO_SQLITE` to the list

### 2. Attach the GameManagerAdapter

Instead of modifying the GameManager class directly, attach the GameManagerAdapter component to the same GameObject that has the GameManager component:

```csharp
// In your scene setup script or editor
var gameManagerObject = GameObject.Find("GameManager");
if (!gameManagerObject.GetComponent<GameManagerAdapter>())
{
    gameManagerObject.AddComponent<GameManagerAdapter>();
}
```

### 3. Use the GameManager as Usual

You can continue to use the GameManager class as you normally would. The GameManagerAdapter will intercept the calls and save the data to the SQLite database.

## Implementation Details

### GameDataManagerSqlite.cs

This class implements the GameDataManager using Mono.Data.Sqlite. It provides the following functionality:

- Database connection management
- Table creation
- CRUD operations for game data
- Transaction support

### GameManagerAdapter.cs

This class acts as a bridge between the GameManager and the GameDataManager. It:

- Initializes the GameDataManager
- Loads data from the database into the GameManager
- Saves data from the GameManager to the database
- Migrates data from PlayerPrefs to SQLite

### GameManagerFix.cs

This utility class fixes the GameManager class by adding the partial modifier. It runs in the Unity Editor when scripts are recompiled.

## Database Schema

The GameDataManager creates the following tables:

- **Resources**: Stores gold, food, wood, metal, battle power, and welfare
- **Heroes**: Stores hero progress including rank, level, experience, and lock status
- **HeroSkills**: Stores hero skill levels and bonuses
- **Buildings**: Stores building data including level and position
- **BuildingCapabilities**: Stores building capability values
- **CompletedResearch**: Stores completed research nodes
- **ActiveResearch**: Stores in-progress research
- **FPSGameProgress**: Stores FPS game progress
- **FPSLevelStats**: Stores statistics for each FPS level
- **FPSLevelHeroes**: Stores which heroes completed each level
- **InventoryItems**: Stores inventory item quantities
- **TroopCounts**: Stores troop counts by type and level

## API Reference

### GameDataManager

```csharp
// Get instance
GameDataManager dataManager = GameDataManager.Instance;

// Get resources
dataManager.GetResources(out int gold, out int food, out int wood, out int metal, out int battlePower, out int welfare);

// Update resources
dataManager.UpdateResources(1000, 5000, 5000, 5000, 100, 50);

// Save hero progress
List<GameDataManager.SkillProgressData> skills = new List<GameDataManager.SkillProgressData>();
// Add skill data...
dataManager.SaveHeroProgress("HeroName", 2, 10, 500, 20, false, skills);

// Get hero progress
GameDataManager.HeroProgressData heroProgress = dataManager.GetHeroProgress("HeroName");

// Get all heroes progress
List<GameDataManager.HeroProgressData> allHeroes = dataManager.GetAllHeroesProgress();
```

### GameManagerAdapter

```csharp
// Get reference to the adapter
GameManagerAdapter adapter = gameManager.GetComponent<GameManagerAdapter>();

// Save resources to database
adapter.SaveResourcesToDatabase();

// Migrate data from PlayerPrefs to SQLite
adapter.MigrateFromPlayerPrefs();
```

## Troubleshooting

### Common Issues

1. **SQLite DLL not found**: Make sure you have the Mono.Data.Sqlite.dll and System.Data.dll references in your project.

2. **Database file not found**: The database file is created in the Application.persistentDataPath directory. Make sure this directory exists and is writable.

3. **Migration fails**: If migration fails, check the Unity console for error messages. You may need to manually migrate the data.

4. **GameManager not found**: Make sure the GameManagerAdapter is attached to the same GameObject as the GameManager.

### Debugging

You can enable debug logging by adding the following to your project:

```csharp
// In a startup script
Debug.unityLogger.filterLogType = LogType.Log;
```

This will show all debug messages in the Unity console.

## Best Practices

1. **Use Transactions**: When performing multiple related operations, use transactions to ensure data integrity.
2. **Close the Connection**: Call `dataManager.Close()` when you're done with the database to free up resources.
3. **Error Handling**: Always handle exceptions that might occur during database operations.
4. **Data Migration**: Use the provided migration utilities to migrate existing PlayerPrefs data to SQLite.

## Advantages Over PlayerPrefs

1. **Performance**: SQLite is more efficient for storing and retrieving large amounts of data.
2. **Data Integrity**: SQLite supports transactions, which ensure that related operations are performed atomically.
3. **Flexibility**: SQLite supports complex queries and relationships between data.
4. **Scalability**: SQLite can handle much larger amounts of data than PlayerPrefs.
5. **Security**: SQLite data is stored in a binary format, making it more secure than PlayerPrefs.
