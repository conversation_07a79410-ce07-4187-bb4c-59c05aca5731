using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BuildingCapabilitiesManager : MonoBehaviour
{
    public static BuildingCapabilitiesManager Instance { get; private set; }

    [Header("Resource Settings")]
    public int startingFood = 1000;
    public int startingWood = 1000;
    public int startingMetal = 1000;
    public float startingEnergy = 500;

    [Header("Resource Generation")]
    public float generationInterval = 60f;
    public float energyProductionRate = 10f;
    public float energyConsumptionRate = 5f;

    // Resource tracking
    private float resourceTimer;
    private float energyTimer;
    private float totalEnergy;
    private float totalEnergyConsumption;

    // Resource capabilities
    private Dictionary<BuildingCapabilityType, float> globalCapabilities = new Dictionary<BuildingCapabilityType, float>();

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            Debug.Log("BuildingCapabilitiesManager initialized");
        }
        else
        {
            Destroy(gameObject);
            return;
        }

        // Initialize global capabilities
        InitializeGlobalCapabilities();
    }

    private void Start()
    {
        // Initialize resources
        totalEnergy = startingEnergy;
        totalEnergyConsumption = energyConsumptionRate;

        // Update UI
        GameManager.Instance.UpdateResourceUI();
    }

    private void Update()
    {
        resourceTimer += Time.deltaTime;
        energyTimer += Time.deltaTime;

        if (resourceTimer >= generationInterval)
        {
            GenerateResources();
            resourceTimer = 0;
        }

        if (energyTimer >= 3600f) // 1 hour in seconds
        {
            UpdateEnergy();
            energyTimer = 0;
        }
    }

    private void InitializeGlobalCapabilities()
    {
        // Initialize resource production capabilities
        SetCapabilityValue(BuildingCapabilityType.ResourceProduction_Food, startingFood);
        SetCapabilityValue(BuildingCapabilityType.ResourceProduction_Wood, startingWood);
        SetCapabilityValue(BuildingCapabilityType.ResourceProduction_Metal, startingMetal);
        SetCapabilityValue(BuildingCapabilityType.ResourceProduction_Energy, startingEnergy);

        // Initialize other global capabilities
        SetCapabilityValue(BuildingCapabilityType.TrainingSpeed, 100);
        SetCapabilityValue(BuildingCapabilityType.ResearchSpeed, 100);
        SetCapabilityValue(BuildingCapabilityType.MarchCapacity, 100);
    }

    private void GenerateResources()
    {
        float energyDeficit = totalEnergy - totalEnergyConsumption;
        float deficitPercentage = (totalEnergyConsumption > 0) ? (energyDeficit / totalEnergyConsumption * 100f) : 0f;
        float penaltyMultiplier = 1f;

        if (deficitPercentage < 0)
        {
            if (deficitPercentage >= -20f)
                penaltyMultiplier = 0.75f; // Mild Deficit
            else
                penaltyMultiplier = 0.5f;  // Severe Deficit
        }

        // Generate resources based on production capabilities
        int foodProduction = Mathf.RoundToInt(GetCapabilityValue(BuildingCapabilityType.ResourceProduction_Food) * penaltyMultiplier / 60);
        int woodProduction = Mathf.RoundToInt(GetCapabilityValue(BuildingCapabilityType.ResourceProduction_Wood) * penaltyMultiplier / 60);
        int metalProduction = Mathf.RoundToInt(GetCapabilityValue(BuildingCapabilityType.ResourceProduction_Metal) * penaltyMultiplier / 60);

        // Add resources to GameManager
        GameManager.Instance.AddResources(foodProduction, woodProduction, metalProduction);

        // Apply penalties if needed
        if (penaltyMultiplier == 0.5f)
        {
            // Apply severe deficit penalties
            ApplyCapabilityPenalty(BuildingCapabilityType.TrainingSpeed, penaltyMultiplier);
            ApplyCapabilityPenalty(BuildingCapabilityType.ResearchSpeed, penaltyMultiplier);
            ApplyCapabilityPenalty(BuildingCapabilityType.MarchCapacity, penaltyMultiplier);
        }
        else
        {
            // Reset penalties
            ResetPenalties();
        }
    }

    private void UpdateEnergy()
    {
        totalEnergy += energyProductionRate;
        totalEnergyConsumption = energyConsumptionRate;
        Debug.Log($"Energy updated: {totalEnergy}/{totalEnergyConsumption}");
    }

    private void ApplyCapabilityPenalty(BuildingCapabilityType type, float multiplier)
    {
        if (globalCapabilities.ContainsKey(type))
        {
            float baseValue = globalCapabilities[type];
            float penaltyValue = baseValue * multiplier;
            globalCapabilities[type] = penaltyValue;
            Debug.Log($"Applied penalty to {type}: {baseValue} -> {penaltyValue}");
        }
    }

    private void ResetPenalties()
    {
        // Reset capabilities to their base values
        SetCapabilityValue(BuildingCapabilityType.TrainingSpeed, 100);
        SetCapabilityValue(BuildingCapabilityType.ResearchSpeed, 100);
        SetCapabilityValue(BuildingCapabilityType.MarchCapacity, 100);
        Debug.Log("Reset all capability penalties");
    }

    // Public methods for accessing and modifying capabilities

    public float GetCapabilityValue(BuildingCapabilityType type)
    {
        if (globalCapabilities.ContainsKey(type))
        {
            return globalCapabilities[type];
        }
        return 0;
    }

    public void SetCapabilityValue(BuildingCapabilityType type, float value)
    {
        globalCapabilities[type] = value;
    }

    public void AddCapabilityValue(BuildingCapabilityType type, float amount)
    {
        if (globalCapabilities.ContainsKey(type))
        {
            globalCapabilities[type] += amount;
        }
        else
        {
            globalCapabilities[type] = amount;
        }
    }

    public void AddResources(int foodAmount, int woodAmount, int metalAmount)
    {
        if (foodAmount > 0)
        {
            AddCapabilityValue(BuildingCapabilityType.ResourceProduction_Food, foodAmount);
        }

        if (woodAmount > 0)
        {
            AddCapabilityValue(BuildingCapabilityType.ResourceProduction_Wood, woodAmount);
        }

        if (metalAmount > 0)
        {
            AddCapabilityValue(BuildingCapabilityType.ResourceProduction_Metal, metalAmount);
        }

        GameManager.Instance.UpdateResourceUI();
    }

    public float GetTotalEnergy()
    {
        return totalEnergy;
    }

    public float GetEnergyConsumption()
    {
        return totalEnergyConsumption;
    }

    public float GetEnergyDeficit()
    {
        return totalEnergy - totalEnergyConsumption;
    }
    
    // Method to recalculate all capabilities from buildings in the scene
    public void RecalculateCapabilities()
    {
        // Reset training-related capabilities to base values
        SetCapabilityValue(BuildingCapabilityType.TrainingSpeed, 100);
        SetCapabilityValue(BuildingCapabilityType.TrainingCapacity, 0);
        
        // Find all buildings with capabilities
        BuildingCapabilities[] allBuildingCapabilities = FindObjectsByType<BuildingCapabilities>(FindObjectsSortMode.None);
        
        // Collect and aggregate capabilities
        foreach (BuildingCapabilities bc in allBuildingCapabilities)
        {
            foreach (var capability in bc.capabilities)
            {
                // For training speed, we add the percentage bonus
                if (capability.capabilityType == BuildingCapabilityType.TrainingSpeed)
                {
                    // Add the percentage bonus to the base 100%
                    AddCapabilityValue(BuildingCapabilityType.TrainingSpeed, capability.baseValue);
                    Debug.Log($"Added {capability.baseValue}% training speed bonus from {bc.gameObject.name}");
                }
                // For training capacity, we add the absolute value
                else if (capability.capabilityType == BuildingCapabilityType.TrainingCapacity)
                {
                    AddCapabilityValue(BuildingCapabilityType.TrainingCapacity, capability.baseValue);
                    Debug.Log($"Added {capability.baseValue} training capacity from {bc.gameObject.name}");
                }
            }
        }
        
        Debug.Log($"Recalculated capabilities - Training Speed: {GetCapabilityValue(BuildingCapabilityType.TrainingSpeed)}%, " +
                 $"Training Capacity: {GetCapabilityValue(BuildingCapabilityType.TrainingCapacity)}");
    }
}
