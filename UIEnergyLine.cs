using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(RectTransform))]
public class UIEnergyLine : MonoBehaviour
{
    [SerializeField] private Color glowColor = Color.cyan;
    [SerializeField] private float moveSpeed = 2f;
    [SerializeField] private float spawnRate = 0.5f;
    [SerializeField] private float glowSize = 1f;
    [SerializeField] private float _glowIntensity = 1f;

    public float glowIntensity
    {
        get => _glowIntensity;
        set
        {
            _glowIntensity = value;
            if (energyMaterial != null)
            {
                energyMaterial.SetFloat("_GlowIntensity", _glowIntensity);
            }
        }
    }

    private Material energyMaterial;
    private float nextSpawnTime;
    private RectTransform rectTransform;

    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        
        Image energyImage = GetComponent<Image>();
        energyMaterial = new Material(Shader.Find("Custom/UIEnergyLine"));
        energyImage.material = energyMaterial;
    }

    private void Update()
    {
        if (Time.time >= nextSpawnTime)
        {
            SpawnGlowSpot();
            nextSpawnTime = Time.time + Random.Range(spawnRate * 0.5f, spawnRate * 1.5f);
        }

        energyMaterial.SetColor("_GlowColor", glowColor);
        energyMaterial.SetFloat("_FlowTime", Time.time * moveSpeed);
        energyMaterial.SetFloat("_GlowSize", glowSize);
        energyMaterial.SetFloat("_GlowIntensity", _glowIntensity);
    }

    private void SpawnGlowSpot()
    {
        float randomOffset = Random.Range(0f, 1f);
        energyMaterial.SetFloat("_SpawnOffset", randomOffset);
    }
}
