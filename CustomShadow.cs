using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(RectTransform))]
public class CustomShadow : MonoBehaviour
{
    [SerializeField] private Color shadowColor = new Color(0, 0, 0, 0.5f);
    [SerializeField] private Vector2 shadowOffset = new Vector2(5f, -5f);
    [SerializeField] private bool useGraphicAlpha = true;

    private RectTransform rectTransform;
    private Image shadowImage;
    private Graphic targetGraphic;

    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        targetGraphic = GetComponent<Graphic>();

        // Create shadow object
        GameObject shadowObj = new GameObject("Shadow");
        shadowObj.transform.SetParent(transform);
        shadowObj.transform.SetSiblingIndex(0); // Place shadow behind the target

        // Setup shadow image
        shadowImage = shadowObj.AddComponent<Image>();
        shadowImage.sprite = targetGraphic is Image ? ((Image)targetGraphic).sprite : null;
        shadowImage.color = shadowColor;
        
        // Copy RectTransform properties
        RectTransform shadowRect = shadowObj.GetComponent<RectTransform>();
        shadowRect.anchorMin = Vector2.zero;
        shadowRect.anchorMax = Vector2.one;
        shadowRect.sizeDelta = Vector2.zero;
        shadowRect.pivot = rectTransform.pivot;
        shadowRect.anchoredPosition = shadowOffset;
    }

    private void LateUpdate()
    {
        if (useGraphicAlpha && targetGraphic != null)
        {
            Color newColor = shadowColor;
            newColor.a *= targetGraphic.color.a;
            shadowImage.color = newColor;
        }
    }
}
