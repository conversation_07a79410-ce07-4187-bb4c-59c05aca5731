using UnityEngine;
using System.Collections.Generic;
using System.IO;

/// <summary>
/// A simple implementation of game data manager that uses PlayerPrefs for storage.
/// This is a temporary solution until you can set up SQLite properly.
/// </summary>
public class SimpleGameDataManager
{
    private static SimpleGameDataManager _instance;
    public static SimpleGameDataManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new SimpleGameDataManager();
            }
            return _instance;
        }
    }

    // Constructor is private to enforce singleton pattern
    private SimpleGameDataManager()
    {
        Initialize();
    }

    /// <summary>
    /// Initialize the data manager
    /// </summary>
    public void Initialize()
    {
        Debug.Log("SimpleGameDataManager initialized");
    }

    /// <summary>
    /// Close the data manager
    /// </summary>
    public void Close()
    {
        // Nothing to close in this simple implementation
    }

    #region Resource Management

    /// <summary>
    /// Get current resource values
    /// </summary>
    public void GetResources(out int gold, out int food, out int wood, out int metal, out int battlePower, out int welfare)
    {
        gold = PlayerPrefs.GetInt("Resources_Gold", 1000);
        food = PlayerPrefs.GetInt("Resources_Food", 100000);
        wood = PlayerPrefs.GetInt("Resources_Wood", 100000);
        metal = PlayerPrefs.GetInt("Resources_Metal", 100000);
        battlePower = PlayerPrefs.GetInt("Resources_BattlePower", 0);
        welfare = PlayerPrefs.GetInt("Resources_Welfare", 0);
    }

    /// <summary>
    /// Update resource values
    /// </summary>
    public void UpdateResources(int gold, int food, int wood, int metal, int battlePower, int welfare)
    {
        PlayerPrefs.SetInt("Resources_Gold", gold);
        PlayerPrefs.SetInt("Resources_Food", food);
        PlayerPrefs.SetInt("Resources_Wood", wood);
        PlayerPrefs.SetInt("Resources_Metal", metal);
        PlayerPrefs.SetInt("Resources_BattlePower", battlePower);
        PlayerPrefs.SetInt("Resources_Welfare", welfare);
        PlayerPrefs.Save();
    }

    #endregion

    #region Hero Management

    /// <summary>
    /// Save hero progress
    /// </summary>
    public void SaveHeroProgress(string heroName, int rank, int expLevel, int currentExp, int currentPts, bool isLocked, List<SkillProgressData> skills)
    {
        HeroProgressData heroProgress = new HeroProgressData
        {
            HeroName = heroName,
            Rank = rank,
            ExpLevel = expLevel,
            CurrentExp = currentExp,
            CurrentPts = currentPts,
            IsLocked = isLocked,
            Skills = skills
        };

        string json = JsonUtility.ToJson(heroProgress);
        PlayerPrefs.SetString("HeroProgress_" + heroName, json);
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Get hero progress
    /// </summary>
    public HeroProgressData GetHeroProgress(string heroName)
    {
        if (PlayerPrefs.HasKey("HeroProgress_" + heroName))
        {
            string json = PlayerPrefs.GetString("HeroProgress_" + heroName);
            return JsonUtility.FromJson<HeroProgressData>(json);
        }
        return null;
    }

    /// <summary>
    /// Get all heroes progress
    /// </summary>
    public List<HeroProgressData> GetAllHeroesProgress()
    {
        List<HeroProgressData> result = new List<HeroProgressData>();
        
        // Find all hero progress keys
        foreach (string key in PlayerPrefs.GetString("PlayerPrefs_Keys", "").Split(','))
        {
            if (key.StartsWith("HeroProgress_"))
            {
                string heroName = key.Substring("HeroProgress_".Length);
                HeroProgressData heroProgress = GetHeroProgress(heroName);
                if (heroProgress != null)
                {
                    result.Add(heroProgress);
                }
            }
        }
        
        return result;
    }

    #endregion

    #region Data Classes

    [System.Serializable]
    public class SkillProgressData
    {
        public string HeroName;
        public string SkillName;
        public int Level;
        public float AttackBonus;
        public float DamageBonus;
        public float DefenseBonus;
        public float HealthBonus;
        public float MarchCapacityBonus;
        public float RallyCapacityBonus;
    }

    [System.Serializable]
    public class HeroProgressData
    {
        public string HeroName;
        public int Rank;
        public int ExpLevel;
        public int CurrentExp;
        public int CurrentPts;
        public bool IsLocked;
        public List<SkillProgressData> Skills = new List<SkillProgressData>();
    }

    #endregion
}
