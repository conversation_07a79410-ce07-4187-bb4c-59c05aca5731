using UnityEngine;

public class ButtonHandler : MonoBehaviour
{
    public BuildingManager buildingManager; // Reference to the BuildingManager script
    public GameObject buildingPrefab; // The building prefab to place

    public void OnBuildButtonClicked()
    {
        if (buildingManager != null && buildingPrefab != null)
        {
            buildingManager.SetBuildingPrefab(buildingPrefab);
        }
        else
        {
            Debug.LogError("BuildingManager or buildingPrefab is not assigned!");
        }
    }
}