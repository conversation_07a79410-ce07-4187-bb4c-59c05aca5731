Shader "Custom/UIGlow"
{
    Properties
    {
        [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
        _GlowColor ("Glow Color", Color) = (1,1,1,0.5)
        _GlowIntensity ("Glow Intensity", Range(0, 2)) = 0.5
        _GlowOffset ("Glow Offset", Range(1, 20)) = 4
        _GlowPower ("Glow Power", Range(1, 4)) = 2
        
        // Required for UI
        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255
        _ColorMask ("Color Mask", Float) = 15
    }
    
    SubShader
    {
        Tags 
        { 
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend SrcAlpha OneMinusSrcAlpha
        ColorMask [_ColorMask]

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float4 color : COLOR;
            };
            
            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float4 color : COLOR;
                float2 texcoord : TEXCOORD1;
            };
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
            float4 _GlowColor;
            float _GlowIntensity;
            float _GlowOffset;
            float _GlowPower;
            
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.texcoord = v.uv;
                o.color = v.color;
                return o;
            }
            
            float SampleAlpha(float2 uv)
            {
                return tex2D(_MainTex, uv).a;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                // Sample the original texture
                fixed4 original = tex2D(_MainTex, i.uv);
                
                // Calculate pixel size for the glow effect
                float2 pixelSize = float2(1.0 / _ScreenParams.x, 1.0 / _ScreenParams.y) * _GlowOffset;
                
                // Initialize variables for glow calculation
                float totalAlpha = 0;
                int samples = 16; // Fixed number of samples for better performance
                
                // Sample points in a circular pattern
                for(int j = 0; j < samples; j++)
                {
                    float angle = (j / (float)samples) * 6.28318530718; // 2*PI
                    float2 offset = float2(cos(angle), sin(angle)) * pixelSize;
                    totalAlpha += SampleAlpha(i.uv + offset);
                }
                
                // Calculate glow based on surrounding pixels
                float avgAlpha = totalAlpha / samples;
                float glowFactor = pow(avgAlpha, _GlowPower);
                
                // Only show glow where the original texture is transparent
                glowFactor *= (1 - original.a);
                
                // Combine original texture with glow
                fixed4 finalColor = original;
                finalColor.rgb += _GlowColor.rgb * glowFactor * _GlowIntensity;
                finalColor.a = max(original.a, glowFactor * _GlowColor.a);
                
                // Apply UI color
                finalColor *= i.color;
                
                return finalColor;
            }
            ENDCG
        }
    }
}