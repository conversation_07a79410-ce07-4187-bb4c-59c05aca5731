using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections; // Required for IEnumerator

public class BuildingSelection : MonoBehaviour
{
    public GameObject movementArrows;
    public GameObject selectionOverlay;
    public GameObject smokeEffectPrefab;
    public LayerMask buildingLayer;
    public LayerMask wallLayer;

    private static BuildingSelection currentlySelected;
    private bool isDragging = false;
    private Vector3 offset;
    private Grid buildingGrid;
    private CameraControl cameraControl;
    private Vector3 lastValidPosition;
    private float initialY;
    private bool isReadyToMove = false;
    private BuildingMenu buildingMenu;


    public static BuildingSelection CurrentlySelected => currentlySelected;
    public static bool IsDraggingBuilding { get; private set; }

    private bool isNewlyPlaced = true; // Tracks whether the building was just instantiated


    private void Start()
    {
        // Get the BuildingMenu component
        buildingMenu = GetComponent<BuildingMenu>();
        if (buildingMenu == null)
        {
            Debug.LogWarning($"BuildingMenu component not found on {gameObject.name}. Menu functionality will be limited.");
        }

        Debug.Log($"IsDraggingBuilding: {IsDraggingBuilding}, CurrentlySelected: {CurrentlySelected?.gameObject.name}");

        buildingGrid = FindFirstObjectByType<Grid>();
        cameraControl = FindFirstObjectByType<CameraControl>();
        initialY = transform.position.y;

        isNewlyPlaced = !IsPrebuilt(); // Ensure prebuilt buildings are NOT newly placed

        if (!isNewlyPlaced) // If prebuilt, ensure no menu shows at the start
        {
            movementArrows?.SetActive(false);
            selectionOverlay?.SetActive(false);
        }

        if (!IsPrebuilt())
        {
            transform.position = SnapToGrid(transform.position);
        }

        movementArrows = transform.Find("MovementArrows")?.gameObject;
        if (movementArrows != null)
            movementArrows.SetActive(false);

        if (selectionOverlay != null)
            selectionOverlay.SetActive(false);
    }


    // Method to check if the building was pre-placed
    private bool IsPrebuilt()
    {
        return gameObject.scene.IsValid(); // Pre-built buildings exist in the scene before runtime
    }


    void OnMouseEnter()
    {
        Debug.Log($"Hovering over {gameObject.name}");
    }


    private void OnMouseDown()
    {
        // Prevent selection if clicking on a UI element
        if (EventSystem.current.IsPointerOverGameObject())
        {
            Debug.Log("Click blocked by UI.");
            return;
        }

        if (currentlySelected != this)
        {
            currentlySelected?.DeselectBuilding();
            SelectBuilding();

            // Block UI interaction for one frame to prevent accidental clicks
            StartCoroutine(BlockUIForOneFrame());
        }
    }

    private IEnumerator BlockUIForOneFrame()
    {
        yield return new WaitForEndOfFrame(); // Wait until the end of the current frame
        EventSystem.current.SetSelectedGameObject(null); // Deselect any UI element
        Input.ResetInputAxes(); // Clear any lingering input
    }


    private void Update()
    {
        if (isReadyToMove && Input.GetMouseButtonDown(0))
        {
            StartMoving();
            isReadyToMove = false;
        }

        if (isDragging && currentlySelected == this)
        {
            Vector3 targetPosition = GetMouseWorldPosition() + offset;
            Vector3 snappedPosition = SnapToGrid(targetPosition);
            transform.position = new Vector3(snappedPosition.x, initialY + 0.1f, snappedPosition.z);

            if (Input.GetMouseButtonUp(0))
            {
                StopMoving();
            }
        }

        if (currentlySelected == this && Input.GetMouseButtonDown(1)) // Right-click
        {
            if (!IsClickingSelectedBuilding())
            {
                DeselectBuilding();
            }
        }

    }

    private bool IsClickingSelectedBuilding()
    {
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out RaycastHit hit))
        {
            return hit.collider.gameObject == gameObject; // Returns true if clicking the selected building
        }
        return false;
    }


    public void StartMoving()
    {
        Debug.Log($"StartMoving called for {gameObject.name} | isReadyToMove: {isReadyToMove} | CurrentlySelected: {currentlySelected?.gameObject.name}");
        if (currentlySelected != this || !isReadyToMove) return;

        movementArrows?.SetActive(true);
        isDragging = true;
        IsDraggingBuilding = true;
        cameraControl.enabled = false;

        offset = transform.position - GetMouseWorldPosition();

        // Block UI interaction while dragging
        EventSystem.current.SetSelectedGameObject(null);
    }

    public void StopMoving()
    {
        isDragging = false;
        IsDraggingBuilding = false;
        cameraControl.enabled = true;

        Vector3 snappedPosition = SnapToGrid(transform.position);
        if (IsValidPlacement(snappedPosition))
        {
            transform.position = new Vector3(snappedPosition.x, initialY, snappedPosition.z);
            lastValidPosition = transform.position;

            if (smokeEffectPrefab != null)
            {
                GameObject smokeInstance = Instantiate(smokeEffectPrefab, transform.position, Quaternion.identity);
                Destroy(smokeInstance, 1f);
            }

            movementArrows?.SetActive(false);
            isNewlyPlaced = false;

            // Reset input after placement
            Input.ResetInputAxes();
            DeselectBuilding();
        }
        else
        {
            transform.position = lastValidPosition;
        }
    }





    private Vector3 GetMouseWorldPosition()
    {
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        return Physics.Raycast(ray, out RaycastHit hit) ? hit.point : transform.position;
    }

    private Vector3 SnapToGrid(Vector3 position)
    {
        if (buildingGrid != null)
        {
            Vector3Int cell = buildingGrid.WorldToCell(position);
            return buildingGrid.CellToWorld(cell) + new Vector3(0.5f, 0.1f, 0.5f);
        }
        return position;
    }

    private bool IsValidPlacement(Vector3 position)
    {
        Vector3 halfSize = GetColliderSize() / 2;
        if (Physics.OverlapBox(position, halfSize, Quaternion.identity, wallLayer).Length > 0) return false;

        foreach (var collider in Physics.OverlapBox(position, halfSize, Quaternion.identity, buildingLayer))
        {
            if (collider.gameObject != gameObject) return false;
        }
        return true;
    }

    private Vector3 GetColliderSize()
    {
        BoxCollider boxCollider = GetComponent<BoxCollider>();
        return boxCollider ? boxCollider.size : Vector3.one;
    }

    public void SelectBuilding()
    {
        if (!gameObject.activeInHierarchy) return;

        if (currentlySelected != null && currentlySelected != this)
        {
            currentlySelected.DeselectBuilding();
        }

        currentlySelected = this;

        if (buildingMenu != null)
        {
            if (isNewlyPlaced)
            {
                isNewlyPlaced = false;
            }
            else
            {
                // Delay menu activation slightly to prevent immediate interaction
                StartCoroutine(DelayedMenuActivation());
            }
        }

        if (selectionOverlay != null)
            selectionOverlay.SetActive(true);
    }

    private IEnumerator DelayedMenuActivation()
    {
        yield return new WaitForEndOfFrame(); // Ensures input is cleared before showing menu
        buildingMenu.ShowMenu();
    }




    public void DeselectBuilding()
    {
        // Check if currentlySelected still exists before using it
        if (currentlySelected == null || currentlySelected.gameObject == null)
        {
            currentlySelected = null;
            return;
        }

        // Hide the menu if it exists
        if (currentlySelected.buildingMenu != null)
        {
            currentlySelected.buildingMenu.HideMenu();
        }

        if (currentlySelected.selectionOverlay != null)
        {
            currentlySelected.selectionOverlay.SetActive(false);
        }

        currentlySelected = null;
    }


    public void EnableDragging()
    {
        // Hide the menu if it exists
        if (buildingMenu != null)
        {
            buildingMenu.HideMenu();
        }

        movementArrows?.SetActive(true);
        isReadyToMove = true;
        IsDraggingBuilding = false; // Not dragging yet, just ready to move
    }

}
