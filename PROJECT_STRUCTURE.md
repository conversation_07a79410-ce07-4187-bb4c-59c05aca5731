# Project Structure

This document outlines the structure of the Unity project to prevent confusion about file locations and dependencies.

## Root Folders

- **Assets/** - Main Unity assets folder
  - **Building System/** - All building-related scripts and prefabs
  - **Training System/** - Training mechanics and UI
  - **Hero System/** - Hero management and abilities
  - **Game System/** - Core game mechanics and managers
  - **UI System/** - UI components and controllers
  - **Balance System/** - Game balancing tools and data
  - **Mini Games/** - Mini-game implementations
  - **Resources/** - Assets loaded at runtime
  - **Shaders/** - Custom shader files

## Key Files by System

### Building System
- **BuildingUpgrade.cs** - Handles building levels and upgrades
- **BuildingCapabilities.cs** - Manages building capabilities and bonuses
- **BuildingCapabilitiesManager.cs** - Global manager for all building capabilities
- **BuildingMenu.cs** - UI interaction for buildings

### Training System
- **TrainingManager.cs** - Core manager for troop training
- **TrainingBuilding.cs** - Connects barracks buildings to training system
- **TrainingUI.cs** - UI for troop training and management
- **TroopSO.cs** - Scriptable object for troop data

### Hero System
- **HeroData.cs** - Core hero data class (in HeroSystem namespace)
- **HeroSelector.cs** - UI for selecting heroes
- **SkillUpgradeUI.cs** - UI for upgrading hero skills
- **HeroThumbnailItem.cs** - UI component for hero thumbnails

### Game System
- **GameManager.cs** - Central manager for game state and resources
- **InventorySystem.cs** - Manages player inventory

### UI System
- **ExpeditionFPS.cs** - UI for FPS expedition mode
- **UIElementAnimator.cs** - Handles UI animations

### Balance System
- **GameBalanceData.cs** - Scriptable object for game balance data

### Mini Games
- **FPSGameUI.cs** - UI for FPS mini-game
- **ZombieStatsManager.cs** - Manages zombie stats in FPS game

## Namespaces

- **HeroSystem** - Contains hero-related classes (HeroData, HeroType, HeroRarity, etc.)

## Dependencies

- Training System depends on Building System for building capabilities
- All systems depend on Game System for resource management
- UI components in each system reference their respective managers

## File References

When files are mentioned in conversation, they should be assumed to be in their respective system folder unless specified otherwise. For example:
- "TrainingManager.cs" refers to "Assets/Training System/TrainingManager.cs"
- "BuildingCapabilities.cs" refers to "Assets/Building System/BuildingCapabilities.cs"
- "HeroData.cs" refers to "Assets/Hero System/HeroData.cs"

## Naming Conventions

- All system folders use PascalCase with spaces
- All script files use PascalCase without spaces
- All prefabs end with "Prefab"
