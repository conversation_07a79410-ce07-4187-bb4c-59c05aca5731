using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

/// <summary>
/// Component to register objects with the NotificationManager.
/// Attach this to any object that needs to show notifications.
/// </summary>
public class NotificationSystem : MonoBehaviour
{
    [Header("Notification Settings")]
    [SerializeField] private string targetId; // Unique ID for this notification target
    [SerializeField] private string processType; // Type of process (Training, Research, etc.)
    [SerializeField] private GameObject notificationTarget; // Object to attach notification to (if null, uses this GameObject)
    [SerializeField] private bool enabledOnStart = true; // Whether notification is enabled on start
    
    [Header("Resource Requirements")]
    [SerializeField] private bool checkResources = true; // Whether to check resource requirements
    [SerializeField] private int requiredFood = 0;
    [SerializeField] private int requiredWood = 0;
    [SerializeField] private int requiredMetal = 0;
    [SerializeField] private int requiredGold = 0;

    [Header("Custom Availability")]
    [SerializeField] private bool useCustomAvailabilityCheck = false;
    [SerializeField] private string customCheckMethodName; // Name of method to call for custom availability check
    
    private Func<bool> availabilityCheck;
    private bool isEnabled = true;
    
    // Public properties
    public string TargetId => targetId;
    public string ProcessType => processType;

    private void Start()
    {
        // If no target ID is provided, generate one based on GameObject name and instance ID
        if (string.IsNullOrEmpty(targetId))
        {
            targetId = $"{gameObject.name}_{gameObject.GetInstanceID()}";
        }

        // If no notification target is provided, use this GameObject
        if (notificationTarget == null)
        {
            notificationTarget = gameObject;
        }

        // Set up availability check function
        SetupAvailabilityCheck();

        // Set initial enabled state
        isEnabled = enabledOnStart;

        // Register with NotificationManager if enabled
        if (NotificationManager.Instance != null && isEnabled)
        {
            NotificationManager.Instance.RegisterNotificationTarget(targetId, notificationTarget, processType, availabilityCheck);
        }
        else if (NotificationManager.Instance == null)
        {
            Debug.LogWarning("NotificationManager instance not found. Notification system will not work.");
        }
    }

    private void OnDestroy()
    {
        // Unregister from NotificationManager
        if (NotificationManager.Instance != null)
        {
            NotificationManager.Instance.UnregisterNotificationTarget(targetId);
        }
    }

    /// <summary>
    /// Set up the availability check function based on settings
    /// </summary>
    private void SetupAvailabilityCheck()
    {
        if (useCustomAvailabilityCheck && !string.IsNullOrEmpty(customCheckMethodName))
        {
            // Use reflection to get the custom method
            try
            {
                var method = GetType().GetMethod(customCheckMethodName, System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Public);
                if (method != null && method.ReturnType == typeof(bool) && method.GetParameters().Length == 0)
                {
                    availabilityCheck = () => (bool)method.Invoke(this, null);
                }
                else
                {
                    Debug.LogError($"Custom availability check method '{customCheckMethodName}' not found or has incorrect signature. Method should return bool and take no parameters.");
                    availabilityCheck = DefaultAvailabilityCheck;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Error setting up custom availability check: {e.Message}");
                availabilityCheck = DefaultAvailabilityCheck;
            }
        }
        else
        {
            availabilityCheck = DefaultAvailabilityCheck;
        }
    }

    /// <summary>
    /// Default availability check based on resource requirements
    /// </summary>
    private bool DefaultAvailabilityCheck()
    {
        if (!checkResources)
        {
            return true;
        }

        if (GameManager.Instance == null)
        {
            return false;
        }

        return GameManager.Instance.HasEnoughResources(requiredFood, requiredWood, requiredMetal) && 
               GameManager.Instance.HasEnoughGold(requiredGold);
    }

    /// <summary>
    /// Set resource requirements for this notification
    /// </summary>
    public void SetResourceRequirements(int food, int wood, int metal, int gold)
    {
        requiredFood = food;
        requiredWood = wood;
        requiredMetal = metal;
        requiredGold = gold;
        checkResources = true;
    }

    /// <summary>
    /// Force check notification status
    /// </summary>
    public void ForceCheckNotification()
    {
        if (NotificationManager.Instance != null)
        {
            NotificationManager.Instance.CheckAllNotifications();
        }
    }

    /// <summary>
    /// Set the target ID for this notification
    /// </summary>
    public void SetTargetId(string id)
    {
        // Unregister old target ID if already registered
        if (NotificationManager.Instance != null && !string.IsNullOrEmpty(targetId))
        {
            NotificationManager.Instance.UnregisterNotificationTarget(targetId);
        }

        targetId = id;

        // Register with new target ID
        if (NotificationManager.Instance != null && isEnabled)
        {
            NotificationManager.Instance.RegisterNotificationTarget(targetId, notificationTarget, processType, availabilityCheck);
        }
    }

    /// <summary>
    /// Set the process type for this notification
    /// </summary>
    public void SetProcessType(string type)
    {
        processType = type;

        // Update registration if already registered
        if (NotificationManager.Instance != null && !string.IsNullOrEmpty(targetId) && isEnabled)
        {
            NotificationManager.Instance.UnregisterNotificationTarget(targetId);
            NotificationManager.Instance.RegisterNotificationTarget(targetId, notificationTarget, processType, availabilityCheck);
        }
    }

    /// <summary>
    /// Set custom availability check function
    /// </summary>
    public void SetCustomAvailabilityCheck(Func<bool> checkFunction)
    {
        availabilityCheck = checkFunction;
        useCustomAvailabilityCheck = true;

        // Update registration if already registered
        if (NotificationManager.Instance != null && !string.IsNullOrEmpty(targetId) && isEnabled)
        {
            NotificationManager.Instance.UnregisterNotificationTarget(targetId);
            NotificationManager.Instance.RegisterNotificationTarget(targetId, notificationTarget, processType, availabilityCheck);
        }
    }

    /// <summary>
    /// Enable or disable this notification
    /// </summary>
    public void SetEnabled(bool enabled)
    {
        if (isEnabled == enabled)
        {
            return;
        }

        isEnabled = enabled;

        if (NotificationManager.Instance != null && !string.IsNullOrEmpty(targetId))
        {
            if (isEnabled)
            {
                NotificationManager.Instance.RegisterNotificationTarget(targetId, notificationTarget, processType, availabilityCheck);
            }
            else
            {
                NotificationManager.Instance.UnregisterNotificationTarget(targetId);
            }
        }
    }
}
