using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;

public class RewardShineEffect : MonoBehaviour
{
    public Image shineEffect; // Assign a separate glow image
    public float shineDuration = 0.8f; // Adjust for slower/faster glow

    void Start()
    {
        Color startColor = shineEffect.color;
        startColor.a = 0f;
        shineEffect.color = startColor;

        // Fade in and out forever
        shineEffect.DOFade(1f, shineDuration).SetLoops(-1, LoopType.Yoyo);
    }
}
