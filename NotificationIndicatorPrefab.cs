using UnityEngine;
using UnityEditor;

#if UNITY_EDITOR
/// <summary>
/// Editor script to create a notification indicator prefab.
/// This is only used in the editor to create the prefab.
/// </summary>
public class NotificationIndicatorPrefab : MonoBehaviour
{
    [MenuItem("Tools/Create Notification Indicator Prefab")]
    public static void CreateNotificationIndicatorPrefab()
    {
        // Create parent GameObject
        GameObject indicatorObj = new GameObject("NotificationIndicator");
        indicatorObj.AddComponent<RectTransform>();

        // Add NotificationIndicator component
        NotificationIndicator indicator = indicatorObj.AddComponent<NotificationIndicator>();

        // Create glow background
        GameObject glowObj = new GameObject("GlowBackground");
        glowObj.transform.SetParent(indicatorObj.transform);
        RectTransform glowRect = glowObj.AddComponent<RectTransform>();
        glowRect.anchorMin = Vector2.zero;
        glowRect.anchorMax = Vector2.one;
        glowRect.offsetMin = new Vector2(-10, -10);
        glowRect.offsetMax = new Vector2(10, 10);
        UnityEngine.UI.Image glowImage = glowObj.AddComponent<UnityEngine.UI.Image>();
        glowImage.sprite = AssetDatabase.GetBuiltinExtraResource<Sprite>("UI/Skin/Knob.psd");
        glowImage.color = new Color(0, 1, 1, 0.5f);

        // Create icon
        GameObject iconObj = new GameObject("Icon");
        iconObj.transform.SetParent(indicatorObj.transform);
        RectTransform iconRect = iconObj.AddComponent<RectTransform>();
        iconRect.anchorMin = Vector2.zero;
        iconRect.anchorMax = Vector2.one;
        iconRect.offsetMin = Vector2.zero;
        iconRect.offsetMax = Vector2.zero;
        UnityEngine.UI.Image iconImage = iconObj.AddComponent<UnityEngine.UI.Image>();
        iconImage.sprite = AssetDatabase.GetBuiltinExtraResource<Sprite>("UI/Skin/UISprite.psd");

        // Create label
        GameObject labelObj = new GameObject("Label");
        labelObj.transform.SetParent(indicatorObj.transform);
        RectTransform labelRect = labelObj.AddComponent<RectTransform>();
        labelRect.anchorMin = new Vector2(0, 0);
        labelRect.anchorMax = new Vector2(1, 0);
        labelRect.offsetMin = new Vector2(0, -30);
        labelRect.offsetMax = new Vector2(0, 0);
        TMPro.TextMeshProUGUI labelText = labelObj.AddComponent<TMPro.TextMeshProUGUI>();
        labelText.text = "Available";
        labelText.fontSize = 14;
        labelText.alignment = TMPro.TextAlignmentOptions.Center;
        labelText.color = Color.white;

        // Set references in the NotificationIndicator component using reflection
        // since the fields are serialized but might not be directly accessible
        var iconField = typeof(NotificationIndicator).GetField("iconImage", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var glowField = typeof(NotificationIndicator).GetField("glowImage", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var labelField = typeof(NotificationIndicator).GetField("labelText", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (iconField != null) iconField.SetValue(indicator, iconImage);
        if (glowField != null) glowField.SetValue(indicator, glowImage);
        if (labelField != null) labelField.SetValue(indicator, labelText);

        // Create prefab
        string prefabPath = "Assets/UI System/Prefabs/NotificationIndicator.prefab";

        // Ensure directory exists
        string directory = System.IO.Path.GetDirectoryName(prefabPath);
        if (!System.IO.Directory.Exists(directory))
        {
            System.IO.Directory.CreateDirectory(directory);
        }

        // Create prefab
        PrefabUtility.SaveAsPrefabAsset(indicatorObj, prefabPath);

        // Destroy the temporary object
        Object.DestroyImmediate(indicatorObj);

        Debug.Log($"Notification indicator prefab created at {prefabPath}");

        // Select the created prefab
        Selection.activeObject = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
    }
}
#endif
