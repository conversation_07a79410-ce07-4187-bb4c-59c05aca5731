using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class ResearchNodeUI : Mono<PERSON>eh<PERSON><PERSON>
{
    [Header("Basic Info")]
    [SerializeField] private Image nodeIcon;
    [SerializeField] private TextMeshProUGUI nodeName;
    [SerializeField] private TextM<PERSON><PERSON><PERSON>UGUI levelText;
    [SerializeField] private TextMeshProUGUI description;

    [Header("Upgrade Panel")]
    [SerializeField] private GameObject upgradePanel;
    [SerializeField] private TextMeshProUGUI totalBonusText;
    [SerializeField] private TextMeshProUGUI nextLevelBonusText;
    [SerializeField] private TextMeshProUGUI foodCostText;
    [SerializeField] private Text<PERSON><PERSON><PERSON><PERSON><PERSON>G<PERSON> woodCostText;
    [SerializeField] private TextMeshProUGUI metalCostText;
    [SerializeField] private TextMeshProUGUI requiredLabLevelText;
    [SerializeField] private Text<PERSON>eshP<PERSON><PERSON>GUI goldCostText;
    [SerializeField] private TextMeshP<PERSON><PERSON>G<PERSON> timeText;
    [SerializeField] private Text<PERSON>esh<PERSON><PERSON><PERSON><PERSON><PERSON> powerText;
    [SerializeField] private Button researchButton;
    [SerializeField] private Button instantButton;
    [SerializeField] private GameObject researchButtons;
    [SerializeField] private ResearchNodeButton currentNodeButton;


    [Header("Maxed Panel")]
    [SerializeField] private GameObject maxedPanel;

    [Header("UI Navigation")]
    [SerializeField] private Button closeDetailsPanelButton;
    [SerializeField] private GameObject researchUIPanel; // Parent panel containing all research UI

    [Header("Research Progress")]
    [SerializeField] private GameObject researchInProgressPanel;
    [SerializeField] private TextMeshProUGUI countdownText;
    [SerializeField] private Button cancelButton;

    private ResearchNodeButton currentSelectedButton;
    private ResearchNode currentNode;
    private ResearchTierData currentTier;
    private int currentLevel;
    private int tierIndex;
    private float remainingTime;
    private bool isResearchInProgress;
    private Coroutine researchCoroutine;

    private void Awake()
    {
        if (closeDetailsPanelButton != null)
            closeDetailsPanelButton.onClick.AddListener(CloseDetailsPanel);

        if (cancelButton != null)
            cancelButton.onClick.AddListener(CancelResearch);
    }

    private void Update()
    {
        if (isResearchInProgress)
        {
            UpdateCountdown();
        }
    }

    private void UpdateCountdown()
    {
        remainingTime -= Time.deltaTime;
        if (remainingTime <= 0)
        {
            CompleteResearch();
        }
        else
        {
            countdownText.text = $"{FormatTime(remainingTime)}";
        }
    }

    public void DisplayNodeDetails(ResearchNode node, int tierIndex, ResearchNodeButton button)
    {
        currentNodeButton = button;

        if (currentSelectedButton != null)
            currentSelectedButton.SetSelected(false);

        currentSelectedButton = button;

        currentNode = node;
        this.tierIndex = tierIndex;
        currentTier = node.tiers[tierIndex];

        // Get current level from GameManager instead of parameter
        currentLevel = GameManager.Instance.GetResearchLevel(node, tierIndex);

        // Update basic info
        nodeIcon.sprite = currentTier.icon;
        nodeName.text = currentTier.nodeName;
        description.text = currentTier.description;

        // Check if node is maxed
        bool isMaxed = IsNodeMaxed();
        upgradePanel.SetActive(!isMaxed);
        maxedPanel.SetActive(isMaxed);

        if (!isMaxed)
        {
            UpdateUpgradePanel();
        }
        else
        {
            // Add this to update the level display even when maxed
            if (levelText != null)
            {
                levelText.text = $"{currentLevel}/{currentTier.maxLevel}";
            }
        }
    }

    private void CloseDetailsPanel()
    {
        // Deselect current button
        if (currentSelectedButton != null)
        {
            currentSelectedButton.SetSelected(false);
            currentSelectedButton = null;
        }

        gameObject.SetActive(false);
    }

    private void CloseResearchUI()
    {
        if (researchUIPanel != null)
            researchUIPanel.SetActive(false);
    }

    private void OnDisable()
    {
        // Ensure button is deselected when UI is disabled
        if (currentSelectedButton != null)
        {
            currentSelectedButton.SetSelected(false);
            currentSelectedButton = null;
        }
    }

    private void UpdateUpgradePanel()
    {
        currentLevel = GameManager.Instance.GetResearchLevel(currentNode, tierIndex);

        // Level display
        if (levelText != null)
        {
            levelText.text = $"{currentLevel}/{currentTier.maxLevel}";
        }

        // Only calculate next level costs if not maxed
        if (!IsNodeMaxed())
        {
            // Calculate costs for next level
            int nextLevel = currentLevel + 1;
            int foodCost = currentNode.GetFoodCost(tierIndex, nextLevel);
            int woodCost = currentNode.GetWoodCost(tierIndex, nextLevel);
            int metalCost = currentNode.GetMetalCost(tierIndex, nextLevel);
            int goldCost = currentNode.GetGoldCost(tierIndex, nextLevel);
            float time = currentNode.GetTime(tierIndex, nextLevel);
            int power = currentNode.GetPower(tierIndex, nextLevel);

             // Check if player has enough resources
            bool hasEnoughFood = GameManager.Instance.Food >= foodCost;
            bool hasEnoughWood = GameManager.Instance.Wood >= woodCost;
            bool hasEnoughMetal = GameManager.Instance.Metal >= metalCost;
            bool hasEnoughGold = GameManager.Instance.HasEnoughGold(goldCost);

            // Update cost displays with color
            foodCostText.text = $"<color={(hasEnoughFood ? "white" : "red")}>{foodCost}</color>";
            woodCostText.text = $"<color={(hasEnoughWood ? "white" : "red")}>{woodCost}</color>";
            metalCostText.text = $"<color={(hasEnoughMetal ? "white" : "red")}>{metalCost}</color>";
            goldCostText.text = $"<color={(hasEnoughGold ? "black" : "red")}>{goldCost}</color>";

            timeText.text = $"{FormatTime(time)}";
            powerText.text = $"{power}";

            // Bonus calculations
            int currentBonus = currentTier.bonus * currentLevel;
            int nextLevelBonus = currentTier.bonus * nextLevel;
            string bonusSymbol = currentNode.isPercentageBonus ? "%" : "";

            totalBonusText.text = $"{nextLevelBonus}{bonusSymbol}";
            nextLevelBonusText.text = $"+{currentTier.bonus}{bonusSymbol}";

            // Display required lab level
            if (requiredLabLevelText != null)
            {
                int currentLabLevel = GameManager.Instance.GetLabLevel();
                int requiredLevel = currentTier.requiredLabLevel;
                string colorTag = currentLabLevel >= requiredLevel ? "white" : "red";
                requiredLabLevelText.text = $"<color={colorTag}>{requiredLevel}</color>";
            }

            SetupButtons();
        }
    }

    private void SetupButtons()
    {
        bool canAfford = CheckResources(currentTier);

        researchButton.interactable = canAfford;
        instantButton.interactable = canAfford;

        researchButton.onClick.RemoveAllListeners();
        instantButton.onClick.RemoveAllListeners();

        researchButton.onClick.AddListener(() => OnResearchButtonClicked());
        instantButton.onClick.AddListener(() => OnInstantButtonClicked());
    }

    private bool CheckResources(ResearchTierData tier)
    {
        // TODO: Implement resource checking logic
        // Return true if player has enough resources for the upgrade
        return true;
    }

    private void OnResearchButtonClicked()
    {
        if (currentNode == null || IsNodeMaxed()) return;

        int nextLevel = currentLevel + 1;
        int foodCost = currentNode.GetFoodCost(tierIndex, nextLevel);
        int woodCost = currentNode.GetWoodCost(tierIndex, nextLevel);
        int metalCost = currentNode.GetMetalCost(tierIndex, nextLevel);

        // Check if player has enough resources
        if (GameManager.Instance.HasEnoughResources(foodCost, woodCost, metalCost))
        {
            // Consume resources
            GameManager.Instance.SpendResources(foodCost, woodCost, metalCost);

            // Start research process
            StartResearch();
        }
        else
        {
            Debug.Log("Not enough resources for research!");
        }
    }

    private void StartResearch()
    {
        isResearchInProgress = true;
        remainingTime = currentNode.GetTime(tierIndex, currentLevel + 1);

        // Update UI
        researchButtons.SetActive(false);
        researchInProgressPanel.SetActive(true);

        // Save research state
        GameManager.Instance.SaveResearchProgress(currentNode, tierIndex, currentLevel, remainingTime);
    }

    private void CancelResearch()
    {
        if (!isResearchInProgress) return;

        // Refund resources
        int nextLevel = currentLevel + 1;
        GameManager.Instance.AddResources(
            currentNode.GetFoodCost(tierIndex, nextLevel),
            currentNode.GetWoodCost(tierIndex, nextLevel),
            currentNode.GetMetalCost(tierIndex, nextLevel)
        );

        // Reset research state
        isResearchInProgress = false;
        researchInProgressPanel.SetActive(false);
        researchButtons.SetActive(true);

        // Clear saved research progress
        GameManager.Instance.ClearResearchProgress(currentNode, tierIndex);
    }

    private void OnInstantButtonClicked()
    {
        if (currentNode == null || IsNodeMaxed()) return;

        int goldCost = currentNode.GetGoldCost(tierIndex, currentLevel + 1);

        if (GameManager.Instance.HasEnoughGold(goldCost))
        {
            GameManager.Instance.SpendGold(goldCost);
            CompleteResearch();
        }
        else
        {
            Debug.Log("Not enough gold for instant research!");
        }
    }

    private void CompleteResearch()
    {
        isResearchInProgress = false;

        // Save research completion with the next level
        GameManager.Instance.SaveResearchCompletion(currentNode, tierIndex, currentLevel + 1);

        // Get updated level after saving
        currentLevel = GameManager.Instance.GetResearchLevel(currentNode, tierIndex);

        // Add power to total battle power
        int powerGained = currentNode.GetPower(tierIndex, currentLevel);
        GameManager.Instance.AddBattlePower(powerGained);

        // Update UI
        researchInProgressPanel.SetActive(false);

        bool isMaxed = IsNodeMaxed();
        upgradePanel.SetActive(!isMaxed);
        maxedPanel.SetActive(isMaxed);

        // Always update the level display
        if (levelText != null)
        {
            levelText.text = $"{currentLevel}/{currentTier.maxLevel}";
        }

        if (!isMaxed)
        {
            UpdateUpgradePanel();
        }

        // Update the button UI
        if (currentNodeButton != null)
        {
            currentNodeButton.UpdateButtonState();
        }

        // Update all research node buttons to reflect new state
        ResearchNodeButton[] allButtons = UnityEngine.Object.FindObjectsByType<ResearchNodeButton>(FindObjectsSortMode.None);
        foreach (var button in allButtons)
        {
            button.UpdateButtonState();
        }
    }

private bool IsNodeMaxed()
    {
        int level = GameManager.Instance.GetResearchLevel(currentNode, tierIndex);
        return level >= currentTier.maxLevel;
    }

    private string FormatTime(float timeInSeconds)
    {
        int hours = Mathf.FloorToInt(timeInSeconds / 3600);
        int minutes = Mathf.FloorToInt((timeInSeconds % 3600) / 60);
        int seconds = Mathf.FloorToInt(timeInSeconds % 60);

        if (hours > 0)
            return $"{hours}:{minutes}:{seconds}";
        else if (minutes > 0)
            return $"00:{minutes}:{seconds}";
        else
            return $"00:00:{seconds}";
    }
}