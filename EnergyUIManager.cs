using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

public class EnergyUIManager : MonoBehaviour
{
    [Header("UI Elements")]
    public RectTransform productionBar;
    public RectTransform consumptionBar;
    public TextMeshProUGUI productionText;
    public TextMeshProUGUI consumptionText;
    public TextMeshProUGUI statusText;

    [Header("Energy Values")]
    public float maxBarHeight = 200f;
    private float productionFluctuation = 0f;
    private float consumptionFluctuation = 0f;

    private BuildingCapabilities buildingCapabilities;

    private void Start()
    {
        // We'll use BuildingCapabilities.Instance instead of finding any BuildingCapabilities
        if (BuildingCapabilities.Instance == null)
        {
            // Debug.LogError("BuildingCapabilities.Instance is not available. Energy UI may not work correctly.");
            // Don't disable - we'll keep trying to find the instance in Update
        }
        else
        {
            buildingCapabilities = BuildingCapabilities.Instance;
            // Debug.Log("EnergyUIManager found BuildingCapabilities.Instance");
        }

        StartCoroutine(FluctuateBars()); // Start fluctuation
    }

    private void Update()
    {
        UpdateEnergyUI();
    }

    private void UpdateEnergyUI()
    {
        // Always try to use the singleton instance
        if (BuildingCapabilities.Instance != null)
        {
            buildingCapabilities = BuildingCapabilities.Instance;
        }
        // Fallback to finding any instance if the singleton is not available
        else if (buildingCapabilities == null)
        {
            buildingCapabilities = FindFirstObjectByType<BuildingCapabilities>();
            if (buildingCapabilities == null)
            {
                // Debug.LogError("No BuildingCapabilities found in the scene. Energy UI cannot update.");
                return;
            }
        }

        // Get energy values from BuildingCapabilities
        float production = buildingCapabilities.EnergyProduction;
        float consumption = buildingCapabilities.TotalEnergyConsumption;
        float energyBalance = buildingCapabilities.EnergyBalance; // Use the new property

        // More detailed debug logging
        // Debug.Log($"Energy UI - Using BuildingCapabilities from: {buildingCapabilities.gameObject.name}");
        // Debug.Log($"Energy UI - Production: {production}, Consumption: {consumption}, Balance: {energyBalance}");

        // Check if this is the main instance
        if (buildingCapabilities == BuildingCapabilities.Instance)
        {
            // Debug.Log("Energy UI is using the main BuildingCapabilities.Instance");
        }
        else
        {
            // Debug.LogWarning("Energy UI is NOT using the main BuildingCapabilities.Instance!");
        }

        // Normalize values between 0 and maxBarHeight (ensure we don't divide by zero)
        float maxValue = Mathf.Max(production, consumption, 100f); // Use at least 100 as the max value
        float productionHeight = Mathf.Clamp((production / maxValue) * maxBarHeight + productionFluctuation, 10f, maxBarHeight);
        float consumptionHeight = Mathf.Clamp((consumption / maxValue) * maxBarHeight + consumptionFluctuation, 10f, maxBarHeight);

        // Smoothly update Bar Heights
        if (productionBar != null)
            productionBar.sizeDelta = new Vector2(productionBar.sizeDelta.x, Mathf.Lerp(productionBar.sizeDelta.y, productionHeight, Time.deltaTime * 2f));

        if (consumptionBar != null)
            consumptionBar.sizeDelta = new Vector2(consumptionBar.sizeDelta.x, Mathf.Lerp(consumptionBar.sizeDelta.y, consumptionHeight, Time.deltaTime * 2f));

        // Update Text Values
        if (productionText != null)
            productionText.text = $"{Mathf.RoundToInt(production)}";

        if (consumptionText != null)
            consumptionText.text = $"{Mathf.RoundToInt(consumption)}";

        // Update Status
        if (statusText != null)
        {
            // Use the energyBalance variable that was already declared above

            if (energyBalance >= 0)
            {
                statusText.text = "Normal";
                statusText.color = Color.green;
            }
            else if (energyBalance > -consumption * 0.3f)
            {
                statusText.text = "Mild Deficit";
                statusText.color = Color.yellow;
                // Debug.Log("Energy status: Mild Deficit");
            }
            else
            {
                statusText.text = "Severe Deficit";
                statusText.color = Color.red;
            //    Debug.Log("Energy status: Severe Deficit");
            }
        }
    }

    private IEnumerator FluctuateBars()
    {
        while (true)
        {
            // Change fluctuation values randomly within -5f to +5f
            productionFluctuation = Random.Range(-20f, 20f);
            consumptionFluctuation = Random.Range(-20f, 20f);

            yield return new WaitForSeconds(Random.Range(0.5f, 1.5f)); // Change values randomly every 0.5 - 1.5 sec
        }
    }
}
