using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Demo script showing how to control the ECG wave system
/// Attach this to a GameObject with UI controls to test the ECG functionality
/// </summary>
public class ECGDemo : MonoBehaviour
{
    [Header("ECG References")]
    public ECGWaveUI ecgWave;
    public ECGGlowController glowController;
    
    [Header("UI Controls (Optional)")]
    public Slider heartRateSlider;
    public Slider amplitudeSlider;
    public Slider speedSlider;
    public Toggle glowToggle;
    public Button alertButton;
    public Text heartRateText;
    
    [Header("Demo Settings")]
    public float minHeartRate = 0.5f; // 30 BPM
    public float maxHeartRate = 3f;   // 180 BPM
    public bool autoDemo = false;
    public float demoCycleTime = 10f;
    
    private float demoTimer = 0f;

    void Start()
    {
        // Find ECG components if not assigned
        if (ecgWave == null)
            ecgWave = FindObjectOfType<ECGWaveUI>();
            
        if (glowController == null)
            glowController = FindObjectOfType<ECGGlowController>();
        
        // Set up UI controls
        SetupUIControls();
        
        // Set initial values
        if (ecgWave != null)
        {
            UpdateHeartRateDisplay();
        }
    }

    void SetupUIControls()
    {
        if (heartRateSlider != null)
        {
            heartRateSlider.minValue = minHeartRate;
            heartRateSlider.maxValue = maxHeartRate;
            heartRateSlider.value = ecgWave != null ? ecgWave.frequency : 1f;
            heartRateSlider.onValueChanged.AddListener(OnHeartRateChanged);
        }
        
        if (amplitudeSlider != null)
        {
            amplitudeSlider.minValue = 5f;
            amplitudeSlider.maxValue = 50f;
            amplitudeSlider.value = ecgWave != null ? ecgWave.amplitude : 20f;
            amplitudeSlider.onValueChanged.AddListener(OnAmplitudeChanged);
        }
        
        if (speedSlider != null)
        {
            speedSlider.minValue = 20f;
            speedSlider.maxValue = 200f;
            speedSlider.value = ecgWave != null ? ecgWave.speed : 100f;
            speedSlider.onValueChanged.AddListener(OnSpeedChanged);
        }
        
        if (glowToggle != null)
        {
            glowToggle.isOn = ecgWave != null ? ecgWave.enableGlow : true;
            glowToggle.onValueChanged.AddListener(OnGlowToggled);
        }
        
        if (alertButton != null)
        {
            alertButton.onClick.AddListener(TriggerAlert);
        }
    }

    void Update()
    {
        if (autoDemo)
        {
            RunAutoDemo();
        }
        
        UpdateHeartRateDisplay();
    }

    void RunAutoDemo()
    {
        demoTimer += Time.deltaTime;
        float progress = (demoTimer % demoCycleTime) / demoCycleTime;
        
        if (ecgWave != null)
        {
            // Cycle through different heart rates
            float heartRate = Mathf.Lerp(minHeartRate, maxHeartRate, 
                (Mathf.Sin(progress * 2f * Mathf.PI) + 1f) * 0.5f);
            ecgWave.SetHeartRate(heartRate);
            
            // Update slider if available
            if (heartRateSlider != null)
                heartRateSlider.value = heartRate;
        }
        
        // Trigger random alerts
        if (Random.Range(0f, 1f) < 0.001f) // Very low chance per frame
        {
            TriggerAlert();
        }
    }

    void OnHeartRateChanged(float value)
    {
        if (ecgWave != null)
        {
            ecgWave.SetHeartRate(value);
        }
    }

    void OnAmplitudeChanged(float value)
    {
        if (ecgWave != null)
        {
            ecgWave.amplitude = value;
        }
    }

    void OnSpeedChanged(float value)
    {
        if (ecgWave != null)
        {
            ecgWave.speed = value;
        }
    }

    void OnGlowToggled(bool enabled)
    {
        if (ecgWave != null)
        {
            ecgWave.SetGlowEnabled(enabled);
        }
        
        if (glowController != null)
        {
            glowController.SetOuterGlowEnabled(enabled);
        }
    }

    void TriggerAlert()
    {
        if (glowController != null)
        {
            // Trigger a red alert pulse
            Color alertColor = Color.red;
            alertColor.a = 0.8f;
            
            glowController.SetOuterGlowColor(alertColor);
            glowController.TriggerGlowPulse(2f, 3f);
            
            // Reset color after alert
            StartCoroutine(ResetGlowColorAfterDelay(2.5f));
        }
        
        Debug.Log("ECG Alert Triggered!");
    }

    System.Collections.IEnumerator ResetGlowColorAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        
        if (glowController != null)
        {
            // Reset to original green color
            Color originalColor = new Color(0, 1, 0, 0.3f);
            glowController.SetOuterGlowColor(originalColor);
        }
    }

    void UpdateHeartRateDisplay()
    {
        if (heartRateText != null && ecgWave != null)
        {
            float bpm = ecgWave.frequency * 60f; // Convert Hz to BPM
            heartRateText.text = $"Heart Rate: {bpm:F0} BPM";
        }
    }

    // Public methods for external control
    public void SetNormalHeartRate()
    {
        if (ecgWave != null)
            ecgWave.SetHeartRate(1.2f); // 72 BPM
    }

    public void SetHighHeartRate()
    {
        if (ecgWave != null)
            ecgWave.SetHeartRate(2.5f); // 150 BPM
    }

    public void SetLowHeartRate()
    {
        if (ecgWave != null)
            ecgWave.SetHeartRate(0.8f); // 48 BPM
    }

    public void ToggleAutoDemo()
    {
        autoDemo = !autoDemo;
        demoTimer = 0f;
    }
}
