using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(RectTransform))]
public class UIOuterGlow : MonoBehaviour
{
    [SerializeField] private Color glowColor = Color.white;
    [SerializeField] private float glowSize = 1.2f;
    [SerializeField] private float glowSpeed = 1f;
    [SerializeField] private float glowIntensity = 1f;
    [SerializeField] private float glowFalloff = 1f;

    private Material glowMaterial;
    private RectTransform rectTransform;
    private Image glowImage;
    private Image sourceImage;

    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        sourceImage = GetComponent<Image>();
            
        GameObject glowObj = new GameObject("Glow");
        glowObj.transform.SetParent(transform);
        glowObj.transform.SetSiblingIndex(0);

        // Set transform values explicitly
        glowObj.transform.localPosition = Vector3.zero;
        glowObj.transform.localRotation = Quaternion.identity;
        glowObj.transform.localScale = Vector3.one;

        glowImage = glowObj.AddComponent<Image>();
        glowImage.sprite = sourceImage.sprite;
        glowMaterial = new Material(Shader.Find("Custom/UIOuterGlow"));
        glowImage.material = glowMaterial;
            
        RectTransform glowRect = glowObj.GetComponent<RectTransform>();
        glowRect.anchorMin = new Vector2(-0.1f, -0.1f);
        glowRect.anchorMax = new Vector2(1.1f, 1.1f);
        glowRect.offsetMin = Vector2.zero;
        glowRect.offsetMax = Vector2.zero;
    }

    private void Update()
    {
        float pulseValue = (Mathf.Sin(Time.time * glowSpeed) + 1f) * 0.5f;
        glowMaterial.SetColor("_GlowColor", glowColor * glowIntensity * pulseValue);
        glowMaterial.SetFloat("_GlowSize", glowSize);
        glowMaterial.SetFloat("_GlowFalloff", glowFalloff);
    }
}
