using UnityEngine;
using UnityEditor;
using System.IO;

public class TroopDataCreator : EditorWindow
{
    private string basePath = "Assets/Training System/Troops";
    private Sprite defaultThumbnail;
    private Sprite defaultImage;

    [MenuItem("Tools/Training System/Create Troop Data")]
    public static void ShowWindow()
    {
        GetWindow<TroopDataCreator>("Troop Data Creator");
    }

    private void OnGUI()
    {
        GUILayout.Label("Troop Data Creator", EditorStyles.boldLabel);

        basePath = EditorGUILayout.TextField("Base Path", basePath);
        defaultThumbnail = (Sprite)EditorGUILayout.ObjectField("Default Thumbnail", defaultThumbnail, typeof(Sprite), false);
        defaultImage = (Sprite)EditorGUILayout.ObjectField("Default Image", defaultImage, typeof(Sprite), false);

        if (GUILayout.Button("Create Infantry Troops"))
        {
            CreateTroops(TroopType.Infantry);
        }

        if (GUILayout.But<PERSON>("Create Rider Troops"))
        {
            CreateTroops(TroopType.Rider);
        }

        if (GUILayout.Button("Create Ranged Troops"))
        {
            CreateTroops(TroopType.Ranged);
        }

        if (GUILayout.Button("Create All Troops"))
        {
            CreateTroops(TroopType.Infantry);
            CreateTroops(TroopType.Rider);
            CreateTroops(TroopType.Ranged);
        }
    }

    private void CreateTroops(TroopType type)
    {
        // Create directory if it doesn't exist
        string typePath = Path.Combine(basePath, type.ToString());
        if (!Directory.Exists(typePath))
        {
            Directory.CreateDirectory(typePath);
        }

        // Create troops for levels 1-10
        for (int level = 1; level <= 10; level++)
        {
            // Create troop scriptable object
            TroopSO troopSO = ScriptableObject.CreateInstance<TroopSO>();
            troopSO.Type = type;
            troopSO.Level = level;
            troopSO.Name = GetTroopName(type, level);
            troopSO.Description = GetTroopDescription(type, level);
            troopSO.Thumbnail = defaultThumbnail;
            troopSO.Image = defaultImage;

            // Calculate stats
            troopSO.CalculateAll();

            // Save asset
            string assetPath = Path.Combine(typePath, $"{type}_Level{level}.asset");
            AssetDatabase.CreateAsset(troopSO, assetPath);
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log($"Created 10 {type} troops at {typePath}");
    }

    private string GetTroopName(TroopType type, int level)
    {
        switch (type)
        {
            case TroopType.Infantry:
                switch (level)
                {
                    case 1: return "Recruit";
                    case 2: return "Soldier";
                    case 3: return "Veteran";
                    case 4: return "Elite Soldier";
                    case 5: return "Guardian";
                    case 6: return "Royal Guard";
                    case 7: return "Knight";
                    case 8: return "Paladin";
                    case 9: return "Champion";
                    case 10: return "Legendary Warrior";
                    default: return $"Infantry Level {level}";
                }
            case TroopType.Rider:
                switch (level)
                {
                    case 1: return "Scout";
                    case 2: return "Horseman";
                    case 3: return "Mounted Soldier";
                    case 4: return "Cavalry";
                    case 5: return "Heavy Cavalry";
                    case 6: return "Royal Cavalry";
                    case 7: return "Knight Rider";
                    case 8: return "Dragoon";
                    case 9: return "Warlord";
                    case 10: return "Legendary Rider";
                    default: return $"Rider Level {level}";
                }
            case TroopType.Ranged:
                switch (level)
                {
                    case 1: return "Archer";
                    case 2: return "Marksman";
                    case 3: return "Sharpshooter";
                    case 4: return "Longbowman";
                    case 5: return "Elite Archer";
                    case 6: return "Royal Archer";
                    case 7: return "Sniper";
                    case 8: return "Master Archer";
                    case 9: return "Deadeye";
                    case 10: return "Legendary Archer";
                    default: return $"Ranged Level {level}";
                }
            default:
                return $"{type} Level {level}";
        }
    }

    private string GetTroopDescription(TroopType type, int level)
    {
        switch (type)
        {
            case TroopType.Infantry:
                return $"Strong melee fighters with high defense. Level {level} infantry troops are effective against ranged units.";
            case TroopType.Rider:
                return $"Fast-moving cavalry with balanced stats. Level {level} riders are effective against infantry units.";
            case TroopType.Ranged:
                return $"Long-range attackers with high attack power. Level {level} ranged troops are effective against riders.";
            default:
                return $"{type} troops of level {level}.";
        }
    }
}
