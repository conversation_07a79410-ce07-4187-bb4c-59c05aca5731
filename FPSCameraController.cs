using UnityEngine;
using System.Collections;
using DG.Tweening;

public class FPSCameraController : MonoBehaviour
{
    [Header("References")]
    public float mouseSensitivity = 60f;
    public Transform cameraTransform;
    public Transform hands;
    public Transform playerBody;
    // Removed sceneTransitionCanvas reference

    [Header("Camera Movement Limits")]
    public float minVerticalAngle = -5f;
    public float maxVerticalAngle = 10f;
    public float minHorizontalAngle = -45f;
    public float maxHorizontalAngle = 45f;

    [Header("Hands Movement")]
    public float handsRotationMultiplier = 1.5f;
    public Vector3 cameraOffset = new Vector3(0f, 0f, 0f);

    [Header("Intro Sequence")]
    public Vector3 introStartPosition = new Vector3(0, 2, -5);
    public Vector3 introStartRotation = new Vector3(20, 0, 0);
    public float introMoveDuration = 2f;
    public AnimationCurve introCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    
    // Events
    public static event System.Action OnIntroComplete;
    
    // Private fields
    private bool introComplete = false;
    private Vector3 gameplayPosition;
    private Quaternion gameplayRotation;
    private float xRotation = 0f;
    private float yRotation = 0f;
    private Vector3 initialCameraPosition;

    void Start()
    {
        Cursor.lockState = CursorLockMode.Locked;
        if (cameraTransform == null)
        {
            cameraTransform = Camera.main.transform;
        }
        
        // Store the intended gameplay position/rotation
        gameplayPosition = cameraTransform.position;
        gameplayRotation = cameraTransform.rotation;
        
        // Store initial camera position relative to hands
        initialCameraPosition = cameraTransform.position - hands.position;

        // Set initial position/rotation for intro
        cameraTransform.position = introStartPosition;
        cameraTransform.rotation = Quaternion.Euler(introStartRotation);

        // Start fade-in and intro sequence
        StartCoroutine(StartSceneSequence());
    }

    private IEnumerator StartSceneSequence()
    {
        // Short delay to ensure everything is set up
        yield return new WaitForSeconds(0.1f);

        // Start the intro sequence immediately since we're already looking at the plane
        StartCoroutine(PlayIntroSequence());
    }

    void Update()
    {
        // Only allow normal camera control after intro
        if (!introComplete) return;

        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity * Time.deltaTime;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity * Time.deltaTime;

        // Update rotations with limits
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, minVerticalAngle, maxVerticalAngle);

        yRotation += mouseX;
        yRotation = Mathf.Clamp(yRotation, minHorizontalAngle, maxHorizontalAngle);

        // Apply camera rotation
        cameraTransform.localRotation = Quaternion.Euler(xRotation, yRotation, 0f);
        
        // Update camera position to follow hands
        cameraTransform.position = hands.position + initialCameraPosition + cameraOffset;

        // Rotate player body
        playerBody.localRotation = Quaternion.Euler(0f, yRotation, 0f);

        // Apply enhanced rotation to hands
        if (hands != null)
        {
            hands.localRotation = Quaternion.Euler(
                xRotation * handsRotationMultiplier, 
                yRotation * handsRotationMultiplier, 
                0f
            );
        }
    }

    private IEnumerator PlayIntroSequence()
    {
        float elapsed = 0;

        while (elapsed < introMoveDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / introMoveDuration;
            float curvedProgress = introCurve.Evaluate(progress);

            // Smoothly interpolate position and rotation
            cameraTransform.position = Vector3.Lerp(introStartPosition, gameplayPosition, curvedProgress);
            cameraTransform.rotation = Quaternion.Lerp(
                Quaternion.Euler(introStartRotation),
                gameplayRotation,
                curvedProgress
            );

            yield return null;
        }

        // Ensure we end up exactly at the target
        cameraTransform.position = gameplayPosition;
        cameraTransform.rotation = gameplayRotation;

        // Enable normal camera control
        introComplete = true;
        OnIntroComplete?.Invoke();
    }

    // Optional: Method to skip intro
    public void SkipIntro()
    {
        StopAllCoroutines();
        cameraTransform.position = gameplayPosition;
        cameraTransform.rotation = gameplayRotation;
        introComplete = true;
    }
    
}
