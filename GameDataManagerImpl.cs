using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using System.Linq;
using System.Data;
using System.Reflection;

// Note: You need to add a reference to Mono.Data.Sqlite.dll and System.Data.dll
// Go to Edit > Project Settings > Player > Other Settings > Configuration
// Add these DLLs to the "Assembly Version Validation" section

/// <summary>
/// GameDataManagerImpl handles all game data persistence.
/// This class is not a MonoBehaviour and works independently of the Unity scene.
/// </summary>
public class GameDataManagerImpl
{
    private static GameDataManagerImpl _instance;
    public static GameDataManagerImpl Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new GameDataManagerImpl();
            }
            return _instance;
        }
    }

    private IDbConnection _connection;
    private string _dbPath;
    private bool _initialized = false;

    // Constructor is private to enforce singleton pattern
    private GameDataManagerImpl()
    {
        Initialize();
    }

    /// <summary>
    /// Initialize the database connection
    /// </summary>
    public void Initialize()
    {
        if (_initialized)
            return;

        try
        {
            // Set database path
            _dbPath = Path.Combine(Application.persistentDataPath, "GameData.db");
            Debug.Log($"Database path: {_dbPath}");

            // Create connection string
            string connectionString = $"URI=file:{_dbPath}";
            
            // We'll use reflection to create the connection to avoid direct references
            // This allows the code to compile even without the Mono.Data.Sqlite reference
            Type sqliteConnectionType = Type.GetType("Mono.Data.Sqlite.SqliteConnection, Mono.Data.Sqlite");
            
            if (sqliteConnectionType != null)
            {
                _connection = (IDbConnection)Activator.CreateInstance(sqliteConnectionType, connectionString);
                _connection.Open();

                // Create tables if they don't exist
                CreateTables();

                _initialized = true;
                Debug.Log("GameDataManagerImpl initialized successfully");
            }
            else
            {
                Debug.LogError("Failed to find Mono.Data.Sqlite.SqliteConnection type. Make sure you have the proper references.");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to initialize GameDataManagerImpl: {ex.Message}");
        }
    }

    /// <summary>
    /// Create database tables if they don't exist
    /// </summary>
    private void CreateTables()
    {
        // Resources table
        ExecuteNonQuery(@"
            CREATE TABLE IF NOT EXISTS Resources (
                Id INTEGER PRIMARY KEY,
                Gold INTEGER NOT NULL DEFAULT 1000,
                Food INTEGER NOT NULL DEFAULT 100000,
                Wood INTEGER NOT NULL DEFAULT 100000,
                Metal INTEGER NOT NULL DEFAULT 100000,
                TotalBattlePower INTEGER NOT NULL DEFAULT 0,
                TotalWelfare INTEGER NOT NULL DEFAULT 0
            )
        ");

        // Heroes table
        ExecuteNonQuery(@"
            CREATE TABLE IF NOT EXISTS Heroes (
                HeroName TEXT PRIMARY KEY,
                Rank INTEGER NOT NULL DEFAULT 1,
                ExpLevel INTEGER NOT NULL DEFAULT 1,
                CurrentExp INTEGER NOT NULL DEFAULT 0,
                CurrentPts INTEGER NOT NULL DEFAULT 0,
                IsLocked INTEGER NOT NULL DEFAULT 1
            )
        ");

        // Hero Skills table
        ExecuteNonQuery(@"
            CREATE TABLE IF NOT EXISTS HeroSkills (
                HeroName TEXT NOT NULL,
                SkillName TEXT NOT NULL,
                Level INTEGER NOT NULL DEFAULT 1,
                AttackBonus REAL NOT NULL DEFAULT 0,
                DamageBonus REAL NOT NULL DEFAULT 0,
                DefenseBonus REAL NOT NULL DEFAULT 0,
                HealthBonus REAL NOT NULL DEFAULT 0,
                MarchCapacityBonus REAL NOT NULL DEFAULT 0,
                RallyCapacityBonus REAL NOT NULL DEFAULT 0,
                PRIMARY KEY (HeroName, SkillName),
                FOREIGN KEY (HeroName) REFERENCES Heroes(HeroName) ON DELETE CASCADE
            )
        ");

        // Insert default resource record if it doesn't exist
        int resourceCount = ExecuteScalar<int>("SELECT COUNT(*) FROM Resources");
        if (resourceCount == 0)
        {
            ExecuteNonQuery("INSERT INTO Resources (Id, Gold, Food, Wood, Metal) VALUES (1, 1000, 100000, 100000, 100000)");
        }
    }

    /// <summary>
    /// Close the database connection
    /// </summary>
    public void Close()
    {
        if (_connection != null && _connection.State != ConnectionState.Closed)
        {
            _connection.Close();
            _initialized = false;
        }
    }

    #region Helper Methods

    /// <summary>
    /// Execute a non-query SQL command
    /// </summary>
    private int ExecuteNonQuery(string sql, params object[] args)
    {
        using (IDbCommand cmd = _connection.CreateCommand())
        {
            cmd.CommandText = sql;
            for (int i = 0; i < args.Length; i++)
            {
                IDbDataParameter param = cmd.CreateParameter();
                param.ParameterName = $"@p{i}";
                param.Value = args[i];
                cmd.Parameters.Add(param);
            }
            return cmd.ExecuteNonQuery();
        }
    }

    /// <summary>
    /// Execute a scalar SQL command
    /// </summary>
    private T ExecuteScalar<T>(string sql, params object[] args)
    {
        using (IDbCommand cmd = _connection.CreateCommand())
        {
            cmd.CommandText = sql;
            for (int i = 0; i < args.Length; i++)
            {
                IDbDataParameter param = cmd.CreateParameter();
                param.ParameterName = $"@p{i}";
                param.Value = args[i];
                cmd.Parameters.Add(param);
            }
            object result = cmd.ExecuteScalar();
            if (result == null || result == DBNull.Value)
            {
                return default(T);
            }
            return (T)Convert.ChangeType(result, typeof(T));
        }
    }

    /// <summary>
    /// Execute a query SQL command and return a list of objects
    /// </summary>
    private List<T> ExecuteQuery<T>(string sql, Func<IDataReader, T> mapper, params object[] args) where T : new()
    {
        List<T> result = new List<T>();
        using (IDbCommand cmd = _connection.CreateCommand())
        {
            cmd.CommandText = sql;
            for (int i = 0; i < args.Length; i++)
            {
                IDbDataParameter param = cmd.CreateParameter();
                param.ParameterName = $"@p{i}";
                param.Value = args[i];
                cmd.Parameters.Add(param);
            }
            using (IDataReader reader = cmd.ExecuteReader())
            {
                while (reader.Read())
                {
                    result.Add(mapper(reader));
                }
            }
        }
        return result;
    }

    /// <summary>
    /// Run multiple commands in a transaction
    /// </summary>
    private void RunInTransaction(Action action)
    {
        IDbTransaction transaction = _connection.BeginTransaction();
        try
        {
            action();
            transaction.Commit();
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            Debug.LogError($"Transaction failed: {ex.Message}");
            throw;
        }
    }

    #endregion

    #region Resource Management

    /// <summary>
    /// Get current resource values
    /// </summary>
    public void GetResources(out int gold, out int food, out int wood, out int metal, out int battlePower, out int welfare)
    {
        string sql = "SELECT Gold, Food, Wood, Metal, TotalBattlePower, TotalWelfare FROM Resources WHERE Id = 1";
        using (IDbCommand cmd = _connection.CreateCommand())
        {
            cmd.CommandText = sql;
            using (IDataReader reader = cmd.ExecuteReader())
            {
                if (reader.Read())
                {
                    gold = Convert.ToInt32(reader[0]);
                    food = Convert.ToInt32(reader[1]);
                    wood = Convert.ToInt32(reader[2]);
                    metal = Convert.ToInt32(reader[3]);
                    battlePower = Convert.ToInt32(reader[4]);
                    welfare = Convert.ToInt32(reader[5]);
                }
                else
                {
                    gold = 1000;
                    food = 100000;
                    wood = 100000;
                    metal = 100000;
                    battlePower = 0;
                    welfare = 0;
                }
            }
        }
    }

    /// <summary>
    /// Update resource values
    /// </summary>
    public void UpdateResources(int gold, int food, int wood, int metal, int battlePower, int welfare)
    {
        string sql = "UPDATE Resources SET Gold = @p0, Food = @p1, Wood = @p2, Metal = @p3, TotalBattlePower = @p4, TotalWelfare = @p5 WHERE Id = 1";
        ExecuteNonQuery(sql, gold, food, wood, metal, battlePower, welfare);
    }

    #endregion
}
