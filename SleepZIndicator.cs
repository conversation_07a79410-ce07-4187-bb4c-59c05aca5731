using UnityEngine;

public class SleepZIndicator : MonoBehaviour
{
    public GameObject zPrefab;
    public float spawnInterval = 0.6f;
    public Vector3 spawnOffset = Vector3.up * 0.5f;

    private float timer;

    void Update()
    {
        timer += Time.deltaTime;
        if (timer >= spawnInterval)
        {
            timer = 0f;
            SpawnZ();
        }
    }

    void SpawnZ()
    {
        if (zPrefab == null) return;

        // Spawn a new Z sprite as a child of this object, using the offset
        GameObject zInstance = Instantiate(zPrefab, transform.position + spawnOffset, Quaternion.identity);

        // Set the parent to keep them aligned and move together
        zInstance.transform.SetParent(transform);
    }
}
