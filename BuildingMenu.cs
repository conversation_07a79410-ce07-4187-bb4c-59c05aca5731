using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

public class BuildingMenu : MonoBehaviour
{
    [<PERSON><PERSON>("Menu Settings")]
    public GameObject menuObject; // Reference to the child menu object
    // Removed menuHeightOffset as we don't want to modify menu position
    private BuildingSelection buildingSelection;
    private BuildingUpgrade buildingUpgrade;
    private BuildingCapabilities buildingCapabilities;

    [Header("Building Info")]
    public string buildingName;
    public Sprite buildingImage;
    [TextArea(3, 5)]
    public string buildingDescription;

    [Header("Menu Buttons")]
    public bool hasInfoButton = true;
    public bool hasUpgradeButton = true;
    public bool hasMoveButton = true;
    public bool hasFunctionButton = false;
    public string functionButtonText = "Function";
    public string functionName = "";

    private void Awake()
    {
        buildingSelection = GetComponent<BuildingSelection>();
        buildingUpgrade = GetComponent<BuildingUpgrade>();
        buildingCapabilities = GetComponent<BuildingCapabilities>();

        // Debug log to check if BuildingCapabilities component is found
        if (buildingCapabilities == null)
        {
            Debug.LogWarning($"BuildingCapabilities component not found on {gameObject.name}. Adding one.");
            // Add the component if it's missing
            buildingCapabilities = gameObject.AddComponent<BuildingCapabilities>();
        }
        else
        {
            Debug.Log($"BuildingCapabilities component found on {gameObject.name}.");
        }

        // Ensure the component is properly initialized
        if (buildingCapabilities != null)
        {
            Debug.Log($"BuildingCapabilities component is ready on {gameObject.name}");
        }

        // Find the menu object if not assigned
        if (menuObject == null)
        {
            menuObject = transform.Find("BuildingMenu")?.gameObject;

            // Log a warning if no menu object is found, but don't create one
            // as all prefabs should already have a menu child object
            if (menuObject == null)
            {
                Debug.LogWarning($"No BuildingMenu child object found for {gameObject.name}. Check your prefab setup.");
                return;
            }
        }

        // Set up the menu
        if (menuObject != null)
        {
            // Hide the menu initially
            menuObject.SetActive(false);
        }
    }

    private void Start()
    {
        // Set up the buttons in Start instead of Awake
        // This gives other managers like UpgradeManager time to initialize
        if (menuObject != null)
        {
            SetupButtons();
        }
    }

    private void CreateMenuStructure()
    {
        Debug.Log($"Creating menu structure for {gameObject.name}");

        // Create the main menu GameObject
        menuObject = new GameObject("BuildingMenu");
        menuObject.transform.SetParent(transform);
        // Removed positioning code to maintain manual positioning
        menuObject.transform.localRotation = Quaternion.identity;

        // Add Canvas component
        Canvas canvas = menuObject.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.WorldSpace;

        // Set canvas size
        RectTransform canvasRect = menuObject.GetComponent<RectTransform>();
        canvasRect.sizeDelta = new Vector2(2f, 1f);
        canvasRect.localScale = new Vector3(0.01f, 0.01f, 0.01f);

        // Add Canvas Scaler
        CanvasScaler scaler = menuObject.AddComponent<CanvasScaler>();
        scaler.dynamicPixelsPerUnit = 100;

        // Add Graphic Raycaster
        menuObject.AddComponent<GraphicRaycaster>();

        // Create Buttons container
        GameObject buttonsContainer = new GameObject("Buttons");
        buttonsContainer.transform.SetParent(menuObject.transform, false);

        // Add Horizontal Layout Group
        HorizontalLayoutGroup layout = buttonsContainer.AddComponent<HorizontalLayoutGroup>();
        layout.spacing = 10;
        layout.childAlignment = TextAnchor.MiddleCenter;
        layout.childForceExpandWidth = false;
        layout.childForceExpandHeight = false;

        // Set RectTransform for buttons container
        RectTransform buttonsRect = buttonsContainer.GetComponent<RectTransform>();
        buttonsRect.anchorMin = new Vector2(0, 0);
        buttonsRect.anchorMax = new Vector2(1, 1);
        buttonsRect.sizeDelta = Vector2.zero;

        // Create buttons
        CreateButton(buttonsContainer, "InfoButton", "Info");
        CreateButton(buttonsContainer, "UpgradeButton", "Upgrade");
        CreateButton(buttonsContainer, "MoveButton", "Move");
        CreateButton(buttonsContainer, "FunctionButton", functionButtonText);
    }

    private GameObject CreateButton(GameObject parent, string buttonName, string buttonText)
    {
        GameObject buttonObj = new GameObject(buttonName);
        buttonObj.transform.SetParent(parent.transform, false);

        // Add Image component (button background)
        Image buttonImage = buttonObj.AddComponent<Image>();
        buttonImage.color = new Color(0.2f, 0.2f, 0.8f);

        // Add Button component
        Button button = buttonObj.AddComponent<Button>();
        ColorBlock colors = button.colors;
        colors.normalColor = new Color(0.2f, 0.2f, 0.8f);
        colors.highlightedColor = new Color(0.3f, 0.3f, 0.9f);
        colors.pressedColor = new Color(0.1f, 0.1f, 0.7f);
        button.colors = colors;

        // Set RectTransform for button
        RectTransform buttonRect = buttonObj.GetComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(40, 40);

        // Create Text child
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(buttonObj.transform, false);

        // Add Text component
        TextMeshProUGUI text = textObj.AddComponent<TextMeshProUGUI>();
        text.text = buttonText;
        text.color = Color.white;
        text.fontSize = 12;
        text.alignment = TextAlignmentOptions.Center;

        // Set RectTransform for text
        RectTransform textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.sizeDelta = Vector2.zero;

        return buttonObj;
    }

    public void ShowMenu()
    {
        if (menuObject != null)
        {
            menuObject.SetActive(true);
            // Removed PositionMenu call to maintain manual positioning
        }
        else
        {
            Debug.LogError($"No menu object assigned for {gameObject.name}");
        }
    }

    // PositionMenu method removed to maintain manual positioning

    private void SetupButtons()
    {
        if (menuObject == null)
        {
            Debug.LogError($"Menu object is null for {gameObject.name}. Cannot set up buttons.");
            return;
        }

        // Find buttons in the menu
        Transform buttonsContainer = menuObject.transform.Find("Buttons");
        if (buttonsContainer == null)
        {
            // Try to find buttons directly in the menu object
            buttonsContainer = menuObject.transform;
            Debug.Log($"No 'Buttons' container found in {gameObject.name}'s menu. Looking for buttons directly in the menu object.");
        }

        // Set up Info button
        Transform infoButtonTransform = buttonsContainer.Find("InfoButton");
        if (infoButtonTransform != null)
        {
            Debug.Log($"Found InfoButton for {gameObject.name}");
            Button infoButton = infoButtonTransform.GetComponent<Button>();
            if (infoButton == null)
            {
                Debug.LogError($"InfoButton found but has no Button component on {gameObject.name}");
                infoButton = infoButtonTransform.gameObject.AddComponent<Button>();
            }

            infoButtonTransform.gameObject.SetActive(hasInfoButton);
            if (hasInfoButton && infoButton != null)
            {
                Debug.Log($"Setting up InfoButton click event for {gameObject.name}");
                infoButton.onClick.RemoveAllListeners();
                infoButton.onClick.AddListener(ShowBuildingInfo);
            }
        }
        else
        {
            Debug.LogWarning($"InfoButton not found in {gameObject.name}'s menu. Check your prefab setup.");
        }

        // Set up Upgrade button
        Transform upgradeButtonTransform = buttonsContainer.Find("UpgradeButton");
        if (upgradeButtonTransform != null)
        {
            Debug.Log($"Found UpgradeButton for {gameObject.name}");
            Button upgradeButton = upgradeButtonTransform.GetComponent<Button>();
            if (upgradeButton == null)
            {
                Debug.LogError($"UpgradeButton found but has no Button component on {gameObject.name}");
                upgradeButton = upgradeButtonTransform.gameObject.AddComponent<Button>();
            }

            upgradeButtonTransform.gameObject.SetActive(hasUpgradeButton);
            if (hasUpgradeButton && upgradeButton != null)
            {
                Debug.Log($"Setting up UpgradeButton click event for {gameObject.name}");
                upgradeButton.onClick.RemoveAllListeners();

                // Add a safe click handler that will check for UpgradeManager at runtime
                upgradeButton.onClick.AddListener(() => {
                    Debug.Log($"Upgrade button clicked for {gameObject.name}");

                    // Make sure this building is selected
                    if (BuildingSelection.CurrentlySelected == null ||
                        BuildingSelection.CurrentlySelected.gameObject != gameObject)
                    {
                        Debug.LogWarning($"Building {gameObject.name} is not currently selected. Selecting it now.");
                        BuildingSelection buildingSelection = GetComponent<BuildingSelection>();
                        if (buildingSelection != null)
                        {
                            buildingSelection.SelectBuilding();
                        }
                    }

                    // Check for UpgradeManager at runtime
                    if (UpgradeManager.Instance != null)
                    {
                        // Now start the upgrade
                        Debug.Log($"Starting upgrade for {gameObject.name} via UpgradeManager");
                        UpgradeManager.Instance.StartUpgrade();
                    }
                    else
                    {
                        Debug.LogWarning("UpgradeManager.Instance is null when trying to upgrade. The manager might not be initialized yet.");

                        // Try to find the UpgradeManager in the scene
                        UpgradeManager upgradeManager = FindFirstObjectByType<UpgradeManager>();
                        if (upgradeManager != null)
                        {
                            Debug.Log("Found UpgradeManager in scene. Attempting to use it directly.");
                            upgradeManager.StartUpgrade();
                        }
                        else
                        {
                            Debug.LogError("No UpgradeManager found in the scene. Make sure it exists in your scene.");
                        }
                    }
                });

                // Disable upgrade button if building is at max level
                if (buildingUpgrade != null && buildingUpgrade.CurrentLevel >= buildingUpgrade.maxLevel)
                {
                    upgradeButton.interactable = false;
                }
            }
        }
        else
        {
            Debug.LogWarning($"UpgradeButton not found in {gameObject.name}'s menu. Check your prefab setup.");
        }

        // Set up Move button
        Transform moveButtonTransform = buttonsContainer.Find("MoveButton");
        if (moveButtonTransform != null)
        {
            Debug.Log($"Found MoveButton for {gameObject.name}");
            Button moveButton = moveButtonTransform.GetComponent<Button>();
            if (moveButton == null)
            {
                Debug.LogError($"MoveButton found but has no Button component on {gameObject.name}");
                moveButton = moveButtonTransform.gameObject.AddComponent<Button>();
            }

            moveButtonTransform.gameObject.SetActive(hasMoveButton);
            if (hasMoveButton && moveButton != null)
            {
                Debug.Log($"Setting up MoveButton click event for {gameObject.name}");
                moveButton.onClick.RemoveAllListeners();
                moveButton.onClick.AddListener(EnableDragging);
            }
        }
        else
        {
            Debug.LogWarning($"MoveButton not found in {gameObject.name}'s menu. Check your prefab setup.");
        }

        // Set up Function button
        Transform functionButtonTransform = buttonsContainer.Find("FunctionButton");
        if (functionButtonTransform != null)
        {
            Debug.Log($"Found FunctionButton for {gameObject.name}");
            Button functionButton = functionButtonTransform.GetComponent<Button>();
            if (functionButton == null)
            {
                Debug.LogError($"FunctionButton found but has no Button component on {gameObject.name}");
                functionButton = functionButtonTransform.gameObject.AddComponent<Button>();
            }

            TextMeshProUGUI functionButtonTextComponent = functionButtonTransform.GetComponentInChildren<TextMeshProUGUI>();
            if (functionButtonTextComponent == null)
            {
                Debug.LogWarning($"FunctionButton has no TextMeshProUGUI component on {gameObject.name}");
                // Try to find a regular Text component
                Text regularText = functionButtonTransform.GetComponentInChildren<Text>();
                if (regularText != null)
                {
                    regularText.text = functionButtonText;
                }
            }

            functionButtonTransform.gameObject.SetActive(hasFunctionButton);

            if (hasFunctionButton && functionButton != null)
            {
                Debug.Log($"Setting up FunctionButton click event for {gameObject.name} with function: {functionName}");

                // Set button text
                if (functionButtonTextComponent != null)
                {
                    functionButtonTextComponent.text = functionButtonText;
                }

                // Set button action
                functionButton.onClick.RemoveAllListeners();
                functionButton.onClick.AddListener(ExecuteFunction);
            }
        }
        else
        {
            Debug.LogWarning($"FunctionButton not found in {gameObject.name}'s menu. Check your prefab setup.");
        }
    }

    public void HideMenu()
    {
        if (menuObject != null)
        {
            menuObject.SetActive(false);
        }
    }

    // Reference to the BuildingInfoUI
    private static BuildingInfoUI buildingInfoUI;

    private void ShowBuildingInfo()
    {
        Debug.Log($"Info button clicked for {gameObject.name}");

        // Find the BuildingInfoUI if not already cached
        if (buildingInfoUI == null)
        {
            buildingInfoUI = FindFirstObjectByType<BuildingInfoUI>();

            if (buildingInfoUI == null)
            {
                Debug.LogError("BuildingInfoUI not found in scene. Make sure you have a GameObject with the BuildingInfoUI component in your scene.");
                return;
            }
        }

        // Show building info
        Debug.Log($"Showing building info for {gameObject.name}");

        // Display the info
        buildingInfoUI.ShowBuildingInfo(this);

        // Hide the menu
        HideMenu();
    }

    private void EnableDragging()
    {
        Debug.Log($"Move button clicked for {gameObject.name}");

        if (buildingSelection != null)
        {
            Debug.Log($"Enabling dragging for {gameObject.name}");
            buildingSelection.EnableDragging();
        }
        else
        {
            Debug.LogError($"BuildingSelection component not found on {gameObject.name}");
        }

        // Hide the menu
        HideMenu();
    }

    private void ExecuteFunction()
    {
        Debug.Log($"Function button clicked for {gameObject.name} with function: {functionName}");

        // Execute the function based on the function name
        switch (functionName.ToLower())
        {
            case "training":
                // Open Training UI
                if (TrainingUI.Instance != null)
                {
                    Debug.Log($"Opening Training UI for {gameObject.name}");
                    TrainingUI.Instance.Open();
                }
                else
                {
                    Debug.LogError("TrainingUI.Instance is null. Make sure the TrainingUI is in the scene.");
                }
                break;

            case "research":
                Debug.Log($"Opening Research UI for {gameObject.name}");
                // Add code to open research UI
                break;

            default:
                Debug.LogWarning($"Function {functionName} not implemented");
                break;
        }

        // Hide the menu
        HideMenu();
    }

    // Get building capabilities for display in BuildingInfoUI
    public List<BuildingCapabilityType> GetCapabilities()
    {
        Debug.Log($"GetCapabilities called for {gameObject.name}");

        // Register this building with GameManager if it's not already registered
        // This ensures the building has capabilities in the GameManager
        GameManager.Instance.RegisterBuilding(gameObject);

        // First check if this building has a BuildingCapabilities component
        BuildingCapabilities buildingCap = GetComponent<BuildingCapabilities>();
        if (buildingCap != null)
        {
            // Get capabilities directly from the BuildingCapabilities component
            List<BuildingCapabilityType> directCapabilities = new List<BuildingCapabilityType>();

            foreach (var capability in buildingCap.capabilities)
            {
                directCapabilities.Add(capability.capabilityType);
                Debug.Log($"Found capability {capability.capabilityType} with value {capability.baseValue} directly on {gameObject.name}");
            }

            // If energy production is set in the Inspector but not in capabilities, add it
            if (buildingCap.energyProduction > 0 && !directCapabilities.Contains(BuildingCapabilityType.ResourceProduction_Energy))
            {
                directCapabilities.Add(BuildingCapabilityType.ResourceProduction_Energy);
                Debug.Log($"Added ResourceProduction_Energy capability from energyProduction field: {buildingCap.energyProduction}");
            }

            if (directCapabilities.Count > 0)
            {
                Debug.Log($"Returning {directCapabilities.Count} capabilities directly from BuildingCapabilities on {gameObject.name}");
                return directCapabilities;
            }
        }

        // Fall back to GameManager if no direct capabilities found
        List<BuildingCapabilityType> result = GameManager.Instance.GetBuildingCapabilities(gameObject);
        Debug.Log($"Returning {result.Count} capabilities for {gameObject.name} from GameManager");
        return result;
    }

    // Get capability value from BuildingCapabilities or GameManager
    public float GetCapabilityValue(BuildingCapabilityType capabilityType)
    {
        // Register this building with GameManager if it's not already registered
        GameManager.Instance.RegisterBuilding(gameObject);

        // First check if this building has a BuildingCapabilities component
        BuildingCapabilities buildingCap = GetComponent<BuildingCapabilities>();
        if (buildingCap != null)
        {
            // Special case for energy production
            if (capabilityType == BuildingCapabilityType.ResourceProduction_Energy && buildingCap.energyProduction > 0)
            {
                Debug.Log($"Getting energy production value directly from energyProduction field: {buildingCap.energyProduction}");
                return buildingCap.energyProduction;
            }

            // Check if the capability exists in the BuildingCapabilities component
            foreach (var capability in buildingCap.capabilities)
            {
                if (capability.capabilityType == capabilityType)
                {
                    Debug.Log($"Getting capability {capabilityType} value directly from BuildingCapabilities: {capability.baseValue}");
                    return capability.baseValue;
                }
            }
        }

        // Fall back to GameManager if not found in BuildingCapabilities
        float value = GameManager.Instance.GetBuildingCapabilityValue(gameObject, capabilityType);
        Debug.Log($"Getting capability {capabilityType} value from GameManager: {value}");
        return value;
    }
}
