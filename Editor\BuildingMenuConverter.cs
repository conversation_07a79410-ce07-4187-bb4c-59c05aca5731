using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using UnityEngine.UI;

public class BuildingMenuConverter : EditorWindow
{
    private GameObject menuPrefab;
    private GameObject capabilityPrefab;
    private bool convertAllBuildings = true;
    private GameObject specificBuilding;
    private Vector2 scrollPosition;
    private List<GameObject> buildingsToConvert = new List<GameObject>();

    [MenuItem("Tools/Building System/Building Menu Converter")]
    public static void ShowWindow()
    {
        GetWindow<BuildingMenuConverter>("Building Menu Converter");
    }

    private void OnGUI()
    {
        GUILayout.Label("Building Menu Converter", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        EditorGUILayout.HelpBox("This tool helps convert existing buildings to use the new BuildingMenu system.", MessageType.Info);
        EditorGUILayout.Space();

        // Prefab fields
        menuPrefab = (GameObject)EditorGUILayout.ObjectField("Menu Prefab (Child Menu)", menuPrefab, typeof(GameObject), false);
        capabilityPrefab = (GameObject)EditorGUILayout.ObjectField("Capability Prefab", capabilityPrefab, typeof(GameObject), false);
        EditorGUILayout.Space();

        // Conversion options
        convertAllBuildings = EditorGUILayout.Toggle("Convert All Buildings", convertAllBuildings);

        if (!convertAllBuildings)
        {
            specificBuilding = (GameObject)EditorGUILayout.ObjectField("Specific Building", specificBuilding, typeof(GameObject), true);
        }
        else
        {
            // Show list of buildings that will be converted
            EditorGUILayout.LabelField("Buildings to Convert:");

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(200));

            buildingsToConvert.Clear();
            foreach (BuildingUpgrade building in FindObjectsByType<BuildingUpgrade>(FindObjectsSortMode.None))
            {
                buildingsToConvert.Add(building.gameObject);
                EditorGUILayout.LabelField(building.gameObject.name);
            }

            EditorGUILayout.EndScrollView();
        }

        EditorGUILayout.Space();

        // Convert button
        GUI.enabled = menuPrefab != null && (convertAllBuildings || specificBuilding != null);
        if (GUILayout.Button("Convert Buildings"))
        {
            ConvertBuildings();
        }
        GUI.enabled = true;
    }

    private void ConvertBuildings()
    {
        if (convertAllBuildings)
        {
            foreach (BuildingUpgrade building in FindObjectsByType<BuildingUpgrade>(FindObjectsSortMode.None))
            {
                ConvertBuilding(building.gameObject);
            }
            Debug.Log($"Converted {buildingsToConvert.Count} buildings to use the new BuildingMenu system.");
        }
        else if (specificBuilding != null)
        {
            ConvertBuilding(specificBuilding);
            Debug.Log($"Converted {specificBuilding.name} to use the new BuildingMenu system.");
        }
    }

    private void ConvertBuilding(GameObject building)
    {
        // Skip if already has BuildingMenu component
        if (building.GetComponent<BuildingMenu>() != null)
        {
            Debug.Log($"{building.name} already has a BuildingMenu component. Skipping.");
            return;
        }

        // Check if building already has a menu child
        Transform existingMenu = building.transform.Find("BuildingMenu");
        GameObject menuObject = null;

        if (existingMenu == null)
        {
            if (menuPrefab != null)
            {
                // Create a new menu as a child of the building using the prefab
                menuObject = Instantiate(menuPrefab, building.transform);
                menuObject.name = "BuildingMenu";
                menuObject.transform.localPosition = new Vector3(0, 1.5f, 0);
                menuObject.transform.localRotation = Quaternion.identity;
                menuObject.SetActive(false);

                // Make sure it has a Buttons container
                if (menuObject.transform.Find("Buttons") == null)
                {
                    GameObject buttonsContainer = new GameObject("Buttons");
                    buttonsContainer.transform.SetParent(menuObject.transform, false);

                    // Add layout component
                    HorizontalLayoutGroup layout = buttonsContainer.AddComponent<HorizontalLayoutGroup>();
                    layout.spacing = 10;
                    layout.childAlignment = TextAnchor.MiddleCenter;

                    // Set RectTransform
                    RectTransform buttonsRect = buttonsContainer.GetComponent<RectTransform>();
                    buttonsRect.anchorMin = new Vector2(0, 0);
                    buttonsRect.anchorMax = new Vector2(1, 1);
                    buttonsRect.sizeDelta = Vector2.zero;
                }
            }
            else
            {
                // Let the BuildingMenu component create the menu structure
                Debug.Log("No menu prefab assigned. The BuildingMenu component will create a default menu structure.");
            }
        }
        else
        {
            menuObject = existingMenu.gameObject;
        }

        // Add BuildingMenu component
        BuildingMenu buildingMenu = building.AddComponent<BuildingMenu>();

        // Set menu object reference
        buildingMenu.menuObject = menuObject;

        // Set building info
        buildingMenu.buildingName = building.name;

        // Try to get description from any existing components
        BuildingUpgrade buildingUpgrade = building.GetComponent<BuildingUpgrade>();
        if (buildingUpgrade != null)
        {
            buildingMenu.buildingDescription = $"{building.name} - Level {buildingUpgrade.CurrentLevel}";

            // Set button visibility based on building type
            buildingMenu.hasUpgradeButton = true;
            buildingMenu.hasMoveButton = true;
            buildingMenu.hasInfoButton = true;

            // Set function button based on building type
            switch (buildingUpgrade.buildingType)
            {
                case BuildingUpgrade.BuildingType.Farm:
                    buildingMenu.hasFunctionButton = false;
                    break;
                case BuildingUpgrade.BuildingType.LumberMill:
                    buildingMenu.hasFunctionButton = false;
                    break;
                case BuildingUpgrade.BuildingType.Quarry: // Metal production
                    buildingMenu.hasFunctionButton = false;
                    break;
                case BuildingUpgrade.BuildingType.NonGenerating:
                    // Check for specific building types by name
                    if (building.name.Contains("Barracks"))
                    {
                        buildingMenu.hasFunctionButton = true;
                        buildingMenu.functionButtonText = "Train";
                        buildingMenu.functionName = "training";
                    }
                    else if (building.name.Contains("Research"))
                    {
                        buildingMenu.hasFunctionButton = true;
                        buildingMenu.functionButtonText = "Research";
                        buildingMenu.functionName = "research";
                    }
                    break;
            }
        }

        // Mark the object as dirty so Unity saves the changes
        EditorUtility.SetDirty(building);
    }
}
