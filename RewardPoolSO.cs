using UnityEngine;
using System.Collections.Generic;


[CreateAssetMenu(fileName = "RewardPool", menuName = "Game/Reward Pool")]
public class RewardPoolSO : ScriptableObject
{
    [System.Serializable]
    public class RewardEntry
    {
        public string rewardId;
        public InventoryItemSO itemData;
        public float chance;
        public bool isAddressable;
        public string addressablePath;
        public RewardCondition[] conditions;
    }

    [System.Serializable]
    public class RewardCondition
    {
        public ConditionType type;
        public int requiredValue;
        public System.DateTime availableFrom;
        public System.DateTime availableUntil;
    }

    public enum ConditionType
    {
        HQLevel,
        PlayerLevel,
        TimeGated,
        EventBased
    }

    public List<RewardEntry> rewards;
}