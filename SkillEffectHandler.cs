using UnityEngine;
using System.Collections.Generic;

public class SkillEffectHandler 
{
    private Dictionary<SkillEffectType, System.Action<SkillEffect, GameObject>> effectHandlers;

    public SkillEffectHandler()
    {
        InitializeEffectHandlers();
    }

    private void InitializeEffectHandlers()
    {
        effectHandlers = new Dictionary<SkillEffectType, System.Action<SkillEffect, GameObject>>
        {
            { SkillEffectType.AdditionalDamage, HandleAdditionalDamage },
            { SkillEffectType.DamageOverTime, HandleDamageOverTime },
            { SkillEffectType.Stun, HandleStun }
        };
    }

    private void HandleAdditionalDamage(SkillEffect effect, GameObject target)
    {
        // Will be implemented when combat system is ready
        Debug.Log($"Handling additional damage effect: {effect.effectValue} damage from turn {effect.startTurn} for {effect.turns} turns with {effect.effectChance}% chance");
    }

    private void HandleDamageOverTime(SkillEffect effect, GameObject target)
    {
        // Will be implemented when combat system is ready
        Debug.Log($"Handling DOT effect: {effect.effectValue} damage from turn {effect.startTurn} for {effect.turns} turns");
    }

    private void HandleStun(SkillEffect effect, GameObject target)
    {
        // Will be implemented when combat system is ready
        Debug.Log($"Handling stun effect from turn {effect.startTurn} for {effect.turns} turns");
    }

    public void ExecuteEffects(SkillData skill, GameObject target, int currentTurn)
    {
        foreach (var effect in skill.skillEffects)
        {
            if (currentTurn >= effect.startTurn && currentTurn < effect.startTurn + effect.turns)
            {
                if (effectHandlers.ContainsKey(effect.effectType))
                {
                    effectHandlers[effect.effectType].Invoke(effect, target);
                }
            }
        }
    }
}
