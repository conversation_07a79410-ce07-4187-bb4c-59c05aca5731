using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(TroopSO))]
public class TroopSOEditor : Editor
{
    public override void OnInspectorGUI()
    {
        TroopSO troopSO = (TroopSO)target;

        // Draw default inspector
        DrawDefaultInspector();

        // Add a button to calculate all stats
        EditorGUILayout.Space();
        if (GUILayout.Button("Calculate All Stats"))
        {
            troopSO.CalculateAll();
            EditorUtility.SetDirty(troopSO);
        }

        // Display calculated values
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Calculated Values", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("Required Building Level", troopSO.RequiredBuildingLevel.ToString());
        
        // Combat Stats
        EditorGUILayout.LabelField("Combat Stats", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("Attack", troopSO.Attack.ToString());
        EditorGUILayout.LabelField("Defense", troopSO.Defense.ToString());
        EditorGUILayout.LabelField("Health", troopSO.Health.ToString());
        
        // Training Requirements
        EditorGUILayout.LabelField("Training Requirements", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("Food Cost", troopSO.FoodCost.ToString());
        EditorGUILayout.LabelField("Wood Cost", troopSO.WoodCost.ToString());
        EditorGUILayout.LabelField("Metal Cost", troopSO.MetalCost.ToString());
        EditorGUILayout.LabelField("Training Time", troopSO.TrainingTime.ToString() + " seconds");
        EditorGUILayout.LabelField("Battle Power", troopSO.BattlePower.ToString());
    }
}
