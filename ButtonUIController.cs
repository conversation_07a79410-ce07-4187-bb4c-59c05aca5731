using UnityEngine;
using UnityEngine.UI;

public class ButtonUIController : MonoBeh<PERSON>our
{
    [Header("UI Targets")]
    [SerializeField] private GameObject uiToOpen;
    [SerializeField] private GameObject uiToClose;

    private void Start()
    {
        Button button = GetComponent<Button>();
        if (button != null)
        {
            button.onClick.AddListener(OnButtonClick);
        }
    }

    private void OnButtonClick()
    {
        if (uiToOpen != null)
        {
            uiToOpen.SetActive(true);
        }

        if (uiToClose != null)
        {
            uiToClose.SetActive(false);
        }
    }

    // Optional: Public methods to set targets via code
    public void SetOpenTarget(GameObject target)
    {
        uiToOpen = target;
    }

    public void SetCloseTarget(GameObject target)
    {
        uiToClose = target;
    }
}