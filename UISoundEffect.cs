using UnityEngine;
using UnityEngine.UI;

public class UISoundEffect : MonoBehaviour
{
    [System.Serializable]
    public class UISoundSettings
    {
        public GameObject targetUI;
        [Header("Sound Effects")]
        public bool enableOpenSound = true;
        public AudioClip openSound;
        [Range(0f, 1f)]
        public float openVolume = 1f;

        public bool enableCloseSound = true;
        public AudioClip closeSound;
        [Range(0f, 1f)]
        public float closeVolume = 1f;
    }

    [SerializeField] private UISoundSettings[] uiElements;
    private AudioSource audioSource;

    private void Awake()
    {
        audioSource = gameObject.AddComponent<AudioSource>();
        audioSource.playOnAwake = false;
        audioSource.spatialBlend = 0f; // 2D sound

        // Set up listeners for each UI element
        foreach (var ui in uiElements)
        {
            if (ui.targetUI != null)
            {
                // Store the original active state
                bool wasActive = ui.targetUI.activeSelf;
                
                // Add a listener to track active state changes
                ui.targetUI.gameObject.SetActive(wasActive);
                StartCoroutine(MonitorUIState(ui));
            }
        }
    }

    private System.Collections.IEnumerator MonitorUIState(UISoundSettings ui)
    {
        bool previousState = ui.targetUI.activeSelf;

        while (true)
        {
            if (ui.targetUI.activeSelf != previousState)
            {
                if (ui.targetUI.activeSelf)
                {
                    PlayOpenSound(ui);
                }
                else
                {
                    PlayCloseSound(ui);
                }
                previousState = ui.targetUI.activeSelf;
            }
            yield return new WaitForSeconds(0.1f); // Check every 0.1 seconds
        }
    }

    private void PlayOpenSound(UISoundSettings ui)
    {
        if (ui.enableOpenSound && ui.openSound != null)
        {
            audioSource.volume = ui.openVolume;
            audioSource.PlayOneShot(ui.openSound);
        }
    }

    private void PlayCloseSound(UISoundSettings ui)
    {
        if (ui.enableCloseSound && ui.closeSound != null)
        {
            audioSource.volume = ui.closeVolume;
            audioSource.PlayOneShot(ui.closeSound);
        }
    }

    // Public methods to manually control UI visibility with sounds
    public void ShowUI(GameObject targetUI)
    {
        var uiSettings = System.Array.Find(uiElements, ui => ui.targetUI == targetUI);
        if (uiSettings != null)
        {
            targetUI.SetActive(true);
            // Sound will be played by the monitor coroutine
        }
    }

    public void HideUI(GameObject targetUI)
    {
        var uiSettings = System.Array.Find(uiElements, ui => ui.targetUI == targetUI);
        if (uiSettings != null)
        {
            targetUI.SetActive(false);
            // Sound will be played by the monitor coroutine
        }
    }

    // Optional: Method to add UI elements at runtime
    public void AddUIElement(GameObject targetUI, AudioClip openSound = null, AudioClip closeSound = null)
    {
        var newElement = new UISoundSettings
        {
            targetUI = targetUI,
            openSound = openSound,
            closeSound = closeSound
        };

        var newList = new UISoundSettings[uiElements.Length + 1];
        uiElements.CopyTo(newList, 0);
        newList[uiElements.Length] = newElement;
        uiElements = newList;

        StartCoroutine(MonitorUIState(newElement));
    }
}