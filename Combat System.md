# Combat System

## General Notes
- Attack targets by sending marches composed of heroes, troops, and optionally beasts/drones when unlocked
- Attackable objects have attack/rally buttons that trigger March UI
- All UI elements and prefabs are already created

## March UI Features
### Hero Panel
- Populates available heroes in descending order by Power (descending)
- Select 2 heroes (Leader and Assistant)
- Displays hero images, names, types, and skills with levels
- Skill tooltips on hover
- Hero selection buttons open scrollable list of available heroes
- Switch button to swap Leader and Assistant roles

### Troops Panel
- Displays available troops sorted by level (descending) and then by type (Infantry, Rider, Ranged)
- For each troop level:
  - Thumbnail and name
  - Slider and text field to set quantity (0-max available)
- Key displayed information:
  - Stamina cost
  - Travel time to target
  - Win probability estimate (non-player targets)
  - Total Battle Power
  - Troop count/march capacity
- Formation management:
  - Save/load formation buttons
  - Load UI shows scrollable list of saved formations
  - Rename saved formations

### Conditional Elements
- Beast/Drone UIs appear when respective buildings are unlocked
- "Call Back" button for outgoing marches
- March slots system limits concurrent marches

### UI Controls
- Info toggle button
- Clear troops button
- March launch button

## Combat Logic
### Battle Mechanics
- Stats combine heroes, troops, beasts, drones
- Hero skill usage:
  - Leader: All unlocked skills
  - Assistant: First 2 skills + last skill (if unlocked)
- Battle rounds (max 60)
- Unit positioning:
  - Infantry vs Infantry frontline
  - Ranged units deal initial damage
  - Special skills can target Riders/Ranged

### Post-Battle Effects
- Heroes cannot die
- Troop losses split:
  - 70% light wounds (auto-heal on return)
  - 30% heavy wounds (sent to Hospital)
- Hospital capacity limits
- Beasts/drones "die" in battle but revive after
- March remains at target until recalled
- Win/lose UI triggers
- XP gains for heroes

### Stamina System
- Attack costs stamina
- Low stamina triggers replenish UI
- Stamina recovery:
  - 1 stamina per 30 seconds
  - Bottles from inventory
  - Gold purchase option

## Rally System
### Requirements
- Player must be in an alliance
- Uses modified March UI
- Timing options (1min or 5min wait)

### Rally UI Elements
- Leader and joiner displays
- Total troops/rally capacity
- Countdown timer
- Target information
- Join button for alliance members
- Expanded view shows detailed participant list

### Rally Management
- Leader can disband joiners
- March prefab animation:
  - Moves to target
  - Battle animation
  - Returns to origin

## To-Do Items
1. Implement Stamina system fully
2. Create Hospital system
3. Develop battle report messaging
4. Finalize stamina replenish UI:
   - Progress bars
   - Countdown timers
   - Inventory integration
   - Purchase flow