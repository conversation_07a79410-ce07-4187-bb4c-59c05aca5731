using System.Resources;
using TMPro;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;

public class UpgradeManager : MonoBehaviour
{
    public static UpgradeManager Instance { get; private set; }

    public GameObject upgradeUI; // The upgrade UI menu to show
    public Camera mainCamera;

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Destroy(gameObject);
            return;
        }

        mainCamera = Camera.main;

        Instance = this;

        if (upgradeButtonImage != null)
        {
            originalButtonColor = upgradeButtonImage.color; // Store original color
        }
    }

    [Header("UI Elements")]
    public TextMeshProUGUI foodText;
    public TextMeshProUGUI woodText;
    public TextMeshProUGUI metalText;
    public TextMeshProUGUI goldText;  // Gold is only used for express upgrade
    public TextMeshProUGUI battlePowerText;
    public TextMeshProUGUI welfareText;
    public Button upgradeButtonInMenu; // Upgrade button in building menu, to open upgrade UI
    public Button upgradeConfirmButton; // Confirm button in upgrade UI to perform upgrade
    public Image upgradeButtonImage; // Button image for greyscale effect (in menu)
    private Color originalButtonColor; // Stores the original button color

    // New UI text elements for time and express upgrade gold cost
    public TextMeshProUGUI timeText;
    public TextMeshProUGUI expressUpgradeGoldCostText;

    public static class NumberFormatter
    {
        // Formats large numbers with K, M, B suffixes
        public static string FormatLargeNumber(int number)
        {
            if (number >= 1000000000) // Billions
            {
                return (number / 1000000000f).ToString("0.##") + "B"; // 2.05B
            }
            else if (number >= 1000000) // Millions
            {
                return (number / 1000000f).ToString("0.##") + "M"; // 2.05M
            }
            else if (number >= 1000) // Thousands
            {
                return (number / 1000f).ToString("0.##") + "K"; // 1.3K
            }
            else
            {
                return number.ToString(); // For smaller numbers, just return the number
            }
        }
    }

    public void StartUpgrade()
    {
        // Get currently selected building
        BuildingSelection selectedBuilding = BuildingSelection.CurrentlySelected;
        if (selectedBuilding == null)
        {
            Debug.LogError("No building selected when starting upgrade!");
            return;
        }
        Debug.Log($"Starting upgrade for: {selectedBuilding.name}");

        Transform building = selectedBuilding.transform;
        BuildingUpgrade buildingUpgrade = selectedBuilding.GetComponent<BuildingUpgrade>();
        if (buildingUpgrade == null) return;

        // Hide the building menu using the menu manager
        BuildingMenuManager.Instance.HideCurrentMenu();

        // Update UI with correct upgrade values
        UpdateUpgradeUI(buildingUpgrade);

        // Show the upgrade UI
        if (upgradeUI != null)
        {
            upgradeUI.SetActive(true);
        }

        // Instantly center the camera on the building
        if (mainCamera != null)
        {
            Vector3 targetPosition = building.position + (mainCamera.transform.forward * -13f);
            targetPosition.y = mainCamera.transform.position.y; // Maintain height
            mainCamera.transform.position = targetPosition;


        }
    }

    public void CancelUpgrade()
    {
        // Hide upgrade UI
        if (upgradeUI != null)
        {
            upgradeUI.SetActive(false);
        }
    }

    // This function is bound to the "Confirm" button in the upgrade UI
    public void ConfirmUpgrade()
    {
        Debug.Log("Confirm Upgrade button clicked!");

        if (BuildingSelection.CurrentlySelected == null)
        {
            Debug.LogError("No building is selected! Make sure you click a building before upgrading.");
            return;
        }

        BuildingUpgrade buildingUpgrade = BuildingSelection.CurrentlySelected.GetComponent<BuildingUpgrade>();
        if (buildingUpgrade == null)
        {
            Debug.Log("Building does not have a BuildingUpgrade component!"); // Debugging
            return;
        }

        Debug.Log($"Upgrading {buildingUpgrade.gameObject.name}...");

        // Calculate costs for the next level
        int nextLevel = buildingUpgrade.CurrentLevel + 1;
        int foodCost = Mathf.RoundToInt(buildingUpgrade.baseFoodCost * Mathf.Pow(buildingUpgrade.resourceGrowthFactor, nextLevel - 1));
        int woodCost = Mathf.RoundToInt(buildingUpgrade.baseWoodCost * Mathf.Pow(buildingUpgrade.resourceGrowthFactor, nextLevel - 1));
        int metalCost = Mathf.RoundToInt(buildingUpgrade.baseMetalCost * Mathf.Pow(buildingUpgrade.resourceGrowthFactor, nextLevel - 1));

        // Check if the player has enough resources
        if (!GameManager.Instance.HasEnoughResources(foodCost, woodCost, metalCost))
        {
            Debug.Log("Not enough resources to upgrade!");
            return;
        }

        // Deduct resources
        GameManager.Instance.SpendResources(foodCost, woodCost, metalCost);
        Debug.Log("Resources deducted successfully!");

        float upgradeTime = buildingUpgrade.GetRequiredUpgradeTime(buildingUpgrade.CurrentLevel);

        Debug.Log($"Starting upgrade for {upgradeTime} seconds."); // Debug log to ensure upgrade starts

        // Perform upgrade
        buildingUpgrade.StartUpgrade(upgradeTime);

        // Hide UI after confirming upgrade
        CancelUpgrade();
    }

    public void ConfirmExpressUpgrade()
    {
        if (BuildingSelection.CurrentlySelected == null) return;

        BuildingUpgrade buildingUpgrade = BuildingSelection.CurrentlySelected.GetComponent<BuildingUpgrade>();
        if (buildingUpgrade == null) return;

        Debug.Log($"Express Upgrading {buildingUpgrade.gameObject.name}");

        // Calculate gold cost
        int goldCost = Mathf.RoundToInt(buildingUpgrade.baseGoldCost * Mathf.Pow(buildingUpgrade.goldCostGrowthFactor, buildingUpgrade.CurrentLevel - 1));

        // Check if the player has enough gold
        if (!GameManager.Instance.HasEnoughGold(goldCost))
        {
            Debug.Log("Not enough gold for express upgrade!");
            return;
        }

        // Deduct gold
        GameManager.Instance.SpendGold(goldCost);

        // Perform an express upgrade
        buildingUpgrade.UpgradeBuilding(true);

        // Hide UI after express upgrade
        CancelUpgrade();
    }

    public void UpdateUpgradeUI(BuildingUpgrade building)
    {
        if (building == null) return;

        int nextLevel = building.CurrentLevel + 1;

        Debug.Log($"Updating upgrade UI for {building.name} | Level: {building.CurrentLevel}");

        // Calculate required resources for the next level
        int foodCost = Mathf.RoundToInt(building.baseFoodCost * Mathf.Pow(building.resourceGrowthFactor, nextLevel - 1));
        int woodCost = Mathf.RoundToInt(building.baseWoodCost * Mathf.Pow(building.resourceGrowthFactor, nextLevel - 1));
        int metalCost = Mathf.RoundToInt(building.baseMetalCost * Mathf.Pow(building.resourceGrowthFactor, nextLevel - 1));
        int goldCost = Mathf.RoundToInt(building.baseGoldCost * Mathf.Pow(building.goldCostGrowthFactor, nextLevel - 1));

        // Calculate Battle Power & Welfare Gain
        int battlePowerGain = Mathf.RoundToInt(building.baseBattlePower * Mathf.Pow(building.battlePowerGrowthFactor, nextLevel - 1));
        int welfareGain = Mathf.RoundToInt(building.baseWelfare * Mathf.Pow(building.welfareGrowthFactor, nextLevel - 1));

        bool hasEnoughResources = GameManager.Instance.HasEnoughResources(foodCost, woodCost, metalCost);

        Debug.Log($"Checking resources: Food({foodCost}), Wood({woodCost}), Metal({metalCost}) - Available: Food({GameManager.Instance.Food}), Wood({GameManager.Instance.Wood}), Metal({GameManager.Instance.Metal})");
        Debug.Log($"Has enough resources: {hasEnoughResources}");

        // Disable the button and apply greyscale if not enough resources
        upgradeConfirmButton.interactable = hasEnoughResources;
        upgradeButtonImage.color = hasEnoughResources ? originalButtonColor : new Color(0.5f, 0.5f, 0.5f, 1f);

        // Format and update resources with color change if needed
        UpdateResourceText(foodText, GameManager.Instance.Food, foodCost);
        UpdateResourceText(woodText, GameManager.Instance.Wood, woodCost);
        UpdateResourceText(metalText, GameManager.Instance.Metal, metalCost);

        // Only show gold cost for express upgrade
        if (building.IsExpressUpgrade)
        {
            expressUpgradeGoldCostText.gameObject.SetActive(true);
            expressUpgradeGoldCostText.text = $"{NumberFormatter.FormatLargeNumber(goldCost)}";
        }
        else
        {
            expressUpgradeGoldCostText.gameObject.SetActive(false);
        }

        // Update Battle Power & Welfare UI
        battlePowerText.text = $"{battlePowerGain}";
        welfareText.text = $"{welfareGain}";

        // Calculate and format the required time for the upgrade
        float requiredTime = building.GetRequiredUpgradeTime(building.CurrentLevel);
        timeText.text = $"{TimeFormatter.FormatTime(requiredTime)}";

    }

    void UpdateResourceText(TextMeshProUGUI textElement, int currentAmount, int requiredAmount)
    {
        // Format the numbers with the global number format
        string formattedCurrent = NumberFormatter.FormatLargeNumber(currentAmount);
        string formattedRequired = NumberFormatter.FormatLargeNumber(requiredAmount);

        // Apply color for the required amount if not enough resources
        string currentColor = (currentAmount < requiredAmount) ? "<color=red>" : "<color=green>";
        string colorReset = "</color>";

        textElement.text = $"{currentColor}{formattedCurrent}{colorReset} / {formattedRequired}";
    }

    public static class TimeFormatter
    {
        public static string FormatTime(float totalSeconds)
        {
            int days = Mathf.FloorToInt(totalSeconds / (24 * 3600)); // Number of full days
            totalSeconds -= days * 24 * 3600;

            int hours = Mathf.FloorToInt(totalSeconds / 3600); // Remaining hours
            totalSeconds -= hours * 3600;

            int minutes = Mathf.FloorToInt(totalSeconds / 60); // Remaining minutes
            int seconds = Mathf.FloorToInt(totalSeconds % 60); // Remaining seconds

            if (days > 0)
            {
                return $"{days}d {hours:00}:{minutes:00}:{seconds:00}";
            }
            else
            {
                return $"{hours:00}:{minutes:00}:{seconds:00}";
            }
        }
    }

}
