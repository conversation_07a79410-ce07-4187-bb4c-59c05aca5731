using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections;

public class ButtonShrinkAnimation : Mono<PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler
{
    [SerializeField] private float shrinkScale = 0.9f; // Scale to shrink to
    [SerializeField] private float animationDuration = 0.1f; // Duration of the shrink and expand animation

    private RectTransform rectTransform;
    private Vector3 originalScale;

    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        originalScale = rectTransform.localScale;
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        StopAllCoroutines();
        StartCoroutine(Shrink());
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        StopAllCoroutines();
        StartCoroutine(Expand());
    }

    private IEnumerator Shrink()
    {
        float elapsedTime = 0f;
        Vector3 targetScale = originalScale * shrinkScale;

        while (elapsedTime < animationDuration)
        {
            rectTransform.localScale = Vector3.Lerp(originalScale, targetScale, elapsedTime / animationDuration);
            elapsedTime += Time.deltaTime;
            yield return null;
        }

        rectTransform.localScale = targetScale;
    }

    private IEnumerator Expand()
    {
        float elapsedTime = 0f;
        Vector3 targetScale = originalScale;

        while (elapsedTime < animationDuration)
        {
            rectTransform.localScale = Vector3.Lerp(rectTransform.localScale, targetScale, elapsedTime / animationDuration);
            elapsedTime += Time.deltaTime;
            yield return null;
        }

        rectTransform.localScale = targetScale;
    }
}
