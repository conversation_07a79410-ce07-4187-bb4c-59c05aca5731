using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections.Generic;

public class MenuManager : MonoBehaviour
{
    [SerializeField] private string uiToHideName; // Name of the UI element to hide
    [SerializeField] private string menuLayerName = "Overlay Menus"; // Layer name for overlay menus

    private GameObject uiToHide; // Reference to the UI element to hide
    private List<GameObject> overlayMenus = new List<GameObject>(); // List of all overlay menus

    private bool isUIHidden = false; // Track whether UI is currently hidden

    private void Start()
    {
        // Find the UI element to hide
        if (!string.IsNullOrEmpty(uiToHideName))
        {
            uiToHide = GameObject.Find(uiToHideName);
        }

        // Ensure the UI starts active
        if (uiToHide != null)
        {
            uiToHide.SetActive(true);
        }

        // Delay the initial check to ensure all objects are initialized
        Invoke("FindOverlayMenus", 0.1f);
    }

    private void FindOverlayMenus()
    {
        overlayMenus.Clear();

        // Get all objects in the scene using the non-deprecated method
        GameObject[] allObjects = GameObject.FindObjectsByType<GameObject>(FindObjectsInactive.Include, FindObjectsSortMode.None);

        foreach (GameObject obj in allObjects)
        {
            if (obj.layer == LayerMask.NameToLayer(menuLayerName))
            {
                overlayMenus.Add(obj);
                SetupOverlayClickEvent(obj);
                Debug.Log("Found overlay menu: " + obj.name + ", active: " + obj.activeSelf);
            }
        }

        // Initial check to hide UI if any menu is open
        CheckMenusAndHideUI();
    }

    private void SetupOverlayClickEvent(GameObject menu)
    {
        GameObject overlay = menu.transform.Find("GreyOverlay")?.gameObject;

        if (overlay != null)
        {
            EventTrigger trigger = overlay.GetComponent<EventTrigger>() ?? overlay.AddComponent<EventTrigger>();

            EventTrigger.Entry entry = new EventTrigger.Entry
            {
                eventID = EventTriggerType.PointerClick
            };
            entry.callback.AddListener((data) => CloseMenu(menu));

            trigger.triggers.Add(entry);
        }
    }

    private void Update()
    {
        CheckMenusAndHideUI();

        // Close the active menu when pressing ESC
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            foreach (GameObject menu in overlayMenus)
            {
                if (menu.activeSelf)
                {
                    CloseMenu(menu);
                    break;
                }
            }
        }
    }

    private void CheckMenusAndHideUI()
    {
        bool anyMenuOpen = false;

        foreach (GameObject menu in overlayMenus)
        {
            if (menu.activeSelf)
            {
                anyMenuOpen = true;
                break;
            }
        }

        // Hide UI only when necessary
        if (uiToHide != null)
        {
            if (anyMenuOpen && !isUIHidden)
            {
                uiToHide.SetActive(false);
                isUIHidden = true;
                Debug.Log("UI hidden");
            }
            else if (!anyMenuOpen && isUIHidden)
            {
                uiToHide.SetActive(true);
                isUIHidden = false;
                Debug.Log("UI shown");
            }
        }
    }

    private void CloseMenu(GameObject menu)
    {
        if (menu != null)
        {
            menu.SetActive(false);
            Debug.Log("Menu closed: " + menu.name);
        }
    }
}