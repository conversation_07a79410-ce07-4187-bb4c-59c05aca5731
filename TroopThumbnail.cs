using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class TroopThumbnail : Mono<PERSON><PERSON>aviour
{
    public Image thumbnailImage;
    public Image frameImage;
    public Image lockImage;
    public TextMeshProUGUI levelText;
    public Button selectButton;

    private TroopSO troop;
    private TrainingUI trainingUI;

    public TroopSO Troop => troop;

    public void Initialize(TroopSO troopSO, TrainingUI ui)
    {
        Debug.Log($"TroopThumbnail.Initialize called for troop: {troopSO.Name}");

        troop = troopSO;
        trainingUI = ui;

        if (trainingUI == null)
        {
            // This is expected when used with InfantryTrainingUI
            Debug.Log("TrainingUI reference is null in Initialize - this is expected when used with InfantryTrainingUI");
        }

        // Set thumbnail image
        if (thumbnailImage != null)
        {
            if (troop.Thumbnail != null)
            {
                thumbnailImage.sprite = troop.Thumbnail;
                Debug.Log($"Set thumbnail image sprite: {troop.Thumbnail.name}");
            }
            else if (troop.Image != null)
            {
                // Use the troop image as a fallback if thumbnail is null
                thumbnailImage.sprite = troop.Image;
                Debug.LogWarning($"Using troop image as thumbnail for {troop.Name} because thumbnail is null");
            }
            else
            {
                // Use null sprite if both thumbnail and image are null
                Debug.LogWarning($"Both thumbnail and image are null for {troop.Name}, using null sprite");
                thumbnailImage.sprite = null;
            }
        }
        else
        {
            Debug.LogError("thumbnailImage is null");
        }

        // Set level text
        if (levelText != null)
        {
            levelText.text = troop.Level.ToString();
            Debug.Log($"Set level text: {troop.Level}");
        }
        else
        {
            Debug.LogError("levelText is null");
        }

        // Check if troop is unlocked
        bool isUnlocked = TrainingManager.Instance.IsTroopUnlocked(troop);
        Debug.Log($"Troop {troop.Name} is {(isUnlocked ? "unlocked" : "locked")}");

        // Handle locked state visuals
        if (thumbnailImage != null)
        {
            // Apply grayscale effect to locked troops
            if (!isUnlocked)
            {
                // Set the color to gray for locked troops
                thumbnailImage.color = new Color(0.5f, 0.5f, 0.5f, 1f);
                Debug.Log("Applied gray color to locked troop thumbnail");
            }
            else
            {
                // Normal color for unlocked troops
                thumbnailImage.color = Color.white;
                Debug.Log("Applied white color to unlocked troop thumbnail");
            }
        }

        if (lockImage != null)
        {
            lockImage.gameObject.SetActive(!isUnlocked);
            Debug.Log($"Set lock image active: {!isUnlocked}");
        }
        else
        {
            Debug.LogError("lockImage is null");
        }

        // Set button interactable
        if (selectButton != null)
        {
            // Remove any existing listeners to prevent duplicates
            selectButton.onClick.RemoveAllListeners();
            Debug.Log("Removed existing button listeners");

            // Add button listener
            selectButton.onClick.AddListener(OnThumbnailClicked);
            Debug.Log("Added OnThumbnailClicked listener");

            // Set button interactable
            selectButton.interactable = isUnlocked;
            Debug.Log($"Set button interactable: {isUnlocked}");

            Debug.Log($"Set up button for {troop.Name} (Level {troop.Level}), interactable: {isUnlocked}");
        }
        else
        {
            Debug.LogError($"Select button is null for {troop.Name} (Level {troop.Level})");
        }
    }

    private void OnThumbnailClicked()
    {
        Debug.Log($"OnThumbnailClicked called for troop: {troop.Name} (Level {troop.Level})");

        if (trainingUI != null)
        {
            Debug.Log($"Calling trainingUI.SelectTroop with troop: {troop.Name}");
            trainingUI.SelectTroop(troop);
        }
        else
        {
            // Try to find InfantryTrainingUI in parent hierarchy
            InfantryTrainingUI infantryUI = GetComponentInParent<InfantryTrainingUI>();
            if (infantryUI != null)
            {
                Debug.Log($"Found InfantryTrainingUI, calling SelectTroop with troop: {troop.Name}");
                infantryUI.SelectTroop(troop);
            }
            else
            {
                Debug.LogWarning($"trainingUI is null and no InfantryTrainingUI found, cannot select troop: {troop.Name}");
            }
        }
    }

    // Flag to prevent infinite recursion
    private bool isUpdatingSelection = false;

    public void UpdateSelection(bool isSelected)
    {
        // Prevent infinite recursion
        if (isUpdatingSelection)
        {
            Debug.Log($"UpdateSelection: Preventing recursive call for troop: {troop.Name}");
            return;
        }

        isUpdatingSelection = true;
        Debug.Log($"UpdateSelection called for troop: {troop.Name}, isSelected: {isSelected}");

        try
        {
            if (frameImage != null)
            {
                // Make sure the frame is active for selected thumbnails
                frameImage.gameObject.SetActive(isSelected);
                Debug.Log($"Set frame image active: {isSelected}");

                if (isSelected)
                {
                    Debug.Log($"Selected troop: {troop.Name} (Level {troop.Level})");

                    // If this thumbnail is selected, make sure the TrainingUI knows about it
                    // BUT only call SelectTroop if we're not already being called from SelectTroop
                    if (TrainingUI.Instance != null && troop != null && trainingUI != null)
                    {
                        // Use the direct reference to avoid the singleton pattern
                        // This helps prevent infinite recursion
                        if (trainingUI.selectedTroop != troop)
                        {
                            Debug.Log($"Notifying TrainingUI about selection: {troop.Name}");
                            OnThumbnailClicked();
                        }
                    }
                }
            }
            else
            {
                Debug.LogWarning($"frameImage is null for troop: {troop.Name}, cannot show selection state");
                // Don't try to create a frame image at runtime, as this can cause memory leaks
                // Just log the warning and continue
            }
        }
        finally
        {
            isUpdatingSelection = false;
        }
    }
}
