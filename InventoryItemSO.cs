using UnityEngine;

[CreateAssetMenu(fileName = "NewInventoryItem", menuName = "Inventory/Item")]
public class InventoryItemSO : ScriptableObject
{
    public string ID;
    public string Name;
    public ItemCategory Category;
    public string Type;
    public ItemRarity Rarity;
    public int Value;
    public string Tag;
    public Sprite Icon;
    public string Description;
    public bool canBeUsedFromInventory;
}

public enum ItemCategory
{
    Resources,
    SpeedUp,
    Battle,
    Hero,
    Other
}

public enum ItemRarity
{
    Uncommon,
    Rare,
    Elite,
    Legendary
}
