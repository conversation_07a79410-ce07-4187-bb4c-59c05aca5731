using UnityEngine;

public class UIFlasher : MonoBehaviour
{
    [Header("Target UI Element")]
    public GameObject target;        // The UI object to flash

    [<PERSON><PERSON>("Timing Settings")]
    public float interval = 1.0f;    // Total cycle time (on + off)
    public float onTime = 0.3f;      // How long the object stays ON during the interval

    private float timer = 0f;
    private bool isOn = false;

    void Update()
    {
        if (target == null) return;

        timer += Time.deltaTime;

        if (!isOn && timer >= interval - onTime)
        {
            // Turn ON
            target.SetActive(true);
            isOn = true;
        }

        if (isOn && timer >= interval)
        {
            // Turn OFF
            target.SetActive(false);
            isOn = false;
            timer = 0f;
        }
    }
}
