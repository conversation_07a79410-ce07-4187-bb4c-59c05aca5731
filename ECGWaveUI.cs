using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class ECGWaveUI : MaskableGraphic
{
    [Header("Wave Settings")]
    public float speed = 100f;
    public float amplitude = 20f;
    public float frequency = 1f; // Heart rate in beats per second
    public float pointSpacing = 2f;
    public float lineThickness = 2f;

    [Header("ECG Pattern")]
    public float baselineNoise = 0.5f; // Small random noise on baseline
    public float pWaveHeight = 0.3f;
    public float qrsComplexHeight = 2.0f;
    public float tWaveHeight = 0.4f;

    [Header("Visual Effects")]
    public Color waveColor = Color.green;
    public bool enableGlow = true;
    public Color glowColor = new Color(0, 1, 0, 0.5f);
    public float glowIntensity = 1.5f;

    private List<Vector2> points = new List<Vector2>();
    private float offset;
    private Material glowMaterial;
    private bool materialInitialized = false;

    protected override void Start()
    {
        base.Start();
        InitializeGlowMaterial();
    }

    private void InitializeGlowMaterial()
    {
        if (enableGlow && !materialInitialized)
        {
            // Try to find a suitable glow shader
            Shader glowShader = Shader.Find("Custom/UIGlow");
            if (glowShader == null)
                glowShader = Shader.Find("Custom/SpriteGlow");

            if (glowShader != null)
            {
                glowMaterial = new Material(glowShader);
                glowMaterial.SetColor("_GlowColor", glowColor);
                glowMaterial.SetFloat("_GlowIntensity", glowIntensity);

                // Apply the material to this graphic
                material = glowMaterial;
            }
            materialInitialized = true;
        }
    }

    protected override void OnPopulateMesh(VertexHelper vh)
    {
        vh.Clear();

        points.Clear();
        float width = rectTransform.rect.width;
        float height = rectTransform.rect.height / 2f;

        int pointCount = Mathf.CeilToInt(width / pointSpacing) + 1;

        for (int i = 0; i < pointCount; i++)
        {
            float x = i * pointSpacing;
            float worldX = x + offset;

            // Calculate ECG waveform
            float y = CalculateECGWaveform(worldX) * amplitude;

            points.Add(new Vector2(x, height + y));
        }

        // Draw the waveform
        for (int i = 0; i < points.Count - 1; i++)
        {
            DrawLine(vh, points[i], points[i + 1], lineThickness, waveColor);
        }
    }

    private float CalculateECGWaveform(float x)
    {
        // Convert x to time-based coordinate
        float t = x * 0.01f;

        // Calculate the position within one heartbeat cycle
        float cycleLength = 1f / frequency; // Length of one heartbeat in time units
        float cyclePosition = Mathf.Repeat(t, cycleLength) / cycleLength; // 0 to 1 within cycle

        float ecgValue = 0f;

        // Add baseline noise
        ecgValue += (Mathf.PerlinNoise(t * 10f, 0) - 0.5f) * baselineNoise;

        // P Wave (0.0 - 0.15)
        if (cyclePosition >= 0.0f && cyclePosition <= 0.15f)
        {
            float pPhase = (cyclePosition - 0.075f) / 0.075f; // Center around 0.075
            ecgValue += Mathf.Exp(-pPhase * pPhase * 8f) * pWaveHeight;
        }

        // QRS Complex (0.2 - 0.35)
        if (cyclePosition >= 0.2f && cyclePosition <= 0.35f)
        {
            float qrsPhase = (cyclePosition - 0.275f) / 0.075f; // Center around 0.275

            // Q wave (small negative)
            if (cyclePosition >= 0.2f && cyclePosition <= 0.23f)
            {
                float qPhase = (cyclePosition - 0.215f) / 0.015f;
                ecgValue -= Mathf.Exp(-qPhase * qPhase * 20f) * 0.2f;
            }
            // R wave (large positive)
            else if (cyclePosition >= 0.23f && cyclePosition <= 0.29f)
            {
                float rPhase = (cyclePosition - 0.26f) / 0.03f;
                ecgValue += Mathf.Exp(-rPhase * rPhase * 15f) * qrsComplexHeight;
            }
            // S wave (negative)
            else if (cyclePosition >= 0.29f && cyclePosition <= 0.35f)
            {
                float sPhase = (cyclePosition - 0.32f) / 0.03f;
                ecgValue -= Mathf.Exp(-sPhase * sPhase * 15f) * 0.4f;
            }
        }

        // T Wave (0.45 - 0.7)
        if (cyclePosition >= 0.45f && cyclePosition <= 0.7f)
        {
            float tPhase = (cyclePosition - 0.575f) / 0.125f; // Center around 0.575
            ecgValue += Mathf.Exp(-tPhase * tPhase * 4f) * tWaveHeight;
        }

        return ecgValue;
    }

    void Update()
    {
        offset += speed * Time.deltaTime;
        SetVerticesDirty();

        // Update glow effect
        if (enableGlow && glowMaterial != null)
        {
            // Pulse the glow intensity slightly
            float pulseGlow = glowIntensity * (1f + Mathf.Sin(Time.time * 2f) * 0.1f);
            glowMaterial.SetFloat("_GlowIntensity", pulseGlow);
        }
    }

    void DrawLine(VertexHelper vh, Vector2 start, Vector2 end, float thickness, Color color)
    {
        Vector2 direction = (end - start).normalized;
        Vector2 normal = new Vector2(-direction.y, direction.x) * thickness / 2f;

        UIVertex[] verts = new UIVertex[4];
        verts[0].position = start - normal;
        verts[1].position = start + normal;
        verts[2].position = end + normal;
        verts[3].position = end - normal;

        for (int i = 0; i < 4; i++)
        {
            verts[i].color = color;
        }

        vh.AddUIVertexQuad(verts);
    }

    // Public methods for runtime control
    public void SetHeartRate(float beatsPerSecond)
    {
        frequency = beatsPerSecond;
    }

    public void SetGlowEnabled(bool enabled)
    {
        enableGlow = enabled;
        if (!enabled && material == glowMaterial)
        {
            material = null;
        }
        else if (enabled && glowMaterial != null)
        {
            material = glowMaterial;
        }
    }
}
