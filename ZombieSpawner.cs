using UnityEngine;
using UnityEngine.AI;
using System.Collections;
using System.Collections.Generic;

public class ZombieSpawner : MonoBehaviour
{
    [Header("References")]
    public ZombieStatsManager statsManager;

    [Header("Spawn Settings")]
    [SerializeField] private float spawnRadius = 30f;
    [SerializeField] private float minDistanceFromPlayer = 15f;

    private Dictionary<ZombieType, int> remainingZombies = new Dictionary<ZombieType, int>();
    private Dictionary<ZombieType, int> activeZombies = new Dictionary<ZombieType, int>();

    private Transform player;
    private bool isBossWave = false;
    private bool isSpawningActive = true;

    void Start()
    {
        // Find player reference
        player = GameObject.FindGameObjectWithTag("FPS").transform;
        
        if (player == null || statsManager == null)
        {
            Debug.LogError("Missing references in ZombieSpawner!");
            enabled = false;
            return;
        }

        InitializeWave();
    }

    private void InitializeWave()
    {
        // Initialize counters for each zombie type
        foreach (ZombieType type in System.Enum.GetValues(typeof(ZombieType)))
        {
            remainingZombies[type] = statsManager.GetZombieCount(type);
            activeZombies[type] = 0;
        }

        isBossWave = remainingZombies[ZombieType.Boss] > 0;

        // Start spawning coroutines for each type
        StartCoroutine(SpawnZombieType(ZombieType.Slow));
        StartCoroutine(SpawnZombieType(ZombieType.Fast));
        
        if (isBossWave)
        {
            StartCoroutine(SpawnZombieType(ZombieType.Boss));
        }
    }

    IEnumerator SpawnZombieType(ZombieType type)
    {
        float spawnInterval = statsManager.GetSpawnInterval(type);
        
        while (isSpawningActive && remainingZombies[type] > 0)
        {
            if (CanSpawnZombie(type))
            {
                Vector3 spawnPoint;
                if (FindValidSpawnPoint(out spawnPoint))
                {
                    SpawnZombie(type, spawnPoint);
                    remainingZombies[type]--;
                    activeZombies[type]++;
                }
            }
            
            if (spawnInterval > 0)
            {
                yield return new WaitForSeconds(spawnInterval);
            }
            else
            {
                yield return null;
            }
        }
    }

    private bool CanSpawnZombie(ZombieType type)
    {
        // Boss should spawn immediately when available
        if (type == ZombieType.Boss)
            return remainingZombies[type] > 0;

        // Don't spawn regular zombies if boss is active
        if (isBossWave && activeZombies[ZombieType.Boss] > 0)
            return false;

        return remainingZombies[type] > 0;
    }

    private void SpawnZombie(ZombieType type, Vector3 position)
    {
        GameObject zombiePrefab = statsManager.GetConfigForType(type).prefab;
        GameObject zombie = Instantiate(zombiePrefab, position, Quaternion.identity);
        
        ZombieAI zombieAI = zombie.GetComponent<ZombieAI>();
        if (zombieAI != null)
        {
            ZombieStats stats = statsManager.GetZombieStats(type);
            zombieAI.Initialize(stats, type);
        }
    }

    public void OnZombieDeath(ZombieType type)
    {
        activeZombies[type]--;
        
        // Check if wave is complete
        if (IsWaveComplete())
        {
            // Trigger wave completion event or next wave
            Debug.Log("Wave Complete!");
        }
    }

    private bool IsWaveComplete()
    {
        foreach (ZombieType type in System.Enum.GetValues(typeof(ZombieType)))
        {
            if (remainingZombies[type] > 0 || activeZombies[type] > 0)
                return false;
        }
        return true;
    }

    bool FindValidSpawnPoint(out Vector3 spawnPoint)
    {
        for (int i = 0; i < 10; i++) // Try 10 times to find a valid spot
        {
            Vector3 randomPoint = player.position + Random.insideUnitSphere * spawnRadius;
            randomPoint.y = player.position.y; // Keep it at ground level

            NavMeshHit hit;
            if (NavMesh.SamplePosition(randomPoint, out hit, 2f, NavMesh.AllAreas))
            {
                if (Vector3.Distance(hit.position, player.position) >= minDistanceFromPlayer)
                {
                    spawnPoint = hit.position;
                    return true;
                }
            }
        }
        spawnPoint = Vector3.zero;
        return false;
    }
}
