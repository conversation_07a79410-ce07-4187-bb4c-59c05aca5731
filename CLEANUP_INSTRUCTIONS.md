# Cleanup Instructions

To fix the compilation errors, follow these steps:

## 1. Delete or rename conflicting files

Delete or rename these files:
- Assets\Hero System\GameDataManager.cs
- Assets\Hero System\GameDataManagerSqlite.cs
- Assets\Hero System\GameManagerExtension.cs

## 2. Fix GameManager.cs

Open `Assets\Game System\GameManager.cs` and change:
```csharp
public class GameManager : MonoBehaviour
```
to:
```csharp
public partial class GameManager : MonoBehaviour
```

## 3. Fix HeroData.cs

Open `Assets\Hero System\HeroData.cs` and wrap the class in a namespace:
```csharp
namespace HeroSystem
{
    public class HeroData : MonoBehaviour
    {
        // Existing code...
    }
}
```

## 4. Use the new implementation

Keep these new files:
- Assets\Hero System\GameDataManagerImpl.cs
- Assets\Hero System\GameManagerAdapterImpl.cs
- Assets\Hero System\Editor\GameDataManagerSetup.cs

## 5. Add SQLite references

1. Download the SQLite DLLs:
   - Mono.Data.Sqlite.dll
   - System.Data.dll

2. Create a `Plugins` folder in your Assets directory if it doesn't exist
   
3. Copy the DLLs to the `Assets\Plugins` folder

## 6. Attach the adapter

1. Find your GameManager GameObject in the scene
2. Add the GameManagerAdapterImpl component to it

## 7. Update any references

If you were using GameDataManager directly, update your code to use GameDataManagerImpl instead.

## 8. Rebuild the project

After making these changes, rebuild your project to make sure all errors are fixed.
