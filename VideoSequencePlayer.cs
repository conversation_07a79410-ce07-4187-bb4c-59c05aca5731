using UnityEngine;
using UnityEngine.Video;
using UnityEngine.UI;

public class VideoSequencePlayer : MonoBehaviour
{
    public VideoPlayer videoPlayer;
    public RawImage displayImage;
    public VideoClip firstVideo;   // Optional: If assigned, plays first
    public VideoClip secondVideo;  // Required: Plays if first is missing or after first
    
    public delegate void VideoPlaybackFailedHandler();
    public event VideoPlaybackFailedHandler OnVideoPlaybackFailed;

    private void OnEnable()
    {
        InitializeVideoPlayback();
    }

    private void OnDisable()
    {
        if (videoPlayer != null)
        {
            videoPlayer.loopPointReached -= OnFirstVideoEnd;
            videoPlayer.errorReceived -= OnVideoError;
        }
    }

    public void InitializeVideoPlayback()
    {
        if (videoPlayer == null)
        {
            Debug.LogError("Video Player not assigned!");
            OnVideoPlaybackFailed?.Invoke();
            return;
        }

        // Stop any current playback
        videoPlayer.Stop();
        
        // Remove any existing event handlers
        videoPlayer.loopPointReached -= OnFirstVideoEnd;
        videoPlayer.errorReceived -= OnVideoError;
        
        // Add error handler
        videoPlayer.errorReceived += OnVideoError;

        if (firstVideo != null) 
        {
            // Play first video if assigned
            videoPlayer.clip = firstVideo;
            videoPlayer.isLooping = false;
            videoPlayer.loopPointReached += OnFirstVideoEnd;
        }
        else if (secondVideo != null)
        {
            // If no first video, play second video directly in loop
            videoPlayer.clip = secondVideo;
            videoPlayer.isLooping = true;
        }
        else
        {
            Debug.LogError("No videos assigned to VideoSequencePlayer!");
            OnVideoPlaybackFailed?.Invoke();
            return;
        }

        // Prepare and play
        videoPlayer.Prepare();
        videoPlayer.prepareCompleted += (VideoPlayer vp) =>
        {
            if (vp.isPrepared)
            {
                vp.Play();
            }
            else
            {
                OnVideoPlaybackFailed?.Invoke();
            }
        };
    }

    private void OnVideoError(VideoPlayer vp, string errorMessage)
    {
        Debug.LogError($"Video playback error: {errorMessage}");
        OnVideoPlaybackFailed?.Invoke();
    }

    private void OnFirstVideoEnd(VideoPlayer vp)
    {
        if (secondVideo == null)
        {
            Debug.LogError("Second video is missing!");
            OnVideoPlaybackFailed?.Invoke();
            return;
        }

        videoPlayer.loopPointReached -= OnFirstVideoEnd;
        
        videoPlayer.clip = secondVideo;
        videoPlayer.isLooping = true;
        videoPlayer.Play();
    }
}
