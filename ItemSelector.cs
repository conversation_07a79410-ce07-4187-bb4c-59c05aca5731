using UnityEngine;

public class ItemSelector : MonoBehaviour
{
    [SerializeField] private ItemDetailsUI itemDetailsUI; // Reference to the ItemDetailsUI script

    private ItemUI selectedItemUI;

    public void OnItemSelected(InventoryItem item, ItemUI itemUI)
    {
        if (selectedItemUI != null)
        {
            selectedItemUI.SetSelected(false);
        }

        selectedItemUI = itemUI;
        selectedItemUI.SetSelected(true);

        // Show item details
        itemDetailsUI.ShowItemDetails(item);
    }
}
