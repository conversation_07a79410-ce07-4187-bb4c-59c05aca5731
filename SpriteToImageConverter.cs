using UnityEngine;
using UnityEngine.UI;

public class SpriteToImageConverter : MonoBehaviour
{
    public void ConvertSpritesToImages()
    {
        SpriteRenderer[] spriteRenderers = GetComponentsInChildren<SpriteRenderer>();
        
        foreach (SpriteRenderer sr in spriteRenderers)
        {
            GameObject imageObj = new GameObject(sr.gameObject.name);
            imageObj.transform.SetParent(transform);
            
            RectTransform rectTransform = imageObj.AddComponent<RectTransform>();
            rectTransform.anchoredPosition = sr.transform.position;
            rectTransform.sizeDelta = new Vector2(sr.sprite.rect.width, sr.sprite.rect.height);
            rectTransform.rotation = sr.transform.rotation;
            rectTransform.localScale = sr.transform.localScale;
            
            Image image = imageObj.AddComponent<Image>();
            image.sprite = sr.sprite;
            image.color = sr.color;
            image.SetNativeSize();
            
            DestroyImmediate(sr.gameObject);
        }
    }
}