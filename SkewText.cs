using TMPro;
using UnityEngine;

[RequireComponent(typeof(TextMeshProUGUI))]
public class SkewText : MonoBehaviour
{
    [SerializeField] private float horizontalSkew = 0.2f; // Horizontal skew amount
    [SerializeField] private float verticalSkew = 0.0f;   // Vertical skew amount

    private TextMeshProUGUI tmp;
    private bool hasTextChanged = false;

    public float HorizontalSkew
    {
        get => horizontalSkew;
        set
        {
            horizontalSkew = value;
            UpdateSkew();
        }
    }

    public float VerticalSkew
    {
        get => verticalSkew;
        set
        {
            verticalSkew = value;
            UpdateSkew();
        }
    }

    // Legacy property for backward compatibility
    [System.Obsolete("Use HorizontalSkew instead")]
    public float SkewAmount
    {
        get => horizontalSkew;
        set
        {
            horizontalSkew = value;
            UpdateSkew();
        }
    }

    void Awake()
    {
        tmp = GetComponent<TextMeshProUGUI>();
    }

    void Start()
    {
        // Register for text change events
        TMPro_EventManager.TEXT_CHANGED_EVENT.Add(OnTextChanged);
        UpdateSkew();
    }

    void OnDestroy()
    {
        // Unregister from events
        TMPro_EventManager.TEXT_CHANGED_EVENT.Remove(OnTextChanged);
    }

    void OnTextChanged(Object obj)
    {
        if (obj == tmp)
        {
            hasTextChanged = true;
        }
    }

    void LateUpdate()
    {
        if (hasTextChanged)
        {
            UpdateSkew();
            hasTextChanged = false;
        }
    }

    public void UpdateSkew()
    {
        if (tmp == null) return;

        // Force the text to generate if it hasn't already
        tmp.ForceMeshUpdate();

        TMP_TextInfo textInfo = tmp.textInfo;

        // Apply skew to each character
        for (int i = 0; i < textInfo.characterCount; i++)
        {
            if (!textInfo.characterInfo[i].isVisible) continue;

            int materialIndex = textInfo.characterInfo[i].materialReferenceIndex;
            int vertexIndex = textInfo.characterInfo[i].vertexIndex;

            Vector3[] vertices = textInfo.meshInfo[materialIndex].vertices;

            // Get the character's center points for reference
            Vector3 charMidBaseline = (vertices[vertexIndex + 0] + vertices[vertexIndex + 2]) * 0.5f;
            Vector3 charMidVertical = (vertices[vertexIndex + 0] + vertices[vertexIndex + 1]) * 0.5f;

            // Apply skew to each vertex of the character
            for (int j = 0; j < 4; j++)
            {
                Vector3 vertex = vertices[vertexIndex + j];

                // Apply horizontal skew based on vertical distance from baseline
                if (horizontalSkew != 0)
                {
                    vertex.x += (vertex.y - charMidBaseline.y) * horizontalSkew;
                }

                // Apply vertical skew based on horizontal distance from center
                if (verticalSkew != 0)
                {
                    vertex.y += (vertex.x - charMidVertical.x) * verticalSkew;
                }

                vertices[vertexIndex + j] = vertex;
            }
        }

        // Update the mesh
        for (int i = 0; i < textInfo.meshInfo.Length; i++)
        {
            textInfo.meshInfo[i].mesh.vertices = textInfo.meshInfo[i].vertices;
            tmp.UpdateGeometry(textInfo.meshInfo[i].mesh, i);
        }
    }

    // Public method to refresh the skew effect
    public void Refresh()
    {
        UpdateSkew();
    }
}