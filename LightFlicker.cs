using UnityEngine;
using System.Collections;

[RequireComponent(typeof(Light))]
public class LightFlicker : MonoBehaviour
{
    [Header("Timing Settings")]
    [Range(0.01f, 1f)]
    public float minFlickerTime = 0.05f;
    [Range(0.01f, 1f)]
    public float maxFlickerTime = 0.2f;
    
    [Header("Intensity Settings")]
    public float baseIntensity = 1f;
    [Range(0f, 1f)]
    public float intensityVariation = 0.5f;
    
    [Header("Flicker Pattern")]
    [Range(0f, 1f)]
    public float flickerChance = 0.5f; // Chance of light turning off
    public bool useIntensityFlicker = true; // If true, varies intensity instead of just on/off
    
    private Light spotLight;
    private float nextChangeTime;
    private float originalIntensity;

    void Start()
    {
        spotLight = GetComponent<Light>();
        originalIntensity = spotLight.intensity;
        StartCoroutine(FlickerRoutine());
    }

    IEnumerator FlickerRoutine()
    {
        while(true)
        {
            // Randomly decide if light should flicker
            if (Random.value < flickerChance)
            {
                if (useIntensityFlicker)
                {
                    // Vary the intensity
                    float randomIntensity = originalIntensity * Random.Range(
                        1f - intensityVariation,
                        1f + intensityVariation
                    );
                    spotLight.intensity = randomIntensity;
                }
                else
                {
                    // Simple on/off flicker
                    spotLight.enabled = !spotLight.enabled;
                }
            }
            else
            {
                // Reset to normal state
                spotLight.enabled = true;
                spotLight.intensity = originalIntensity;
            }

            // Random delay before next flicker
            float randomDelay = Random.Range(minFlickerTime, maxFlickerTime);
            yield return new WaitForSeconds(randomDelay);
        }
    }

    void OnDisable()
    {
        // Ensure light is on when script is disabled
        if (spotLight != null)
        {
            spotLight.enabled = true;
            spotLight.intensity = originalIntensity;
        }
    }
}