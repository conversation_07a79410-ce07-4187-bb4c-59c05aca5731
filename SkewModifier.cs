using TMPro;
using UnityEngine;

[System.Obsolete("SkewModifier is no longer needed. Use SkewText component directly on TextMeshProUGUI objects.")]
public class SkewModifier : MonoBehaviour
{
    // This class is kept for backward compatibility but is no longer functional
    // Please use the SkewText component directly on your TextMeshProUGUI objects instead

    void Start()
    {
        Debug.LogWarning("SkewModifier is obsolete. Please use SkewText component directly on TextMeshProUGUI objects.", this);

        // Auto-migrate if possible
        var skewText = GetComponent<SkewText>();
        if (skewText == null)
        {
            Debug.Log("Adding SkewText component to replace obsolete SkewModifier", this);
            gameObject.AddComponent<SkewText>();
        }

        // Remove this component
        Destroy(this);
    }
}