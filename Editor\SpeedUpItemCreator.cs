using UnityEngine;
using UnityEditor;
using System.IO;

public class SpeedUpItemCreator : EditorWindow
{
    private string basePath = "Assets/Resources/InventoryItems";
    private Sprite defaultIcon;

    [MenuItem("Tools/Training System/Create Speed-Up Items")]
    public static void ShowWindow()
    {
        GetWindow<SpeedUpItemCreator>("Speed-Up Item Creator");
    }

    private void OnGUI()
    {
        GUILayout.Label("Speed-Up Item Creator", EditorStyles.boldLabel);

        basePath = EditorGUILayout.TextField("Base Path", basePath);
        defaultIcon = (Sprite)EditorGUILayout.ObjectField("Default Icon", defaultIcon, typeof(Sprite), false);

        if (GUILayout.Button("Create Training Speed-Up Items"))
        {
            CreateTrainingSpeedUpItems();
        }

        if (GUILayout.But<PERSON>("Create General Speed-Up Items"))
        {
            CreateGeneralSpeedUpItems();
        }
    }

    private void CreateTrainingSpeedUpItems()
    {
        // Create directory if it doesn't exist
        if (!Directory.Exists(basePath))
        {
            Directory.CreateDirectory(basePath);
        }

        // Create speed-up items with different durations
        CreateSpeedUpItem("Training Speed-Up (5m)", "TrainingSpeedUp", 5, ItemRarity.Uncommon);
        CreateSpeedUpItem("Training Speed-Up (15m)", "TrainingSpeedUp", 15, ItemRarity.Uncommon);
        CreateSpeedUpItem("Training Speed-Up (30m)", "TrainingSpeedUp", 30, ItemRarity.Rare);
        CreateSpeedUpItem("Training Speed-Up (1h)", "TrainingSpeedUp", 60, ItemRarity.Rare);
        CreateSpeedUpItem("Training Speed-Up (3h)", "TrainingSpeedUp", 180, ItemRarity.Elite);
        CreateSpeedUpItem("Training Speed-Up (8h)", "TrainingSpeedUp", 480, ItemRarity.Elite);
        CreateSpeedUpItem("Training Speed-Up (24h)", "TrainingSpeedUp", 1440, ItemRarity.Legendary);

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log("Created training speed-up items at " + basePath);
    }

    private void CreateGeneralSpeedUpItems()
    {
        // Create directory if it doesn't exist
        if (!Directory.Exists(basePath))
        {
            Directory.CreateDirectory(basePath);
        }

        // Create speed-up items with different durations
        CreateSpeedUpItem("General Speed-Up (5m)", "GeneralSpeedUp", 5, ItemRarity.Uncommon);
        CreateSpeedUpItem("General Speed-Up (15m)", "GeneralSpeedUp", 15, ItemRarity.Uncommon);
        CreateSpeedUpItem("General Speed-Up (30m)", "GeneralSpeedUp", 30, ItemRarity.Rare);
        CreateSpeedUpItem("General Speed-Up (1h)", "GeneralSpeedUp", 60, ItemRarity.Rare);
        CreateSpeedUpItem("General Speed-Up (3h)", "GeneralSpeedUp", 180, ItemRarity.Elite);
        CreateSpeedUpItem("General Speed-Up (8h)", "GeneralSpeedUp", 480, ItemRarity.Elite);
        CreateSpeedUpItem("General Speed-Up (24h)", "GeneralSpeedUp", 1440, ItemRarity.Legendary);

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log("Created general speed-up items at " + basePath);
    }

    private void CreateSpeedUpItem(string name, string tag, int minutes, ItemRarity rarity)
    {
        // Create inventory item scriptable object
        InventoryItemSO itemSO = ScriptableObject.CreateInstance<InventoryItemSO>();
        itemSO.ID = System.Guid.NewGuid().ToString();
        itemSO.Name = name;
        itemSO.Category = ItemCategory.SpeedUp;
        itemSO.Type = "Speed-Up";
        itemSO.Rarity = rarity;
        itemSO.Value = minutes;
        itemSO.Tag = tag;
        itemSO.Icon = defaultIcon;
        itemSO.Description = $"Reduces time by {minutes} minutes for {(tag == "TrainingSpeedUp" ? "troop training" : "any activity")}.";
        itemSO.canBeUsedFromInventory = true;

        // Save asset
        string fileName = name.Replace(" ", "_").Replace("(", "").Replace(")", "");
        string assetPath = Path.Combine(basePath, $"{fileName}.asset");
        AssetDatabase.CreateAsset(itemSO, assetPath);
    }
}
