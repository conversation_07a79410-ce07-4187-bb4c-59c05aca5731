using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

/// <summary>
/// Central manager for the notification system that provides visual feedback about available functions and upgradable objects.
/// </summary>
public class NotificationManager : MonoBehaviour
{
    private static NotificationManager _instance;
    public static NotificationManager Instance => _instance;

    [Header("Notification Settings")]
    [SerializeField] private float checkInterval = 1f; // How often to check for notifications (in seconds)
    [SerializeField] private GameObject defaultNotificationIndicatorPrefab; // Default prefab for notification indicators
    [SerializeField] private AudioClip notificationSound; // Sound to play when a notification becomes available

    [Header("Process-Specific Indicators")]
    [SerializeField] private ProcessIndicator[] processIndicators; // Custom indicators for specific process types

    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true; // Enable debug logs by default

    // Dictionary to track all registered notification targets
    private Dictionary<string, NotificationTarget> notificationTargets = new Dictionary<string, NotificationTarget>();

    // Dictionary to track ongoing processes
    private Dictionary<string, ProcessInfo> ongoingProcesses = new Dictionary<string, ProcessInfo>();

    // Event for when notification status changes
    public event Action<string, bool> OnNotificationStatusChanged;

    private void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // Start checking for notifications
        StartCoroutine(CheckNotificationsRoutine());

        // Start cleanup routine for completed processes
        StartCoroutine(CleanupCompletedProcessesRoutine());

        // Subscribe to relevant events
        SubscribeToEvents();
    }

    private IEnumerator CleanupCompletedProcessesRoutine()
    {
        while (true)
        {
            yield return new WaitForSeconds(5f); // Check every 5 seconds

            // Create a list of process IDs to remove
            List<string> processesToRemove = new List<string>();

            // Find completed processes
            foreach (var kvp in ongoingProcesses)
            {
                ProcessInfo process = kvp.Value;

                // If the process is marked as completed or has expired
                if (process.HasExpired)
                {
                    processesToRemove.Add(kvp.Key);
                    DebugLog($"Found completed process to remove: {kvp.Key} (IsCompleted: {process.IsCompleted}, RemainingTime: {process.RemainingTime}, HasExpired: {process.HasExpired})");
                }
            }

            // Remove the completed processes
            foreach (string processId in processesToRemove)
            {
                if (ongoingProcesses.TryGetValue(processId, out ProcessInfo process))
                {
                    string processType = process.ProcessType;
                    ongoingProcesses.Remove(processId);
                    DebugLog($"Cleanup: Removed completed process: {processId}");

                    // Update notifications for this process type
                    UpdateNotificationsForProcessType(processType);
                }
            }

            // Log the number of remaining processes
            DebugLog($"Cleanup complete. Remaining processes: {ongoingProcesses.Count}");
        }
    }

    private void OnDestroy()
    {
        // Unsubscribe from events
        UnsubscribeFromEvents();
    }

    private void SubscribeToEvents()
    {
        // Subscribe to TrainingManager events
        if (TrainingManager.Instance != null)
        {
            TrainingManager.Instance.OnTrainingStarted += OnTrainingStarted;
            TrainingManager.Instance.OnTrainingCompleted += OnTrainingCompleted;
            TrainingManager.Instance.OnTrainingCancelled += OnTrainingCancelled;
        }

        // Add more event subscriptions as needed for other systems
    }

    private void UnsubscribeFromEvents()
    {
        // Unsubscribe from TrainingManager events
        if (TrainingManager.Instance != null)
        {
            TrainingManager.Instance.OnTrainingStarted -= OnTrainingStarted;
            TrainingManager.Instance.OnTrainingCompleted -= OnTrainingCompleted;
            TrainingManager.Instance.OnTrainingCancelled -= OnTrainingCancelled;
        }

        // Add more event unsubscriptions as needed for other systems
    }

    #region Process Event Handlers

    private void OnTrainingStarted(TroopSO troop, int count, float time)
    {
        // Add to ongoing processes
        string processId = $"Training_{troop.Type}_{troop.Level}";
        string processType = $"Training_{troop.Type}"; // Use specific training type

        DebugLog($"OnTrainingStarted: Registering process {processId} of type {processType} with duration {time}");
        RegisterOngoingProcess(processId, processType, time);

        // Force check all notifications immediately to ensure indicators are updated
        CheckAllNotifications();
    }

    private void OnTrainingCompleted(TroopSO troop, int count)
    {
        // Remove from ongoing processes
        string processId = $"Training_{troop.Type}_{troop.Level}";
        string processType = $"Training_{troop.Type}"; // Use specific training type

        DebugLog($"OnTrainingCompleted: Unregistering process {processId} of type {processType}");
        UnregisterOngoingProcess(processId);

        // Force check all notifications immediately to ensure indicators are updated
        CheckAllNotifications();
    }

    private void OnTrainingCancelled(TroopSO troop)
    {
        // Remove from ongoing processes
        string processId = $"Training_{troop.Type}_{troop.Level}";
        string processType = $"Training_{troop.Type}"; // Use specific training type

        DebugLog($"OnTrainingCancelled: Unregistering process {processId} of type {processType}");
        UnregisterOngoingProcess(processId);

        // Force check all notifications immediately to ensure indicators are updated
        CheckAllNotifications();
    }

    #endregion

    #region Process Management

    /// <summary>
    /// Register a new ongoing process
    /// </summary>
    public void RegisterOngoingProcess(string processId, string processType, float duration)
    {
        ProcessInfo processInfo = new ProcessInfo
        {
            ProcessId = processId,
            ProcessType = processType,
            StartTime = Time.time,
            Duration = duration,
            IsCompleted = false
        };

        ongoingProcesses[processId] = processInfo;
        DebugLog($"Registered process: {processId}, Type: {processType}, Duration: {duration}");

        // Update notifications for this process type immediately
        UpdateNotificationsForProcessType(processType);

        // Force check all notifications to ensure everything is updated
        CheckAllNotifications();
    }

    /// <summary>
    /// Mark a process as completed without removing it immediately
    /// </summary>
    public void MarkProcessCompleted(string processId)
    {
        if (ongoingProcesses.TryGetValue(processId, out ProcessInfo processInfo))
        {
            processInfo.IsCompleted = true;
            string processType = processInfo.ProcessType;
            DebugLog($"Marked process as completed: {processId} of type {processType}");

            // Update notifications for this process type immediately
            UpdateNotificationsForProcessType(processType);

            // Force check all notifications to ensure everything is updated
            CheckAllNotifications();
        }
    }

    /// <summary>
    /// Unregister an ongoing process (when completed or cancelled)
    /// </summary>
    public void UnregisterOngoingProcess(string processId)
    {
        if (ongoingProcesses.TryGetValue(processId, out ProcessInfo processInfo))
        {
            // First mark as completed to ensure any checks see it as completed
            processInfo.IsCompleted = true;

            string processType = processInfo.ProcessType;
            ongoingProcesses.Remove(processId);
            DebugLog($"Unregistered process: {processId}");

            // Update notifications for this process type immediately
            UpdateNotificationsForProcessType(processType);

            // Force check all notifications to ensure everything is updated
            CheckAllNotifications();
        }
    }

    /// <summary>
    /// Check if a specific process type is currently running
    /// </summary>
    public bool IsProcessTypeRunning(string processType)
    {
        bool isRunning = false;
        List<string> completedProcesses = new List<string>();

        // Check if any process of this type is running and not completed
        foreach (var kvp in ongoingProcesses)
        {
            string processId = kvp.Key;
            ProcessInfo process = kvp.Value;

            if (process.ProcessType == processType)
            {
                // Check if the process has expired
                if (process.HasExpired)
                {
                    // Process is completed or expired
                    completedProcesses.Add(processId);
                    DebugLog($"Process {processId} is completed or expired (Remaining time: {process.RemainingTime}, IsCompleted: {process.IsCompleted})");
                }
                else if (!process.IsCompleted)
                {
                    // Process is still running
                    isRunning = true;
                    DebugLog($"Process {processId} is still running (Remaining time: {process.RemainingTime})");
                }
            }
        }

        // Mark completed processes
        foreach (string processId in completedProcesses)
        {
            if (ongoingProcesses.TryGetValue(processId, out ProcessInfo process))
            {
                process.IsCompleted = true;
                DebugLog($"Marked process as completed: {processId}");
            }
        }

        DebugLog($"IsProcessTypeRunning for {processType}: {isRunning}");
        return isRunning;
    }

    /// <summary>
    /// Get all ongoing processes of a specific type
    /// </summary>
    public List<ProcessInfo> GetOngoingProcessesByType(string processType)
    {
        List<ProcessInfo> processes = new List<ProcessInfo>();
        foreach (var process in ongoingProcesses.Values)
        {
            if (process.ProcessType == processType)
            {
                processes.Add(process);
            }
        }
        return processes;
    }

    #endregion

    #region Notification Target Management

    /// <summary>
    /// Register a new notification target
    /// </summary>
    public void RegisterNotificationTarget(string targetId, GameObject targetObject, string processType, Func<bool> availabilityCheck = null)
    {
        if (notificationTargets.ContainsKey(targetId))
        {
            DebugLog($"Notification target already registered: {targetId}");
            return;
        }

        NotificationTarget target = new NotificationTarget
        {
            TargetId = targetId,
            TargetObject = targetObject,
            ProcessType = processType,
            AvailabilityCheck = availabilityCheck,
            IsNotificationActive = false,
            NotificationIndicator = null
        };

        notificationTargets[targetId] = target;
        DebugLog($"Registered notification target: {targetId}, Process Type: {processType}");

        // Check notification status immediately
        CheckNotificationStatus(target);
    }

    /// <summary>
    /// Unregister a notification target
    /// </summary>
    public void UnregisterNotificationTarget(string targetId)
    {
        if (notificationTargets.TryGetValue(targetId, out NotificationTarget target))
        {
            // Remove notification indicator if it exists
            if (target.NotificationIndicator != null)
            {
                Destroy(target.NotificationIndicator);
            }

            notificationTargets.Remove(targetId);
            DebugLog($"Unregistered notification target: {targetId}");
        }
    }

    #endregion

    #region Notification Status Checking

    private IEnumerator CheckNotificationsRoutine()
    {
        while (true)
        {
            yield return new WaitForSeconds(checkInterval);
            CheckAllNotifications();
        }
    }

    /// <summary>
    /// Check all registered notification targets
    /// </summary>
    public void CheckAllNotifications()
    {
        foreach (var target in notificationTargets.Values)
        {
            CheckNotificationStatus(target);
        }
    }

    /// <summary>
    /// Update notifications for a specific process type
    /// </summary>
    private void UpdateNotificationsForProcessType(string processType)
    {
        foreach (var target in notificationTargets.Values)
        {
            if (target.ProcessType == processType)
            {
                CheckNotificationStatus(target);
            }
        }
    }

    /// <summary>
    /// Check notification status for a specific target
    /// </summary>
    private void CheckNotificationStatus(NotificationTarget target)
    {
        if (target.TargetObject == null)
        {
            // Target object has been destroyed, remove from tracking
            UnregisterNotificationTarget(target.TargetId);
            return;
        }

        bool isProcessRunning = IsProcessTypeRunning(target.ProcessType);
        bool hasResources = true; // Default to true

        // Check custom availability condition if provided
        if (target.AvailabilityCheck != null)
        {
            hasResources = target.AvailabilityCheck();
        }

        // Process is available if it's not running and resources are available
        bool isAvailable = !isProcessRunning && hasResources;

        DebugLog($"CheckNotificationStatus for {target.TargetId}: isProcessRunning={isProcessRunning}, hasResources={hasResources}, isAvailable={isAvailable}, currentStatus={target.IsNotificationActive}");

        // Always update notification status to ensure it's correct
        // This ensures the notification is properly hidden when a process starts
        SetNotificationStatus(target, isAvailable);
    }

    /// <summary>
    /// Set notification status for a target
    /// </summary>
    private void SetNotificationStatus(NotificationTarget target, bool isActive)
    {
        target.IsNotificationActive = isActive;

        if (isActive)
        {
            // Create notification indicator if it doesn't exist
            if (target.NotificationIndicator == null)
            {
                // Find the appropriate indicator prefab for this process type
                GameObject indicatorPrefab = defaultNotificationIndicatorPrefab;
                Color glowColor = Color.cyan;
                string label = "";
                Sprite icon = null;

                // Look for a custom indicator for this process type
                if (processIndicators != null)
                {
                    foreach (var processIndicator in processIndicators)
                    {
                        if (processIndicator.ProcessType == target.ProcessType && processIndicator.IndicatorPrefab != null)
                        {
                            indicatorPrefab = processIndicator.IndicatorPrefab;
                            glowColor = processIndicator.GlowColor;
                            label = processIndicator.Label;
                            icon = processIndicator.Icon;
                            break;
                        }
                    }
                }

                // If we have a prefab, instantiate it
                if (indicatorPrefab != null)
                {
                    GameObject indicator = Instantiate(indicatorPrefab, target.TargetObject.transform);
                    target.NotificationIndicator = indicator;

                    // Configure the indicator
                    NotificationIndicator indicatorComponent = indicator.GetComponent<NotificationIndicator>();
                    if (indicatorComponent != null)
                    {
                        indicatorComponent.Initialize(target.TargetId, label);
                        indicatorComponent.SetGlowColor(glowColor);

                        if (icon != null)
                        {
                            indicatorComponent.SetIcon(icon);
                        }
                    }
                }
            }

            // Show the indicator
            if (target.NotificationIndicator != null)
            {
                target.NotificationIndicator.SetActive(true);
            }

            // Play notification sound
            if (notificationSound != null)
            {
                AudioSource.PlayClipAtPoint(notificationSound, Camera.main.transform.position, 0.5f);
            }
        }
        else
        {
            // Hide the indicator
            if (target.NotificationIndicator != null)
            {
                target.NotificationIndicator.SetActive(false);
            }
        }

        // Trigger event
        OnNotificationStatusChanged?.Invoke(target.TargetId, isActive);
        DebugLog($"Notification status changed for {target.TargetId}: {isActive}");
    }

    #endregion

    private void DebugLog(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[NotificationManager] {message}");
        }
    }
}

/// <summary>
/// Information about a notification target
/// </summary>
[System.Serializable]
public class NotificationTarget
{
    public string TargetId;
    public GameObject TargetObject;
    public string ProcessType;
    public Func<bool> AvailabilityCheck;
    public bool IsNotificationActive;
    public GameObject NotificationIndicator;
}

/// <summary>
/// Information about an ongoing process
/// </summary>
[System.Serializable]
public class ProcessInfo
{
    public string ProcessId;
    public string ProcessType;
    public float StartTime;
    public float Duration;
    public bool IsCompleted;

    // Calculate remaining time based on start time and duration
    public float RemainingTime
    {
        get
        {
            if (IsCompleted)
            {
                return 0f;
            }
            return Mathf.Max(0f, (StartTime + Duration) - Time.time);
        }
    }

    // Calculate progress as a percentage (0-1)
    public float Progress
    {
        get
        {
            if (IsCompleted)
            {
                return 1f;
            }
            if (Duration <= 0f)
            {
                return 1f;
            }
            return Mathf.Clamp01((Time.time - StartTime) / Duration);
        }
    }

    // Check if the process has expired based on time
    public bool HasExpired
    {
        get
        {
            return IsCompleted || (Time.time >= (StartTime + Duration));
        }
    }
}

/// <summary>
/// Custom indicator for a specific process type
/// </summary>
[System.Serializable]
public class ProcessIndicator
{
    public string ProcessType; // Type of process (Training, Research, etc.)
    public GameObject IndicatorPrefab; // Custom prefab for this process type
    public Color GlowColor = Color.cyan; // Custom glow color for this process type
    public string Label; // Optional label for the indicator
    public Sprite Icon; // Optional icon for the indicator
}
