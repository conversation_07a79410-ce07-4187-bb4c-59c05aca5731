using UnityEngine;
using UnityEngine.UI;

public class BuildingMenuManager : MonoBehaviour
{
    public static BuildingMenuManager Instance { get; private set; } // Singleton instance
    private GameObject currentBuildingMenu;  // The currently active building's menu
    private Button moveButton;
    private Button upgradeButton;

    private void Awake()
    {
        // Ensure only one instance exists
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }

    public void SetCurrentBuildingMenu(GameObject buildingMenu)
    {
        if (currentBuildingMenu != null)
        {
            currentBuildingMenu.SetActive(false);
        }

        currentBuildingMenu = buildingMenu;

        if (currentBuildingMenu != null)
        {
            currentBuildingMenu.SetActive(true);

            // Get selected building's position
            Vector3 buildingPosition = BuildingSelection.CurrentlySelected.transform.position;

            // Move menu above the building

            float menuHeightOffset = Camera.main.orthographicSize * 0.2f; // Adjust 0.2f as needed
            Vector3 menuPosition = buildingPosition + new Vector3(0, menuHeightOffset, 0);
            currentBuildingMenu.transform.position = menuPosition;

            // Ensure menu faces the camera
            currentBuildingMenu.transform.rotation = Quaternion.Euler(0, 0, 0); // Flat UI facing camera


            SetUpMenuButtons(currentBuildingMenu);
        }
        else
        {
            Debug.LogError("Tried to activate a null building menu.");
        }
    }


    private void SetUpMenuButtons(GameObject buildingMenu)
    {
        Button[] buttons = buildingMenu.GetComponentsInChildren<Button>();
        foreach (Button btn in buttons)
        {
            switch (btn.name)
            {
                case "MoveButton":
                    moveButton = btn;
                    moveButton.onClick.RemoveAllListeners();
                    moveButton.onClick.AddListener(() =>
                    {
                        Debug.Log("Move Button Clicked!");
                        EnableDragging();
                    });
                    break;

                case "UpgradeButton":
                    upgradeButton = btn;
                    upgradeButton.onClick.RemoveAllListeners();
                    upgradeButton.onClick.AddListener(() => UpgradeManager.Instance.StartUpgrade());
                    break;

                case "SpecialButton1":
                    btn.onClick.RemoveAllListeners();
                    btn.onClick.AddListener(SpecialFunction1);
                    break;

                case "SpecialButton2":
                    btn.onClick.RemoveAllListeners();
                    btn.onClick.AddListener(SpecialFunction2);
                    break;

                default:
                    Debug.LogWarning($"Button '{btn.name}' not handled.");
                    break;
            }
        }
    }

    private void EnableDragging()
    {
        Debug.Log("Enable Dragging triggered");

        if (BuildingSelection.CurrentlySelected != null)
        {
            BuildingSelection.CurrentlySelected.EnableDragging();
        }
    }


    private void SpecialFunction1()
    {
        Debug.Log("Special Function 1 triggered");
        // Unique logic for this building
    }

    private void SpecialFunction2()
    {
        Debug.Log("Special Function 2 triggered");
        // Unique logic for another building
    }

    public void HideCurrentMenu()
    {
        if (currentBuildingMenu != null)
        {
            currentBuildingMenu.SetActive(false);
            currentBuildingMenu = null;
        }
    }
}
