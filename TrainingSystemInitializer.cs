using UnityEngine;
using System.Collections;

/// <summary>
/// Ensures that the TrainingManager is properly initialized before any other scripts try to use it.
/// This script should be attached to a GameObject in the scene that loads before any UI elements.
/// </summary>
public class TrainingSystemInitializer : MonoBehaviour
{
    [Header("Prefabs")]
    [SerializeField] private GameObject trainingManagerPrefab;
    
    [Head<PERSON>("Settings")]
    [SerializeField] private bool createTrainingManagerIfMissing = true;
    [SerializeField] private bool dontDestroyOnLoad = true;
    
    private static bool isInitialized = false;
    
    private void Awake()
    {
        if (isInitialized)
        {
            // Already initialized, no need to do it again
            return;
        }
        
        // Check if TrainingManager already exists
        if (TrainingManager.Instance == null && createTrainingManagerIfMissing)
        {
            Debug.Log("TrainingSystemInitializer: Creating TrainingManager instance");
            
            // Create TrainingManager if it doesn't exist
            if (trainingManagerPrefab != null)
            {
                GameObject trainingManagerObj = Instantiate(trainingManagerPrefab);
                trainingManagerObj.name = "TrainingManager";
                
                if (dontDestroyOnLoad)
                {
                    DontDestroyOnLoad(trainingManagerObj);
                }
                
                // Wait a frame to ensure TrainingManager.Awake() is called
                StartCoroutine(VerifyTrainingManagerInitialized());
            }
            else
            {
                // Create a new GameObject with TrainingManager component
                GameObject trainingManagerObj = new GameObject("TrainingManager");
                trainingManagerObj.AddComponent<TrainingManager>();
                
                if (dontDestroyOnLoad)
                {
                    DontDestroyOnLoad(trainingManagerObj);
                }
                
                // Wait a frame to ensure TrainingManager.Awake() is called
                StartCoroutine(VerifyTrainingManagerInitialized());
            }
        }
        else
        {
            Debug.Log("TrainingSystemInitializer: TrainingManager instance already exists");
            isInitialized = true;
        }
    }
    
    private IEnumerator VerifyTrainingManagerInitialized()
    {
        // Wait for the end of the frame to ensure Awake() has been called on the TrainingManager
        yield return new WaitForEndOfFrame();
        
        if (TrainingManager.Instance != null)
        {
            Debug.Log("TrainingSystemInitializer: TrainingManager successfully initialized");
            isInitialized = true;
        }
        else
        {
            Debug.LogError("TrainingSystemInitializer: Failed to initialize TrainingManager");
        }
    }
}
