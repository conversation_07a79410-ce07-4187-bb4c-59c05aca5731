using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using TMPro;
using System;
using System.Collections.Generic;
using System.Linq;


public class HeroSearch : MonoBehaviour
{
    [Header("Search Items")]
    [SerializeField] private TextMeshProUG<PERSON> basicShardCount;
    [SerializeField] private TextMeshP<PERSON><PERSON><PERSON><PERSON> advancedShardCount;
    [SerializeField] private Button basicSearchButton;
    [SerializeField] private Button advancedSearchButton;
    
    [Header("Free Search")]
    [SerializeField] private TextMeshProUGUI basicSearchesLeftText;
    [SerializeField] private TextMeshProUGUI basicSearchCooldownText;
    [SerializeField] private TextMeshP<PERSON><PERSON><PERSON><PERSON> advancedSearchCooldownText;
    
    [Header("UI Elements")]
    [SerializeField] private GameObject rewardsPanel;
    [SerializeField] private SpriteShakeAndScatter basicShakeEffect;
    [SerializeField] private SpriteShakeAndScatter advancedShakeEffect;
    [SerializeField] private Button rewardsPanelCloseButton;
    [Serial<PERSON><PERSON>ield] private But<PERSON> heroSearchCloseButton;
    [SerializeField] private GameObject heroSearch;

    [Header("Rewards UI")]
    [SerializeField] private Image[] rewardSlots; // Array of 4 reward image slots
    [SerializeField] private Button searchAgainButton;
    [SerializeField] private Button searchTenButton;
    [SerializeField] private Button searchAllButton;

    [SerializeField] private RewardPoolSO basicSearchRewardPool;
    [SerializeField] private RewardPoolSO advancedSearchRewardPool;
    private const int MAX_DAILY_BASIC_SEARCHES = 7;
    private const float BASIC_COOLDOWN_MINUTES = 5f;
    private const float ADVANCED_COOLDOWN_HOURS = 48f;
    
    private int remainingBasicSearches;
    private DateTime lastBasicSearchTime;
    private DateTime lastAdvancedSearchTime; 
    private bool isSearchInProgress;


    private void Start()
    {
        Debug.Log($"RewardManager reference: {(RewardManager.Instance != null ? "Found" : "Missing")}");
        LoadSearchData();
        UpdateUI();
        StartCoroutine(UpdateCooldowns());
        
        basicSearchButton.onClick.AddListener(ExecuteBasicSearch);
        advancedSearchButton.onClick.AddListener(ExecuteAdvancedSearch);
        rewardsPanelCloseButton.onClick.AddListener(CloseRewardsPanel);
        heroSearchCloseButton.onClick.AddListener(CloseHeroSearch);
        searchAgainButton.onClick.AddListener(() => ExecuteMultipleSearches(1));
        searchTenButton.onClick.AddListener(() => ExecuteMultipleSearches(10));
        searchAllButton.onClick.AddListener(ExecuteAllSearches);
    }

    private void LoadSearchData()
    {
        // Load saved data or set default values
        string lastBasicSearchStr = PlayerPrefs.GetString("LastBasicSearch", "");
        string lastAdvancedSearchStr = PlayerPrefs.GetString("LastAdvancedSearch", "");
        remainingBasicSearches = PlayerPrefs.GetInt("RemainingBasicSearches", MAX_DAILY_BASIC_SEARCHES);

        if (DateTime.TryParse(lastBasicSearchStr, out lastBasicSearchTime) == false)
            lastBasicSearchTime = DateTime.MinValue;
            
        if (DateTime.TryParse(lastAdvancedSearchStr, out lastAdvancedSearchTime) == false)
            lastAdvancedSearchTime = DateTime.MinValue;

        // Reset daily searches if it's a new day (UTC)
        if (DateTime.UtcNow.Date > lastBasicSearchTime.Date)
        {
            remainingBasicSearches = MAX_DAILY_BASIC_SEARCHES;
            SaveSearchData();
        }
    }

    private void SaveSearchData()
    {
        PlayerPrefs.SetString("LastBasicSearch", lastBasicSearchTime.ToString());
        PlayerPrefs.SetString("LastAdvancedSearch", lastAdvancedSearchTime.ToString());
        PlayerPrefs.SetInt("RemainingBasicSearches", remainingBasicSearches);
        PlayerPrefs.Save();
    }

    private void ExecuteBasicSearch()
    {
        if (isSearchInProgress) return;

        bool canUseFreeSearch = CanUseFreeDailySearch();
        bool hasShards = HasSearchShards("CorruptedShard");

        if (!canUseFreeSearch && !hasShards) return;

        isSearchInProgress = true;
        
        if (!canUseFreeSearch)
        {
            InventorySystem.Instance.RemoveItemByTag("CorruptedShard", 1);
        }
        else
        {
            remainingBasicSearches--;
            lastBasicSearchTime = DateTime.UtcNow;
            SaveSearchData();
        }

        StartCoroutine(ExecuteSearchSequence(false));
    }

    private void ExecuteAdvancedSearch()
    {
        if (isSearchInProgress) return;

        bool canUseFreeSearch = CanUseAdvancedFreeSearch();
        bool hasShards = HasSearchShards("StableShard");

        if (!canUseFreeSearch && !hasShards) return;

        isSearchInProgress = true;

        if (!canUseFreeSearch)
        {
            InventorySystem.Instance.RemoveItemByTag("StableShard", 1);
        }
        else
        {
            lastAdvancedSearchTime = DateTime.UtcNow;
            SaveSearchData();
        }

        StartCoroutine(ExecuteSearchSequence(true));
    }

    private IEnumerator ExecuteSearchSequence(bool isAdvanced)
    {
        Debug.Log("Starting search sequence");

        if (isAdvanced)
            advancedShakeEffect.StartEffect();
        else
            basicShakeEffect.StartEffect();
            
        yield return new WaitForSeconds(2f);

        Debug.Log($"Basic Pool: {(basicSearchRewardPool != null ? "Found" : "Missing")}");
        Debug.Log($"Advanced Pool: {(advancedSearchRewardPool != null ? "Found" : "Missing")}");

        RewardItem[] rewards = GenerateRewards(isAdvanced);
        Debug.Log("Generating rewards");
        ShowRewards(isAdvanced);
        UpdateUI();
        isSearchInProgress = false;
    }

    private bool HasSearchShards(string tag)
    {
        return InventorySystem.Instance.GetItemQuantityByTag(tag) > 0;
    }

    private bool CanUseFreeDailySearch()
    {
        if (remainingBasicSearches <= 0) return false;
        
        TimeSpan timeSinceLastSearch = DateTime.UtcNow - lastBasicSearchTime;
        return timeSinceLastSearch.TotalMinutes >= BASIC_COOLDOWN_MINUTES;
    }

    private bool CanUseAdvancedFreeSearch()
    {
        TimeSpan timeSinceLastSearch = DateTime.UtcNow - lastAdvancedSearchTime;
        return timeSinceLastSearch.TotalHours >= ADVANCED_COOLDOWN_HOURS;
    }

    private IEnumerator UpdateCooldowns()
    {
        while (true)
        {
            TimeSpan basicCooldown = DateTime.UtcNow - lastBasicSearchTime;
            TimeSpan advancedCooldown = DateTime.UtcNow - lastAdvancedSearchTime;

            if (basicCooldown.TotalMinutes < BASIC_COOLDOWN_MINUTES)
            {
                TimeSpan remaining = TimeSpan.FromMinutes(BASIC_COOLDOWN_MINUTES) - basicCooldown;
                basicSearchCooldownText.text = $"Free Search in: {remaining.Minutes:D2}:{remaining.Seconds:D2}";
            }
            else
            {
                basicSearchCooldownText.text = "Search Ready";
            }

            if (advancedCooldown.TotalHours < ADVANCED_COOLDOWN_HOURS)
            {
                TimeSpan remaining = TimeSpan.FromHours(ADVANCED_COOLDOWN_HOURS) - advancedCooldown;
                advancedSearchCooldownText.text = $"Free Search in: {remaining.Hours:D2}:{remaining.Minutes:D2}:{remaining.Seconds:D2}";
            }
            else
            {
                advancedSearchCooldownText.text = "Search Ready";
            }

            yield return new WaitForSeconds(1f);
        }
    }

    private void UpdateUI()
    {
        int basicShards = InventorySystem.Instance.GetItemQuantityByTag("CorruptedShard");
        int advancedShards = InventorySystem.Instance.GetItemQuantityByTag("StableShard");

        basicShardCount.text = basicShards.ToString();
        advancedShardCount.text = advancedShards.ToString();
        basicSearchesLeftText.text = $"Free Searches: {remainingBasicSearches}/{MAX_DAILY_BASIC_SEARCHES}";

        // Buttons are active if there are shards available, regardless of cooldown
        basicSearchButton.interactable = (basicShards > 0) && !isSearchInProgress;
        advancedSearchButton.interactable = (advancedShards > 0) && !isSearchInProgress;
    }

    private void ShowRewards(bool isAdvanced)
    {
        RewardItem[] rewards = GenerateRewards(isAdvanced);
        
        // Display rewards in UI slots
        for (int i = 0; i < rewardSlots.Length; i++)
        {
            rewardSlots[i].sprite = rewards[i].icon;
            // You might want to add UI elements for showing quantity
        }
        
        rewardsPanel.SetActive(true);
        basicShakeEffect.gameObject.SetActive(false);
        advancedShakeEffect.gameObject.SetActive(false);
    }

    public void CloseRewardsPanel()
    {
        rewardsPanel.GetComponent<UIElementAnimator>().HandleDeactivation();
        basicShakeEffect.gameObject.SetActive(true);
        advancedShakeEffect.gameObject.SetActive(true);
        isSearchInProgress = false;
        UpdateUI();
    }

    public void CloseHeroSearch()
    {
        heroSearch.SetActive(false);
    }

    private void ExecuteMultipleSearches(int searchCount)
    {
        if (searchCount == 10)
        {
            searchCount++; // Add bonus pull
        }

        bool isAdvanced = false; // Set based on which search type was last used
        int availableShards = InventorySystem.Instance.GetItemQuantityByTag(isAdvanced ? "StableShard" : "CorruptedShard");
        int actualSearches = Mathf.Min(searchCount, availableShards);

        List<RewardItem[]> allRewards = new List<RewardItem[]>();
        
        for (int i = 0; i < actualSearches; i++)
        {
            RewardItem[] rewards = GenerateRewards(isAdvanced);
            allRewards.Add(rewards);
        }

        DisplayBatchRewards(allRewards);
    }

    private void ExecuteAllSearches()
    {
        bool isAdvanced = false; // Set based on which search type was last used
        int availableShards = InventorySystem.Instance.GetItemQuantityByTag(isAdvanced ? "StableShard" : "CorruptedShard");
        ExecuteMultipleSearches(availableShards);
    }

    private RewardItem[] GenerateRewards(bool isAdvanced)
    {
        Debug.Log("Starting reward generation");
        RewardItem[] rewards = new RewardItem[4];
        for (int i = 0; i < 4; i++)
        {
            string rewardId = GetRandomRewardId(isAdvanced);
            Debug.Log($"Selected reward ID: {rewardId}");
            var reward = RewardManager.Instance.GetReward(rewardId).Result;
            Debug.Log($"Fetched reward: {(reward != null ? reward.Name : "null")}");
            rewards[i] = new RewardItem(reward);
        }
        return rewards;
    }

    private string GetRandomRewardId(bool isAdvanced)
    {
        float roll = UnityEngine.Random.Range(0f, 100f);
        float currentChance = 0f;
        
        var rewardPool = isAdvanced ? advancedSearchRewardPool : basicSearchRewardPool;
        Debug.Log($"Using {(isAdvanced ? "Advanced" : "Basic")} search pool");
        
        foreach (var reward in rewardPool.rewards)
        {
            currentChance += reward.chance;
            if (roll <= currentChance)
            {
                return reward.rewardId;
            }
        }

        return rewardPool.rewards[0].rewardId;
    }



    private void DisplayBatchRewards(List<RewardItem[]> allRewards)
    {
        // Combine and display all rewards
        for (int i = 0; i < rewardSlots.Length; i++)
        {
            RewardItem combinedReward = CombineRewards(allRewards, i);
            rewardSlots[i].sprite = combinedReward.icon;
            // Add any additional UI updates (quantity text, etc.)
        }
    }

    private RewardItem CombineRewards(List<RewardItem[]> allRewards, int slotIndex)
    {
        // Get all rewards for this slot index
        var slotRewards = allRewards.Select(rewards => rewards[slotIndex]);
        
        // Get the first reward's item type
        var firstReward = slotRewards.First();
        
        // Sum up quantities of all rewards of the same type
        int totalQuantity = slotRewards.Sum(r => r.quantity);
        
        // Create new combined reward
        return new RewardItem(firstReward.itemSO, totalQuantity);
    }

}