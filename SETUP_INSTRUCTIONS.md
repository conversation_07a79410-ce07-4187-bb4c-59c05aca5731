# Setup Instructions for GameDataManager

## Fixing Compilation Errors

### 1. Add the partial modifier to GameManager

Open `Assets\Game System\GameManager.cs` and change:
```csharp
public class GameManager : MonoBehaviour
```
to:
```csharp
public partial class GameManager : MonoBehaviour
```

### 2. Add SQLite references to your project

1. Download the SQLite DLLs:
   - Mono.Data.Sqlite.dll
   - System.Data.dll

2. Create a `Plugins` folder in your Assets directory if it doesn't exist
   
3. Copy the DLLs to the `Assets\Plugins` folder

4. In Unity, select the DLLs in the Project view and make sure they are set to be compatible with your target platform in the Inspector

### 3. Use the new implementation

Instead of using the original GameDataManager, use the new GameDataManagerImpl and GameManagerAdapterImpl:

1. Delete or rename these files to avoid conflicts:
   - GameDataManager.cs
   - GameDataManagerSqlite.cs
   - GameManagerAdapter.cs
   - GameManagerExtension.cs

2. Keep these new files:
   - GameDataManagerImpl.cs
   - GameManagerAdapterImpl.cs

3. Attach the GameManagerAdapterImpl component to your GameManager GameObject:
   ```csharp
   // In your scene setup script or editor
   var gameManagerObject = GameObject.Find("GameManager");
   if (!gameManagerObject.GetComponent<GameManagerAdapterImpl>())
   {
       gameManagerObject.AddComponent<GameManagerAdapterImpl>();
   }
   ```

### 4. Fix HeroData conflict

If you have a conflict with HeroData, you need to put one of them in a namespace:

1. Open the HeroData.cs file in the Hero System folder
2. Wrap the class in a namespace:
   ```csharp
   namespace HeroSystem
   {
       public class HeroData : MonoBehaviour
       {
           // Your code here
       }
   }
   ```

3. Update any references to this class to use the namespace:
   ```csharp
   HeroSystem.HeroData heroData = GetComponent<HeroSystem.HeroData>();
   ```

## How It Works

### GameDataManagerImpl

This class handles all data persistence using SQLite. It uses reflection to create the SQLite connection, so it doesn't require direct references to the SQLite DLLs at compile time.

### GameManagerAdapterImpl

This class acts as a bridge between the GameManager and the GameDataManagerImpl. It:

- Initializes the GameDataManagerImpl
- Loads data from the database into the GameManager
- Saves data from the GameManager to the database
- Migrates data from PlayerPrefs to SQLite

## Using the API

### Saving Resources

```csharp
// Get the adapter
var adapter = gameManager.GetComponent<GameManagerAdapterImpl>();

// Save resources
adapter.SaveResourcesToDatabase();
```

### Getting Resources

The GameManager will automatically load resources from the database when the game starts. You can continue to use the GameManager's properties to get resources:

```csharp
int gold = gameManager.Gold;
int food = gameManager.Food;
int wood = gameManager.Wood;
int metal = gameManager.Metal;
```

## Troubleshooting

### SQLite DLL not found

If you get an error about Mono.Data.Sqlite not being found, make sure:

1. The DLLs are in the Plugins folder
2. The DLLs are set to be compatible with your target platform
3. You've restarted Unity after adding the DLLs

### GameManager not found

If the GameManagerAdapterImpl can't find the GameManager, make sure:

1. The GameManagerAdapterImpl is attached to the same GameObject as the GameManager
2. The GameManager class is named exactly "GameManager"

### Database file not found

The database file is created in the Application.persistentDataPath directory. Make sure this directory exists and is writable.

### Migration fails

If migration fails, check the Unity console for error messages. You may need to manually migrate the data.

## Advanced Usage

### Adding Custom Data Types

To add support for custom data types, you'll need to:

1. Add the appropriate tables to the CreateTables method in GameDataManagerImpl
2. Add methods to save and load your custom data
3. Add migration logic if needed

### Backing Up Data

You can add methods to back up and restore the database:

```csharp
public void BackupDatabase(string backupPath)
{
    if (File.Exists(_dbPath))
    {
        File.Copy(_dbPath, backupPath, true);
    }
}

public void RestoreDatabase(string backupPath)
{
    if (File.Exists(backupPath))
    {
        Close(); // Close the connection first
        File.Copy(backupPath, _dbPath, true);
        Initialize(); // Reopen the connection
    }
}
```
