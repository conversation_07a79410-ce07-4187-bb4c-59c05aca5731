using UnityEngine;
using System.Linq;
using System;
using UnityEngine.Video;

namespace HeroSystem
{

[CreateAssetMenu(fileName = "New Hero", menuName = "Hero")]
public class HeroData : ScriptableObject
{
    public string HeroName;
    public string NickName;
    public Sprite HeroThumbnail;
    // public GameObject HeroPrefab; // We can remove or keep this if needed for other purposes
    public VideoClip IntroVideo;     // First video (optional)
    public VideoClip LoopVideo;      // Second video (required)
    public Sprite FallbackImage;     // Fallback image when video playback fails
    public HeroType CurrentHeroType;
    public HeroRarity Rarity;
    public int HeroGeneration;
    public int InitialPower;
    public BaseStats BaseStats;
    public TroopModifiers TroopModifiers;

    private int rank = 1; // Hero rank starts at 1
    public int expLevel = 1; // Initial EXP level
    private int skillPoints = 1; // Skill points start at 1
    private int currentExp = 0; // Current experience points
    private int currentPts = 0; // Current Rank points points

    public bool IsLocked = true; // Indicates if the hero is locked
    public int RequiredFragments = 10; // Number of fragments required to unlock the hero

    public delegate void HeroPowerChangedHandler();
    public event HeroPowerChangedHandler OnHeroPowerChanged;
    public SkillData[] Skills;
    private SkillEffectHandler skillEffectHandler;
    // Removed skillLevels array as we'll use GameManager for this

    private void Awake()
    {
        skillEffectHandler = new SkillEffectHandler();
    }

    private void OnEnable()
    {
        if (skillEffectHandler == null)
        {
            skillEffectHandler = new SkillEffectHandler();
        }
    }

    public void CastSkill(int skillIndex, GameObject target, int currentTurn)
    {
        if (skillIndex >= 0 && skillIndex < Skills.Length)
        {
            skillEffectHandler.ExecuteEffects(Skills[skillIndex], target, currentTurn);
        }
    }

    public bool UpgradeSkill(int skillIndex)
    {
        if (skillIndex < 0 || skillIndex >= Skills.Length)
            return false;

        // Get current level from GameManager
        int currentLevel = GameManager.Instance.GetSkillLevel(HeroName, Skills[skillIndex].skillName);
        
        // Skill 5 (index 4) special condition
        if (skillIndex == 4)
        {
            // Check if all previous skills are level 5
            bool allPreviousMaxed = true;
            for (int i = 0; i < 4; i++)
            {
                if (GameManager.Instance.GetSkillLevel(HeroName, Skills[i].skillName) < 5)
                {
                    allPreviousMaxed = false;
                    break;
                }
            }

            if (currentLevel < 5 && allPreviousMaxed)
            {
                GameManager.Instance.UpdateSkillProgress(HeroName, Skills[skillIndex]);
                OnHeroPowerChanged?.Invoke();
                return true;
            }
            return false;
        }
        
        // Other skills (0-3) rank-based conditions
        if (currentLevel < 5 && (skillIndex == 0 || Rank > skillIndex))
        {
            GameManager.Instance.UpdateSkillProgress(HeroName, Skills[skillIndex]);
            OnHeroPowerChanged?.Invoke();
            return true;
        }
        
        return false;
    }

    public int GetSkillLevel(int skillIndex)
    {
        if (skillIndex < 0 || skillIndex >= Skills.Length)
            return 0;

        // Get level from GameManager
        return GameManager.Instance.GetSkillLevel(HeroName, Skills[skillIndex].skillName);
    }

    // Property to get the current power of the hero
    public int CurrentPower
    {
        get
        {
            return CalculateCurrentPower();
        }
    }

    // Method to upgrade the hero power
    public void UpgradePower(int amount)
    {
        InitialPower += amount;
        OnHeroPowerChanged?.Invoke();
    }

    // Method to upgrade the hero rank
    public void UpgradeRank(int pts)
    {
        currentPts += pts;
        int requiredPts = CalculateRequiredPtsForNextRank();
        while (currentPts >= requiredPts)
        {
            currentPts -= requiredPts;
            rank++;
            requiredPts = CalculateRequiredPtsForNextRank();
        }
        OnHeroPowerChanged?.Invoke();
    }

    // Method to upgrade the hero level
    public void UpgradeExp(int exp)
    {
        currentExp += exp;
        int requiredExp = CalculateRequiredBattleBooks(expLevel + 1);
        while (currentExp >= requiredExp)
        {
            currentExp -= requiredExp;
            expLevel++;
            requiredExp = CalculateRequiredBattleBooks(expLevel + 1);
        }
        OnHeroPowerChanged?.Invoke();
    }


    public int CalculateRequiredBattleBooks(int level)
    {
        return 100 * level * level - 200 * level + 200;
    }

    // Method to calculate the required PTs for the next rank
    public int CalculateRequiredPtsForNextRank()
    {
        return (int)(500 * Math.Pow(3.6, rank - 1));
    }

    // Method to calculate the current power based on the initial power, rank, level, and skill points
    private int CalculateCurrentPower()
    {
        int power = InitialPower;

        switch (Rarity)
        {
            case HeroRarity.Rare:
                power += expLevel * 210;
                power += skillPoints * 2250;
                power += rank * 2420;
                break;
            case HeroRarity.Elite:
                power += expLevel * 240;
                power += skillPoints * 3750;
                power += rank * 5220;
                break;
            case HeroRarity.Legendary:
                power += expLevel * 420;
                power += skillPoints * 7500;
                power += rank * 8620;
                break;
        }

        return power;
    }

    // Method to get the experience required for the next level
    private int GetExpForNextLevel()
    {
        return expLevel * 100; // Example: each level requires 100 * current level exp
    }

    // Properties to get the current rank, level, and skill points
    public int Rank
    {
        get => rank;
        private set => rank = value;
    }
    public int ExpLevel => expLevel;
    public int SkillPoints => skillPoints;
    public int CurrentExp => currentExp;
    public int CurrentPts => currentPts;

    public void LoadProgress(int rank, int expLevel, int currentExp, int currentPts, bool isLocked, int[] skillLevels, bool updateSkills = false)
    {
        this.rank = rank;  // Use field directly
        this.expLevel = expLevel;  // Use field directly
        this.currentExp = currentExp;
        this.currentPts = currentPts;
        this.IsLocked = isLocked;

        // Only apply skill levels, don't trigger updates
        if (!updateSkills)
        {
            for (int i = 0; i < Skills.Length && i < skillLevels.Length; i++)
            {
                // Just store the level, don't trigger GameManager updates
                var skillProgress = GameManager.Instance.GetSkillProgress(HeroName, Skills[i].skillName);
                if (skillProgress != null)
                {
                    skillProgress.level = skillLevels[i];
                }
            }
        }

        OnHeroPowerChanged?.Invoke();
    }
    
}

public enum HeroType { Infantry, Rider, Ranged }
public enum HeroRarity { Rare, Elite, Legendary }

[System.Serializable]
public class BaseStats
{
    public int MarchCapacity;
    public int Attack;
    public int Defense;
    public int HP;
}

[System.Serializable]
public class TroopModifiers
{
    public float AttackBonus;
    public float DefenseBonus;
    public float HpBonus;
}




}