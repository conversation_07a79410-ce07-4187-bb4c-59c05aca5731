using UnityEngine;
using UnityEditor;
using System.IO;
using UnityEngine.UI;

public class TrainingSystemPrefabCreator : EditorWindow
{
    [MenuItem("Tools/Training System/Create Training System Prefabs")]
    public static void ShowWindow()
    {
        GetWindow<TrainingSystemPrefabCreator>("Training System Prefab Creator");
    }

    private void OnGUI()
    {
        GUILayout.Label("Training System Prefab Creator", EditorStyles.boldLabel);

        if (GUILayout.<PERSON><PERSON>("Create Training Manager Prefab"))
        {
            CreateTrainingManagerPrefab();
        }

        if (GUILayout.But<PERSON>("Create Training UI Prefab"))
        {
            CreateTrainingUIPrefab();
        }
    }

    private void CreateTrainingManagerPrefab()
    {
        // Create a new GameObject
        GameObject managerObject = new GameObject("TrainingManager");

        // Add the TrainingManager component
        managerObject.AddComponent<TrainingManager>();

        // Create the prefab
        string prefabPath = "Assets/Training System/Prefabs";
        if (!Directory.Exists(prefabPath))
        {
            Directory.CreateDirectory(prefabPath);
        }

        string assetPath = Path.Combine(prefabPath, "TrainingManager.prefab");
        bool success = false;
        PrefabUtility.SaveAsPrefabAsset(managerObject, assetPath, out success);

        // Destroy the temporary GameObject
        DestroyImmediate(managerObject);

        if (success)
        {
            Debug.Log("Training Manager prefab created successfully at " + assetPath);
        }
        else
        {
            Debug.LogError("Failed to create Training Manager prefab");
        }
    }

    private void CreateTrainingUIPrefab()
    {
        // Create a new GameObject
        GameObject uiObject = new GameObject("TrainingUI");

        // Add a Canvas component
        Canvas canvas = uiObject.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;

        // Add a Canvas Scaler component
        CanvasScaler scaler = uiObject.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);

        // Add a Graphic Raycaster component
        uiObject.AddComponent<UnityEngine.UI.GraphicRaycaster>();

        // Add the TrainingUI component
        uiObject.AddComponent<TrainingUI>();

        // Create the prefab
        string prefabPath = "Assets/Training System/Prefabs";
        if (!Directory.Exists(prefabPath))
        {
            Directory.CreateDirectory(prefabPath);
        }

        string assetPath = Path.Combine(prefabPath, "TrainingUI.prefab");
        bool success = false;
        PrefabUtility.SaveAsPrefabAsset(uiObject, assetPath, out success);

        // Destroy the temporary GameObject
        DestroyImmediate(uiObject);

        if (success)
        {
            Debug.Log("Training UI prefab created successfully at " + assetPath);
        }
        else
        {
            Debug.LogError("Failed to create Training UI prefab");
        }
    }
}
