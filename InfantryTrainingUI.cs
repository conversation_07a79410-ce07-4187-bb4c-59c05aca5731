using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class InfantryTrainingUI : MonoBehaviour
{
    [Header("Main Panels")]
    public GameObject mainPanel;
    public GameObject scrollViewPanel;
    public GameObject troopDetailPanel;
    public GameObject countdownPanel;
    public GameObject speedUpPanel;
    public GameObject completionPanel;
    public GameObject troopStatsPanel;
    public GameObject resourceReplenishmentPanel;

    [Header("Scroll View")]
    public Transform thumbnailContainer;
    public GameObject thumbnailPrefab;

    [Header("Troop Detail")]
    public Image troopImage;
    public TextMeshProUGUI troopNameText;
    public TextMeshProUGUI troopTypeText;
    public TextMeshProUGUI troopLevelText;
    public TextMeshProUGUI troopDescriptionText;
    public TextMeshProUGUI foodRequirementText;
    public TextMeshProUGUI woodRequirementText;
    public TextMeshProUGUI metalRequirementText;
    public TextMeshProUGUI timeRequirementText;
    public TextMeshProUG<PERSON> totalTrainedText;
    public Slider trainingAmountSlider;
    public TMP_InputField trainingAmountInput;
    public Button trainButton;
    public Button upgradeButton;
    public Button statsButton;

    [Header("Countdown")]
    public TextMeshProUGUI countdownText;
    public Button speedUpButton;
    public Button cancelButton;

    [Header("Speed Up")]
    public TextMeshProUGUI requiredTimeText;
    public TextMeshProUGUI availableTimeText;
    public Button confirmSpeedUpButton;
    public Button cancelSpeedUpButton;

    [Header("Completion")]
    public TextMeshProUGUI gainedBPText;

    [Header("Troop Stats")]
    public TextMeshProUGUI attackText;
    public TextMeshProUGUI defenseText;
    public TextMeshProUGUI healthText;
    public Button backFromStatsButton;

    [Header("Resource Replenishment")]
    public TextMeshProUGUI missingResourcesText;
    public Button replenishButton;
    public Button cancelReplenishButton;

    [Header("Background")]
    public Image backgroundImage;
    public Sprite[] infantryBackgrounds;

    [Header("UI Controls")]
    public Button closeButton;

    // Public for access from TroopThumbnail
    public TroopSO selectedTroop;
    private int trainingAmount = 1;
    private TrainingQueueItem currentTrainingItem;
    private Coroutine countdownCoroutine;
    private List<GameObject> thumbnailObjects = new List<GameObject>();

    // Flag to prevent infinite recursion
    private bool isSelectingTroop = false;

    private void Awake()
    {
        Debug.Log("InfantryTrainingUI.Awake() called");

        // Set up button listeners for internal UI functionality
        trainButton.onClick.AddListener(StartTraining);
        upgradeButton.onClick.AddListener(StartUpgrade);
        statsButton.onClick.AddListener(ShowStats);
        backFromStatsButton.onClick.AddListener(HideStats);
        speedUpButton.onClick.AddListener(ShowSpeedUpPanel);
        cancelButton.onClick.AddListener(CancelTraining);
        confirmSpeedUpButton.onClick.AddListener(ConfirmSpeedUp);
        cancelSpeedUpButton.onClick.AddListener(HideSpeedUpPanel);
        replenishButton.onClick.AddListener(ReplenishResources);
        cancelReplenishButton.onClick.AddListener(HideResourceReplenishmentPanel);

        // Set up close button
        if (closeButton != null)
        {
            closeButton.onClick.RemoveAllListeners();
            closeButton.onClick.AddListener(Close);
            Debug.Log("Close button listener set up");
        }

        // Set up slider and input field
        trainingAmountSlider.onValueChanged.AddListener(OnSliderValueChanged);
        trainingAmountInput.onValueChanged.AddListener(OnInputValueChanged);

        // Try to find the Training Manager in the scene
        GameObject trainingManager = GameObject.Find("TrainingManager");
        if (trainingManager != null && !trainingManager.activeSelf)
        {
            trainingManager.SetActive(true);
            Debug.Log("InfantryTrainingUI.Awake: Activated TrainingManager");
        }
        else if (trainingManager == null)
        {
            // Try to find the Training Manager by type
            TrainingManager managerComponent = FindAnyObjectByType<TrainingManager>();
            if (managerComponent != null)
            {
                trainingManager = managerComponent.gameObject;
                trainingManager.SetActive(true);
                Debug.Log($"InfantryTrainingUI.Awake: Found and activated TrainingManager: {trainingManager.name}");
            }
        }

        // Subscribe to training events if TrainingManager instance exists
        if (TrainingManager.Instance != null)
        {
            TrainingManager.Instance.OnTrainingStarted += OnTrainingStarted;
            TrainingManager.Instance.OnTrainingCompleted += OnTrainingCompleted;
            TrainingManager.Instance.OnTrainingCancelled += OnTrainingCancelled;
            TrainingManager.Instance.OnTrainingSpeededUp += OnTrainingSpeededUp;
            Debug.Log("InfantryTrainingUI.Awake: Successfully subscribed to TrainingManager events");
        }
        else
        {
            Debug.LogWarning("TrainingManager.Instance is null in InfantryTrainingUI.Awake(). Will try to subscribe in Start().");
        }

        // Hide the UI initially but don't deactivate the GameObject
        // This allows the component to be found by GetComponent
        HideAllPanels();
    }

    private void Start()
    {
        Debug.Log("InfantryTrainingUI.Start() called");

        // Don't try to use TrainingManager.Instance here
        // We'll wait until Open() is called, which will ensure TrainingManager is active

        // Hide all panels initially
        HideAllPanels();

        // Check if we have all required references
        if (thumbnailPrefab == null)
        {
            Debug.LogError("thumbnailPrefab is not assigned! Please assign it in the Inspector.");
        }

        if (thumbnailContainer == null)
        {
            Debug.LogError("thumbnailContainer is not assigned! Please assign it in the Inspector.");
        }

        if (mainPanel == null || scrollViewPanel == null || troopDetailPanel == null)
        {
            Debug.LogError("One or more required panels are not assigned! Please assign them in the Inspector.");
        }
    }

    private void OnDestroy()
    {
        // Unsubscribe from events
        if (TrainingManager.Instance != null)
        {
            TrainingManager.Instance.OnTrainingStarted -= OnTrainingStarted;
            TrainingManager.Instance.OnTrainingCompleted -= OnTrainingCompleted;
            TrainingManager.Instance.OnTrainingCancelled -= OnTrainingCancelled;
            TrainingManager.Instance.OnTrainingSpeededUp -= OnTrainingSpeededUp;
        }
    }

    private void HideAllPanels()
    {
        mainPanel.SetActive(false);
        scrollViewPanel.SetActive(false);
        troopDetailPanel.SetActive(false);
        countdownPanel.SetActive(false);
        speedUpPanel.SetActive(false);
        completionPanel.SetActive(false);
        troopStatsPanel.SetActive(false);
        resourceReplenishmentPanel.SetActive(false);
    }

    public void Open()
    {
        Debug.Log("Open method called for Infantry troops");

        // Check if we have all required references
        if (thumbnailPrefab == null)
        {
            Debug.LogError("Cannot open UI: thumbnailPrefab is not assigned! Please assign it in the Inspector.");
            return;
        }

        if (thumbnailContainer == null)
        {
            Debug.LogError("Cannot open UI: thumbnailContainer is not assigned! Please assign it in the Inspector.");
            return;
        }

        if (mainPanel == null || scrollViewPanel == null || troopDetailPanel == null)
        {
            Debug.LogError("Cannot open UI: One or more required panels are not assigned! Please assign them in the Inspector.");
            return;
        }

        // Make sure the UI is visible
        gameObject.SetActive(true);

        // Show the main panels
        if (mainPanel != null)
        {
            mainPanel.SetActive(true);
            Debug.Log("InfantryTrainingUI.Open: Activated mainPanel");
        }
        else
        {
            Debug.LogError("InfantryTrainingUI.Open: mainPanel is null! Please assign it in the Inspector.");
        }

        if (scrollViewPanel != null)
        {
            scrollViewPanel.SetActive(true);
            Debug.Log("InfantryTrainingUI.Open: Activated scrollViewPanel");
        }
        else
        {
            Debug.LogError("InfantryTrainingUI.Open: scrollViewPanel is null! Please assign it in the Inspector.");
        }

        if (troopDetailPanel != null)
        {
            troopDetailPanel.SetActive(true);
            Debug.Log("InfantryTrainingUI.Open: Activated troopDetailPanel");
        }
        else
        {
            Debug.LogError("InfantryTrainingUI.Open: troopDetailPanel is null! Please assign it in the Inspector.");
        }

        // Make sure the Training Manager is active
        GameObject trainingManager = GameObject.Find("TrainingManager");
        if (trainingManager != null)
        {
            if (!trainingManager.activeSelf)
            {
                trainingManager.SetActive(true);
                Debug.Log("InfantryTrainingUI.Open: Activated TrainingManager");
            }

            // Wait a frame to ensure TrainingManager.Instance is initialized
            StartCoroutine(InitializeUIDelayed());
        }
        else
        {
            // Try to find the Training Manager by type
            TrainingManager managerComponent = FindAnyObjectByType<TrainingManager>();
            if (managerComponent != null)
            {
                trainingManager = managerComponent.gameObject;
                trainingManager.SetActive(true);
                Debug.Log($"InfantryTrainingUI.Open: Found and activated TrainingManager: {trainingManager.name}");

                // Wait a frame to ensure TrainingManager.Instance is initialized
                StartCoroutine(InitializeUIDelayed());
            }
            else
            {
                Debug.LogError("InfantryTrainingUI.Open: TrainingManager not found in scene! Cannot open UI.");

                // Hide the UI
                HideAllPanels();
                gameObject.SetActive(false);
            }
        }
    }

    private IEnumerator InitializeUIDelayed()
    {
        yield return null; // Wait one frame

        // Check if TrainingManager.Instance is available
        if (TrainingManager.Instance == null)
        {
            Debug.LogError("InfantryTrainingUI.InitializeUIDelayed: TrainingManager.Instance is still null after waiting a frame!");
            yield return null; // Wait one more frame
        }

        // Try again
        if (TrainingManager.Instance == null)
        {
            Debug.LogError("InfantryTrainingUI.InitializeUIDelayed: TrainingManager.Instance is still null after waiting two frames! UI functionality will be limited.");

            // Hide the UI
            HideAllPanels();
            gameObject.SetActive(false);
            yield break;
        }

        // TrainingManager.Instance is available, subscribe to events
        TrainingManager.Instance.OnTrainingStarted -= OnTrainingStarted; // Remove first to avoid duplicates
        TrainingManager.Instance.OnTrainingCompleted -= OnTrainingCompleted;
        TrainingManager.Instance.OnTrainingCancelled -= OnTrainingCancelled;
        TrainingManager.Instance.OnTrainingSpeededUp -= OnTrainingSpeededUp;

        // Subscribe again
        TrainingManager.Instance.OnTrainingStarted += OnTrainingStarted;
        TrainingManager.Instance.OnTrainingCompleted += OnTrainingCompleted;
        TrainingManager.Instance.OnTrainingCancelled += OnTrainingCancelled;
        TrainingManager.Instance.OnTrainingSpeededUp += OnTrainingSpeededUp;

        Debug.Log("InfantryTrainingUI.InitializeUIDelayed: Successfully subscribed to TrainingManager events");

        // Check if there's an active training in progress for infantry troops
        bool hasActiveTraining = false;
        List<TrainingQueueItem> queue = TrainingManager.Instance.GetTrainingQueue();
        Debug.Log($"Found {queue.Count} items in training queue");

        foreach (TrainingQueueItem item in queue)
        {
            if (item != null && item.Troop != null && item.Troop.Type == TroopType.Infantry)
            {
                Debug.Log($"Found active infantry training: {item.Troop.Name} x{item.Count}, Remaining time: {item.RemainingTime}");
                currentTrainingItem = item;
                hasActiveTraining = true;
                break;
            }
        }

        if (hasActiveTraining && currentTrainingItem != null)
        {
            Debug.Log("Active training found, showing countdown panel");

            // Show countdown panel instead of troop detail panel
            if (troopDetailPanel != null)
            {
                troopDetailPanel.SetActive(false);
                Debug.Log("Deactivated troopDetailPanel");
            }

            if (countdownPanel != null)
            {
                countdownPanel.SetActive(true);
                Debug.Log("Activated countdownPanel");

                // Make sure the cancel button is active and interactable
                if (cancelButton != null)
                {
                    cancelButton.gameObject.SetActive(true);
                    cancelButton.interactable = true;
                    Debug.Log("Activated cancel button");
                }

                // Make sure the speed up button is active and interactable
                if (speedUpButton != null)
                {
                    speedUpButton.gameObject.SetActive(true);
                    speedUpButton.interactable = true;
                    Debug.Log("Activated speed up button");
                }

                // Start countdown
                if (countdownCoroutine != null)
                {
                    StopCoroutine(countdownCoroutine);
                }

                countdownCoroutine = StartCoroutine(UpdateCountdown());
                Debug.Log("Started countdown coroutine");
            }
        }
        else
        {
            Debug.Log("No active training found, showing normal UI");

            // Make sure the scroll view panel is active before populating troops
            if (scrollViewPanel != null && !scrollViewPanel.activeSelf)
            {
                scrollViewPanel.SetActive(true);
                Debug.Log("InfantryTrainingUI.InitializeUIDelayed: Activated scrollViewPanel");
            }

            // Make sure the thumbnail container is active
            if (thumbnailContainer != null && !thumbnailContainer.gameObject.activeSelf)
            {
                thumbnailContainer.gameObject.SetActive(true);
                Debug.Log("InfantryTrainingUI.InitializeUIDelayed: Activated thumbnailContainer");
            }

            // Make sure the troop detail panel is active
            if (troopDetailPanel != null && !troopDetailPanel.activeSelf)
            {
                troopDetailPanel.SetActive(true);
                Debug.Log("InfantryTrainingUI.InitializeUIDelayed: Activated troopDetailPanel");
            }

            // Populate troops
            PopulateTroops();
        }

        Debug.Log("Opened Infantry Training UI");
    }

    public void Close()
    {
        Debug.Log("Close method called");

        // Check if there's an active training in progress
        if (currentTrainingItem != null)
        {
            Debug.Log($"Active training in progress: {currentTrainingItem.Troop.Name} x{currentTrainingItem.Count}, Remaining time: {currentTrainingItem.RemainingTime}");

            // Stop the countdown coroutine but keep the currentTrainingItem reference
            if (countdownCoroutine != null)
            {
                StopCoroutine(countdownCoroutine);
                countdownCoroutine = null;
                Debug.Log("Stopped countdown coroutine but preserved currentTrainingItem reference");
            }
        }
        else
        {
            Debug.Log("No active training in progress");
        }

        // Unsubscribe from events to prevent memory leaks
        if (TrainingManager.Instance != null)
        {
            TrainingManager.Instance.OnTrainingStarted -= OnTrainingStarted;
            TrainingManager.Instance.OnTrainingCompleted -= OnTrainingCompleted;
            TrainingManager.Instance.OnTrainingCancelled -= OnTrainingCancelled;
            TrainingManager.Instance.OnTrainingSpeededUp -= OnTrainingSpeededUp;
            Debug.Log("Unsubscribed from TrainingManager events");
        }

        // Hide all panels first
        HideAllPanels();

        // Deactivate the GameObject
        gameObject.SetActive(false);
        Debug.Log("InfantryTrainingUI closed and deactivated");
    }

    private void PopulateTroops()
    {
        Debug.Log("PopulateTroops called");

        // Check if TrainingManager.Instance is available
        if (TrainingManager.Instance == null)
        {
            Debug.LogError("Cannot populate troops: TrainingManager.Instance is null");
            return;
        }

        // Clear existing thumbnails
        foreach (GameObject obj in thumbnailObjects)
        {
            if (obj != null) Destroy(obj);
        }
        thumbnailObjects.Clear();

        // Check if thumbnailPrefab is assigned
        if (thumbnailPrefab == null)
        {
            Debug.LogError("Cannot populate troops: thumbnailPrefab is null");
            return;
        }

        // Check if thumbnailContainer is assigned
        if (thumbnailContainer == null)
        {
            Debug.LogError("Cannot populate troops: thumbnailContainer is null");
            return;
        }

        // Make sure the thumbnailContainer is active
        if (!thumbnailContainer.gameObject.activeSelf)
        {
            Debug.LogWarning("thumbnailContainer is not active! Activating it now.");
            thumbnailContainer.gameObject.SetActive(true);
        }

        // Log the hierarchy path to the thumbnailContainer
        string path = GetGameObjectPath(thumbnailContainer.gameObject);
        Debug.Log($"thumbnailContainer path: {path}");

        // Log the number of children in the thumbnailContainer
        Debug.Log($"thumbnailContainer has {thumbnailContainer.childCount} children before populating");

        // Get infantry troops
        List<TroopSO> troops = null;
        try
        {
            troops = TrainingManager.Instance.GetTroopsForType(TroopType.Infantry);
            Debug.Log($"GetTroopsForType returned {(troops != null ? troops.Count : 0)} infantry troops");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error getting infantry troops: {e.Message}\n{e.StackTrace}");
            return;
        }

        if (troops == null || troops.Count == 0)
        {
            Debug.LogWarning("No infantry troops found. Make sure troops are assigned in the TrainingManager in the Inspector.");
            return;
        }

        Debug.Log($"Found {troops.Count} infantry troops");

        // Log the names and levels of the troops
        foreach (TroopSO troop in troops)
        {
            if (troop != null)
            {
                Debug.Log($"Troop: {troop.Name}, Level: {troop.Level}, Type: {troop.Type}");
            }
        }

        // Create thumbnails
        foreach (TroopSO troop in troops)
        {
            if (troop == null)
            {
                Debug.LogWarning("Skipping null troop");
                continue;
            }

            try
            {
                GameObject thumbnailObj = Instantiate(thumbnailPrefab, thumbnailContainer);
                if (thumbnailObj == null)
                {
                    Debug.LogError("Failed to instantiate thumbnailPrefab!");
                    continue;
                }

                thumbnailObjects.Add(thumbnailObj);

                TroopThumbnail thumbnail = thumbnailObj.GetComponent<TroopThumbnail>();
                if (thumbnail != null)
                {
                    thumbnail.Initialize(troop, null); // Pass null for TrainingUI
                    thumbnail.selectButton.onClick.AddListener(() => SelectTroop(troop));
                    Debug.Log($"Created thumbnail for troop: {troop.Name}, Level: {troop.Level}");
                }
                else
                {
                    Debug.LogError("TroopThumbnail component not found on instantiated prefab!");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error creating thumbnail for troop {troop.Name}: {e.Message}\n{e.StackTrace}");
            }
        }

        // Select the first troop
        if (troops.Count > 0)
        {
            try
            {
                Debug.Log($"Selecting first troop: {troops[0].Name}, Level: {troops[0].Level}");
                SelectTroop(troops[0]);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error selecting troop: {e.Message}\n{e.StackTrace}");
            }
        }
    }

    public void SelectTroop(TroopSO troop)
    {
        // Prevent infinite recursion
        if (isSelectingTroop)
        {
            Debug.Log($"SelectTroop: Preventing recursive call for troop: {(troop != null ? troop.Name : "null")}");
            return;
        }

        isSelectingTroop = true;

        try
        {
            Debug.Log($"SelectTroop called with troop: {(troop != null ? troop.Name : "null")}");

            // Check if troop is null
            if (troop == null)
            {
                Debug.LogError("Cannot select null troop");
                isSelectingTroop = false;
                return;
            }

            // Check if TrainingManager is available
            if (TrainingManager.Instance == null)
            {
                Debug.LogError("Cannot select troop: TrainingManager.Instance is null");
                isSelectingTroop = false;
                return;
            }

            // Check if this is the same troop that's already selected
            if (selectedTroop == troop)
            {
                Debug.Log($"Troop {troop.Name} is already selected, skipping");
                isSelectingTroop = false;
                return;
            }

            selectedTroop = troop;
            Debug.Log($"Selected troop set to: {selectedTroop.Name}");

            // Update troop details
            if (troopImage != null && troop.Image != null)
            {
                troopImage.sprite = troop.Image;
            }
            else if (troopImage != null)
            {
                troopImage.sprite = null;
            }

            if (troopNameText != null)
            {
                troopNameText.text = troop.Name;
            }

            if (troopTypeText != null)
            {
                troopTypeText.text = troop.Type.ToString();
            }

            if (troopLevelText != null)
            {
                troopLevelText.text = $"Level {troop.Level}";
            }

            if (troopDescriptionText != null)
            {
                troopDescriptionText.text = troop.Description;
            }

            // Update requirements
            UpdateResourceTexts();

            // Update total trained
            if (totalTrainedText != null)
            {
                int totalTrained = TrainingManager.Instance.GetTroopCount(troop.Type, troop.Level);
                totalTrainedText.text = $"Total: {totalTrained}";
            }

            // Update slider
            if (trainingAmountSlider != null && trainingAmountInput != null)
            {
                trainingAmountSlider.minValue = 1;
                trainingAmountSlider.maxValue = Mathf.Max(1, TrainingManager.Instance.GetRemainingTrainingCapacity());
                trainingAmountSlider.value = 1;
                trainingAmountInput.text = "1";
                trainingAmount = 1;
            }

            // Update upgrade button
            UpdateUpgradeButton();

            // Update background image
            UpdateBackgroundImage();

            // Update thumbnails
            foreach (GameObject obj in thumbnailObjects)
            {
                if (obj == null) continue;

                TroopThumbnail thumbnail = obj.GetComponent<TroopThumbnail>();
                if (thumbnail != null && thumbnail.Troop != null)
                {
                    bool isSelected = thumbnail.Troop == selectedTroop;
                    if (thumbnail.frameImage != null)
                    {
                        thumbnail.frameImage.gameObject.SetActive(isSelected);
                    }
                }
            }

            // Update stats
            UpdateStatsPanel();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in SelectTroop: {e.Message}\n{e.StackTrace}");
        }
        finally
        {
            // Always reset the flag
            isSelectingTroop = false;
        }
    }

    private void UpdateResourceTexts()
    {
        // Check if GameManager and TrainingManager are available
        if (GameManager.Instance == null || TrainingManager.Instance == null || selectedTroop == null)
        {
            Debug.LogError("Cannot update resource texts: GameManager.Instance or TrainingManager.Instance or selectedTroop is null");

            // Disable train button as a fallback
            if (trainButton != null)
            {
                trainButton.interactable = false;
            }
            return;
        }

        try
        {
            bool hasEnoughFood = GameManager.Instance.HasEnoughFood(selectedTroop.FoodCost * trainingAmount);
            bool hasEnoughWood = GameManager.Instance.HasEnoughWood(selectedTroop.WoodCost * trainingAmount);
            bool hasEnoughMetal = GameManager.Instance.HasEnoughMetal(selectedTroop.MetalCost * trainingAmount);

            foodRequirementText.text = $"{selectedTroop.FoodCost * trainingAmount}";
            woodRequirementText.text = $"{selectedTroop.WoodCost * trainingAmount}";
            metalRequirementText.text = $"{selectedTroop.MetalCost * trainingAmount}";

            // Calculate total training time based on the number of troops (scales linearly)
            float totalTrainingTime = selectedTroop.TrainingTime * trainingAmount;
            timeRequirementText.text = $"{FormatTime(totalTrainingTime)}";

            foodRequirementText.color = hasEnoughFood ? Color.white : Color.red;
            woodRequirementText.color = hasEnoughWood ? Color.white : Color.red;
            metalRequirementText.color = hasEnoughMetal ? Color.white : Color.red;

            // Enable/disable train button
            trainButton.interactable = hasEnoughFood && hasEnoughWood && hasEnoughMetal &&
                                      TrainingManager.Instance.IsTroopUnlocked(selectedTroop) &&
                                      TrainingManager.Instance.GetRemainingTrainingCapacity() >= trainingAmount;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in UpdateResourceTexts: {e.Message}\n{e.StackTrace}");

            // Disable train button as a fallback
            if (trainButton != null)
            {
                trainButton.interactable = false;
            }
        }
    }

    private void UpdateUpgradeButton()
    {
        // Check if TrainingManager is available
        if (TrainingManager.Instance == null || selectedTroop == null)
        {
            Debug.LogError("Cannot update upgrade button: TrainingManager.Instance or selectedTroop is null");

            // Hide upgrade button as a fallback
            if (upgradeButton != null)
            {
                upgradeButton.gameObject.SetActive(false);
            }
            return;
        }

        try
        {
            // Find the next level troop
            List<TroopSO> troops = TrainingManager.Instance.GetTroopsForType(selectedTroop.Type);
            TroopSO nextLevelTroop = null;

            foreach (TroopSO troop in troops)
            {
                if (troop.Level == selectedTroop.Level + 1)
                {
                    nextLevelTroop = troop;
                    break;
                }
            }

            // Show upgrade button only if there's a next level and it's unlocked
            if (nextLevelTroop != null && TrainingManager.Instance.IsTroopUnlocked(nextLevelTroop))
            {
                upgradeButton.gameObject.SetActive(true);
                upgradeButton.interactable = TrainingManager.Instance.GetTroopCount(selectedTroop.Type, selectedTroop.Level) > 0;
            }
            else
            {
                upgradeButton.gameObject.SetActive(false);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in UpdateUpgradeButton: {e.Message}\n{e.StackTrace}");

            // Hide upgrade button as a fallback
            if (upgradeButton != null)
            {
                upgradeButton.gameObject.SetActive(false);
            }
        }
    }

    private void UpdateBackgroundImage()
    {
        try
        {
            // Check if selectedTroop is null
            if (selectedTroop == null || backgroundImage == null)
            {
                return;
            }

            // Check if backgrounds array is valid
            if (infantryBackgrounds == null || infantryBackgrounds.Length == 0)
            {
                backgroundImage.sprite = null;
                return;
            }

            // Select background based on level
            int backgroundIndex;
            if (selectedTroop.Level <= 3)
                backgroundIndex = 0;
            else if (selectedTroop.Level <= 6)
                backgroundIndex = 1;
            else if (selectedTroop.Level <= 9)
                backgroundIndex = 2;
            else
                backgroundIndex = 3;

            // Make sure we don't go out of bounds
            backgroundIndex = Mathf.Min(backgroundIndex, infantryBackgrounds.Length - 1);
            backgroundIndex = Mathf.Max(0, backgroundIndex); // Also ensure it's not negative

            if (backgroundIndex < infantryBackgrounds.Length && infantryBackgrounds[backgroundIndex] != null)
            {
                backgroundImage.sprite = infantryBackgrounds[backgroundIndex];
            }
            else
            {
                backgroundImage.sprite = null;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in UpdateBackgroundImage: {e.Message}\n{e.StackTrace}");

            // Set sprite to null as a last resort
            if (backgroundImage != null)
            {
                backgroundImage.sprite = null;
            }
        }
    }

    private void UpdateStatsPanel()
    {
        if (selectedTroop == null)
        {
            return;
        }

        if (attackText != null)
        {
            attackText.text = $"Attack: {selectedTroop.Attack}";
        }

        if (defenseText != null)
        {
            defenseText.text = $"Defense: {selectedTroop.Defense}";
        }

        if (healthText != null)
        {
            healthText.text = $"Health: {selectedTroop.Health}";
        }
    }

    private void OnSliderValueChanged(float value)
    {
        trainingAmount = Mathf.RoundToInt(value);
        trainingAmountInput.text = trainingAmount.ToString();
        UpdateResourceTexts();
    }

    private void OnInputValueChanged(string value)
    {
        if (int.TryParse(value, out int amount))
        {
            amount = Mathf.Clamp(amount, 1, Mathf.RoundToInt(trainingAmountSlider.maxValue));
            trainingAmount = amount;
            trainingAmountSlider.value = amount;
        }
        UpdateResourceTexts();
    }

    private void StartTraining()
    {
        // Check if TrainingManager and GameManager are available
        if (TrainingManager.Instance == null || GameManager.Instance == null || selectedTroop == null)
        {
            Debug.LogError("Cannot start training: TrainingManager.Instance or GameManager.Instance or selectedTroop is null");
            return;
        }

        try
        {
            if (!TrainingManager.Instance.CanTrainTroop(selectedTroop, trainingAmount))
            {
                // Check if it's due to resources
                bool hasEnoughFood = GameManager.Instance.HasEnoughFood(selectedTroop.FoodCost * trainingAmount);
                bool hasEnoughWood = GameManager.Instance.HasEnoughWood(selectedTroop.WoodCost * trainingAmount);
                bool hasEnoughMetal = GameManager.Instance.HasEnoughMetal(selectedTroop.MetalCost * trainingAmount);

                if (!hasEnoughFood || !hasEnoughWood || !hasEnoughMetal)
                {
                    ShowResourceReplenishmentPanel();
                }
                return;
            }

            TrainingManager.Instance.StartTraining(selectedTroop, trainingAmount);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in StartTraining: {e.Message}\n{e.StackTrace}");
        }
    }

    private void StartUpgrade()
    {
        if (TrainingManager.Instance == null || selectedTroop == null)
        {
            return;
        }

        // Find the next level troop
        List<TroopSO> troops = TrainingManager.Instance.GetTroopsForType(selectedTroop.Type);
        TroopSO nextLevelTroop = null;

        foreach (TroopSO troop in troops)
        {
            if (troop.Level == selectedTroop.Level + 1)
            {
                nextLevelTroop = troop;
                break;
            }
        }

        if (nextLevelTroop != null)
        {
            // Check if we can upgrade
            if (!TrainingManager.Instance.CanUpgradeTroop(selectedTroop, nextLevelTroop, 1))
            {
                // Check if it's due to resources
                float upgradeCostFactor = TrainingManager.Instance.CalculateUpgradeCost(selectedTroop, nextLevelTroop) / 100f;
                int foodCost = Mathf.RoundToInt(nextLevelTroop.FoodCost * upgradeCostFactor);
                int woodCost = Mathf.RoundToInt(nextLevelTroop.WoodCost * upgradeCostFactor);
                int metalCost = Mathf.RoundToInt(nextLevelTroop.MetalCost * upgradeCostFactor);

                bool hasEnoughFood = GameManager.Instance.HasEnoughFood(foodCost);
                bool hasEnoughWood = GameManager.Instance.HasEnoughWood(woodCost);
                bool hasEnoughMetal = GameManager.Instance.HasEnoughMetal(metalCost);

                if (!hasEnoughFood || !hasEnoughWood || !hasEnoughMetal)
                {
                    ShowResourceReplenishmentPanel();
                }
                return;
            }

            TrainingManager.Instance.UpgradeTroop(selectedTroop, nextLevelTroop, 1);

            // Update UI
            SelectTroop(nextLevelTroop);
        }
    }

    private void ShowStats()
    {
        troopDetailPanel.SetActive(false);
        troopStatsPanel.SetActive(true);
    }

    private void HideStats()
    {
        troopStatsPanel.SetActive(false);
        troopDetailPanel.SetActive(true);
    }

    private void ShowSpeedUpPanel()
    {
        speedUpPanel.SetActive(true);
    }

    private void HideSpeedUpPanel()
    {
        speedUpPanel.SetActive(false);
    }

    private void ShowResourceReplenishmentPanel()
    {
        resourceReplenishmentPanel.SetActive(true);
    }

    private void HideResourceReplenishmentPanel()
    {
        resourceReplenishmentPanel.SetActive(false);
    }

    private void ReplenishResources()
    {
        // TODO: Implement resource replenishment from inventory
        HideResourceReplenishmentPanel();
    }

    private void ConfirmSpeedUp()
    {
        // TODO: Implement speed up logic
        HideSpeedUpPanel();
    }

    private void OnTrainingStarted(TroopSO troop, int count, float time)
    {
        Debug.Log($"OnTrainingStarted called for {troop.Name}, count: {count}, time: {time}");

        // Only handle infantry troops
        if (troop.Type != TroopType.Infantry)
        {
            Debug.Log($"Ignoring non-infantry troop: {troop.Name}, Type: {troop.Type}");
            return;
        }

        // Make sure the GameObject is active
        if (!gameObject.activeSelf)
        {
            Debug.Log("GameObject is inactive, activating it");
            gameObject.SetActive(true);
        }

        // Find the training item
        List<TrainingQueueItem> queue = TrainingManager.Instance.GetTrainingQueue();
        Debug.Log($"Training queue has {queue.Count} items");

        foreach (TrainingQueueItem item in queue)
        {
            if (item.Troop == troop && item.Count == count)
            {
                currentTrainingItem = item;
                Debug.Log($"Found training item for {troop.Name}, count: {count}, remaining time: {item.RemainingTime}");
                break;
            }
        }

        if (currentTrainingItem == null)
        {
            Debug.LogWarning($"Could not find training item for {troop.Name}, count: {count}");
            return;
        }

        // Show countdown panel
        if (troopDetailPanel != null)
        {
            troopDetailPanel.SetActive(false);
            Debug.Log("Deactivated troopDetailPanel");
        }

        if (countdownPanel != null)
        {
            countdownPanel.SetActive(true);
            Debug.Log("Activated countdownPanel");

            // Make sure the cancel button is active and interactable
            if (cancelButton != null)
            {
                cancelButton.gameObject.SetActive(true);
                cancelButton.interactable = true;
                Debug.Log("Activated cancel button");
            }
            else
            {
                Debug.LogError("cancelButton is null!");
            }

            // Make sure the speed up button is active and interactable
            if (speedUpButton != null)
            {
                speedUpButton.gameObject.SetActive(true);
                speedUpButton.interactable = true;
                Debug.Log("Activated speed up button");
            }
            else
            {
                Debug.LogError("speedUpButton is null!");
            }
        }
        else
        {
            Debug.LogError("countdownPanel is null!");
            return;
        }

        // Start countdown
        try
        {
            if (countdownCoroutine != null)
            {
                StopCoroutine(countdownCoroutine);
                Debug.Log("Stopped existing countdown coroutine");
            }

            countdownCoroutine = StartCoroutine(UpdateCountdown());
            Debug.Log("Started countdown coroutine");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error starting countdown coroutine: {e.Message}\n{e.StackTrace}");
        }
    }

    private void OnTrainingCompleted(TroopSO troop, int count)
    {
        Debug.Log($"OnTrainingCompleted called for {troop.Name}, count: {count}");

        // Only handle infantry troops
        if (troop.Type != TroopType.Infantry)
        {
            Debug.Log($"Ignoring non-infantry troop: {troop.Name}, Type: {troop.Type}");
            return;
        }

        // Stop countdown
        if (countdownCoroutine != null)
        {
            StopCoroutine(countdownCoroutine);
            countdownCoroutine = null;
            Debug.Log("Stopped countdown coroutine");
        }

        // Hide countdown panel
        countdownPanel.SetActive(false);
        Debug.Log("Deactivated countdownPanel");

        // Deactivate cancel button
        if (cancelButton != null)
        {
            cancelButton.gameObject.SetActive(false);
            Debug.Log("Deactivated cancel button");
        }

        // Deactivate speed up button
        if (speedUpButton != null)
        {
            speedUpButton.gameObject.SetActive(false);
            Debug.Log("Deactivated speed up button");
        }

        // Show completion panel
        completionPanel.SetActive(true);
        gainedBPText.text = $"+{troop.BattlePower * count} Battle Power";
        Debug.Log("Activated completion panel");

        // Hide completion panel after 2 seconds
        StartCoroutine(HideCompletionPanel());
        Debug.Log("Started HideCompletionPanel coroutine");

        // Update UI
        UpdateResourceTexts();
        UpdateUpgradeButton();
        int totalTrained = TrainingManager.Instance.GetTroopCount(troop.Type, troop.Level);
        totalTrainedText.text = $"Total: {totalTrained}";
        Debug.Log("Updated UI elements");

        // Reset current training item
        currentTrainingItem = null;
        Debug.Log("Reset currentTrainingItem to null");
    }

    private void OnTrainingCancelled(TroopSO troop)
    {
        Debug.Log($"OnTrainingCancelled called for {troop.Name}");

        // Only handle infantry troops
        if (troop.Type != TroopType.Infantry)
        {
            Debug.Log($"Ignoring non-infantry troop: {troop.Name}, Type: {troop.Type}");
            return;
        }

        // Stop countdown
        if (countdownCoroutine != null)
        {
            StopCoroutine(countdownCoroutine);
            countdownCoroutine = null;
            Debug.Log("Stopped countdown coroutine");
        }

        // Hide countdown panel
        countdownPanel.SetActive(false);
        Debug.Log("Deactivated countdownPanel");

        // Deactivate cancel button
        if (cancelButton != null)
        {
            cancelButton.gameObject.SetActive(false);
            Debug.Log("Deactivated cancel button");
        }

        // Deactivate speed up button
        if (speedUpButton != null)
        {
            speedUpButton.gameObject.SetActive(false);
            Debug.Log("Deactivated speed up button");
        }

        // Show troop detail panel
        troopDetailPanel.SetActive(true);
        Debug.Log("Activated troopDetailPanel");

        // Update UI
        UpdateResourceTexts();
        Debug.Log("Updated resource texts");

        // Reset current training item
        currentTrainingItem = null;
        Debug.Log("Reset currentTrainingItem to null");
    }

    private void OnTrainingSpeededUp(TroopSO troop, float minutes)
    {
        Debug.Log($"OnTrainingSpeededUp called for {troop.Name}, minutes: {minutes}");

        // Only handle infantry troops
        if (troop.Type != TroopType.Infantry)
        {
            Debug.Log($"Ignoring non-infantry troop: {troop.Name}, Type: {troop.Type}");
            return;
        }

        // Hide speed up panel
        speedUpPanel.SetActive(false);
        Debug.Log("Deactivated speedUpPanel");

        // Update countdown
        if (currentTrainingItem != null)
        {
            Debug.Log($"Current training item remaining time: {currentTrainingItem.RemainingTime}");

            if (currentTrainingItem.RemainingTime <= 0)
            {
                // Training is complete
                countdownPanel.SetActive(false);
                Debug.Log("Deactivated countdownPanel because training is complete");

                // Deactivate cancel button
                if (cancelButton != null)
                {
                    cancelButton.gameObject.SetActive(false);
                    Debug.Log("Deactivated cancel button");
                }

                // Deactivate speed up button
                if (speedUpButton != null)
                {
                    speedUpButton.gameObject.SetActive(false);
                    Debug.Log("Deactivated speed up button");
                }
            }
            else
            {
                Debug.Log("Training is still in progress after speed up");
            }
        }
        else
        {
            Debug.LogWarning("currentTrainingItem is null in OnTrainingSpeededUp");
        }
    }

    private IEnumerator UpdateCountdown()
    {
        Debug.Log("UpdateCountdown coroutine started");

        if (currentTrainingItem == null)
        {
            Debug.LogError("UpdateCountdown: currentTrainingItem is null!");
            yield break;
        }

        Debug.Log($"Initial remaining time: {currentTrainingItem.RemainingTime} seconds");

        // Make sure the countdown panel is active
        if (countdownPanel != null && !countdownPanel.activeSelf)
        {
            countdownPanel.SetActive(true);
            Debug.Log("Activated countdownPanel in UpdateCountdown");
        }

        // Make sure the troop detail panel is inactive
        if (troopDetailPanel != null && troopDetailPanel.activeSelf)
        {
            troopDetailPanel.SetActive(false);
            Debug.Log("Deactivated troopDetailPanel in UpdateCountdown");
        }

        // Make sure the cancel button is active and interactable
        if (cancelButton != null)
        {
            cancelButton.gameObject.SetActive(true);
            cancelButton.interactable = true;
            Debug.Log("Activated cancel button in UpdateCountdown");
        }

        // Make sure the speed up button is active and interactable
        if (speedUpButton != null)
        {
            speedUpButton.gameObject.SetActive(true);
            speedUpButton.interactable = true;
            Debug.Log("Activated speed up button in UpdateCountdown");
        }

        // Update the countdown text immediately
        if (countdownText != null)
        {
            countdownText.text = $"{FormatTime(currentTrainingItem.RemainingTime)}";
            Debug.Log($"Initial countdown text: {countdownText.text}, Remaining time: {currentTrainingItem.RemainingTime}");
        }

        while (true)
        {
            // Check if the training item still exists
            if (currentTrainingItem == null)
            {
                Debug.Log("Training item no longer exists, ending countdown");
                break;
            }

            // Check if the training is complete
            if (currentTrainingItem.RemainingTime <= 0)
            {
                Debug.Log("Training is complete, ending countdown");
                break;
            }

            // Check if the UI is still active
            if (!gameObject.activeSelf)
            {
                Debug.Log("UI is no longer active, pausing countdown");
                yield break;
            }

            // Update the countdown text
            if (countdownText != null)
            {
                countdownText.text = $"{FormatTime(currentTrainingItem.RemainingTime)}";
                Debug.Log($"Updated countdown text: {countdownText.text}, Remaining time: {currentTrainingItem.RemainingTime}");
            }
            else
            {
                Debug.LogError("UpdateCountdown: countdownText is null!");
            }

            // Wait for 1 second
            yield return new WaitForSeconds(1f);

            // The TrainingManager should be updating the remaining time in its own coroutine
            // We don't need to update it here
        }

        Debug.Log("UpdateCountdown coroutine finished");
    }

    private IEnumerator HideCompletionPanel()
    {
        yield return new WaitForSeconds(2f);
        completionPanel.SetActive(false);
        troopDetailPanel.SetActive(true);
    }

    private void CancelTraining()
    {
        if (currentTrainingItem != null)
        {
            TrainingManager.Instance.CancelTraining(currentTrainingItem);
            currentTrainingItem = null;
        }
    }

    private string FormatTime(float seconds)
    {
        int minutes = Mathf.FloorToInt(seconds / 60);
        int hours = Mathf.FloorToInt(minutes / 60);
        minutes %= 60;
        int secs = Mathf.FloorToInt(seconds % 60);

        if (hours > 0)
        {
            return $"{hours}h {minutes}m {secs}s";
        }
        else if (minutes > 0)
        {
            return $"{minutes}m {secs}s";
        }
        else
        {
            return $"{secs}s";
        }
    }

    // Helper method to get the full path of a GameObject in the hierarchy
    private string GetGameObjectPath(GameObject obj)
    {
        string path = obj.name;
        Transform parent = obj.transform.parent;

        while (parent != null)
        {
            path = parent.name + "/" + path;
            parent = parent.parent;
        }

        return path;
    }
}
