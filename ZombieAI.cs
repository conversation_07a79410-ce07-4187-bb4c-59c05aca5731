using UnityEngine;
using UnityEngine.AI;

[RequireComponent(typeof(NavMeshAgent))]
public class ZombieAI : MonoBehaviour
{
    private NavMeshAgent agent;
    private Transform player;
    private ZombieStats stats;
    private ZombieType zombieType;
    
    private float currentHealth;
    private bool isDead = false;

    [Header("Movement Settings")]
    [SerializeField] private float updatePathInterval = 0.2f;
    
    [Header("Attack Settings")]
    [SerializeField] private float attackRange = 2f;
    [SerializeField] private float attackCooldown = 1f;
    private float nextAttackTime;

    [Header("Animation")]
    [SerializeField] private Animator animator;
    private static readonly int AttackTrigger = Animator.StringToHash("AttackTrigger");
    private static readonly int DeathTrigger = Animator.StringToHash("DeathTrigger");

    private void Awake()
    {
        agent = GetComponent<NavMeshAgent>();
        animator = GetComponent<Animator>();
        
        if (animator == null)
        {
            animator = GetComponentInChildren<Animator>();
        }
    }

    public void Initialize(ZombieStats stats, ZombieType type)
    {
        this.stats = stats;
        this.zombieType = type;
        currentHealth = stats.maxHealth;

        // Find player
        GameObject playerObj = GameObject.FindGameObjectWithTag("FPS");
        if (playerObj != null)
        {
            player = playerObj.transform;
            StartCoroutine(UpdatePathRoutine()); // Only start if player is found
        }
        else
        {
            Debug.LogWarning("Player not found! Zombie will be inactive.");
        }

        // Set agent speed based on zombie type
        if (agent != null)
        {
            switch (type)
            {
                case ZombieType.Fast:
                    agent.speed = 7f;
                    agent.acceleration = 12f;
                    break;
                case ZombieType.Boss:
                    agent.speed = 4f;
                    agent.acceleration = 8f;
                    attackRange = 3f;
                    break;
                default: // Slow zombie
                    agent.speed = 3.5f;
                    agent.acceleration = 6f;
                    break;
            }
        }
    }

    private System.Collections.IEnumerator UpdatePathRoutine()
    {
        while (!isDead && player != null)
        {
            UpdatePath();
            yield return new WaitForSeconds(updatePathInterval);
        }
    }

    private void UpdatePath()
    {
        if (player != null && agent.isOnNavMesh)
        {
            agent.SetDestination(player.position);
        }
    }

    private void Update()
    {
        if (isDead || player == null) return;

        // Check if within attack range
        if (Vector3.Distance(transform.position, player.position) <= attackRange)
        {
            TryAttack();
        }
    }

    private void TryAttack()
    {
        if (Time.time >= nextAttackTime)
        {
            // Play attack animation
            if (animator != null)
            {
                animator.SetTrigger(AttackTrigger);
            }

            // Perform attack
            Debug.Log($"Zombie attacking! Damage: {stats.attack}");
            
            nextAttackTime = Time.time + attackCooldown;
        }
    }

    public void TakeDamage(float damage)
    {
        if (isDead) return;

        float actualDamage = Mathf.Max(1, damage - stats.defense);
        currentHealth -= actualDamage;

        if (currentHealth <= 0)
        {
            Die();
        }
    }

    private void Die()
    {
        isDead = true;
        agent.isStopped = true;

        // Play death animation if animator exists
        if (animator != null)
        {
            try
            {
                animator.SetTrigger(DeathTrigger);
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to set animation parameters: {e.Message}");
            }
        }

        // Notify spawner
        ZombieSpawner spawner = FindFirstObjectByType<ZombieSpawner>();
        if (spawner != null)
        {
            spawner.OnZombieDeath(zombieType);
        }

        // Get animation length for proper timing
        float destroyDelay = 2f;
        if (animator != null)
        {
            RuntimeAnimatorController rac = animator.runtimeAnimatorController;
            foreach (AnimationClip clip in rac.animationClips)
            {
                if (clip.name.Contains("Death"))
                {
                    destroyDelay = clip.length;
                    break;
                }
            }
        }

        // Destroy after animation completes
        Destroy(gameObject, destroyDelay);
    }

    // Optional: Visualize attack range in editor
    private void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);
    }
}
