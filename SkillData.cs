using UnityEngine;
using System;
using System.Linq;

[CreateAssetMenu(fileName = "New Skill", menuName = "Skills/Skill Data")]
public class SkillData : ScriptableObject
{
    public string skillName;
    public Sprite skillIcon;
    public string description;           // Skill description
    public string secondaryDescription;  // Secondary description if needed
     
    // Base stats
    public float attackBonus;
    public float damageBonus;
    public float defenseBonus;
    public float healthBonus;
    public float marchCapacityBonus;
    public float rallyCapacityBonus;
    public SkillEffect[] skillEffects;
}

[System.Serializable]
public class SkillEffect
{
    public SkillEffectType effectType;
    public int startTurn;
    public int turns;
    public float effectChance;
    public float effectValue;
    public TargetType targetType;
}
