using UnityEngine;

public class GridManager : MonoBehaviour
{
    public int gridSizeX = 20; // Width of the grid
    public int gridSizeY = 20; // Height of the grid
    public float cellSize = 1f; // Size of each grid cell
    private bool[,] grid; // 2D array to store grid occupancy

    void Start()
    {
        InitializeGrid();
    }

    void InitializeGrid()
    {
        grid = new bool[gridSizeX, gridSizeY]; // Initialize grid with false (unoccupied)
    }

    public bool IsCellOccupied(int x, int y)
    {
        if (x < 0 || x >= gridSizeX || y < 0 || y >= gridSizeY)
            return true; // Out of bounds is considered occupied
        return grid[x, y];
    }

    public void OccupyCells(int x, int y, int width, int height)
    {
        for (int i = x; i < x + width; i++)
        {
            for (int j = y; j < y + height; j++)
            {
                if (i >= 0 && i < gridSizeX && j >= 0 && j < gridSizeY)
                {
                    grid[i, j] = true;
                }
            }
        }
    }

    public void FreeCells(int x, int y, int width, int height)
    {
        for (int i = x; i < x + width; i++)
        {
            for (int j = y; j < y + height; j++)
            {
                if (i >= 0 && i < gridSizeX && j >= 0 && j < gridSizeY)
                {
                    grid[i, j] = false;
                }
            }
        }
    }

    public Vector3 GetWorldPosition(int x, int y)
    {
        float gridHeightOffset = 0.1f; // Adjust this value if needed
        return new Vector3(x * cellSize, gridHeightOffset, y * cellSize);
    }

    public void DrawDebugGrid()
    {
        for (int x = 0; x < gridSizeX; x++)
        {
            for (int y = 0; y < gridSizeY; y++)
            {
                Debug.DrawLine(GetWorldPosition(x, y) + Vector3.up * 0.5f, GetWorldPosition(x, y + 1) + Vector3.up * 0.5f, Color.gray);
                Debug.DrawLine(GetWorldPosition(x, y) + Vector3.up * 0.5f, GetWorldPosition(x + 1, y) + Vector3.up * 0.5f, Color.gray);
            }
        }
    }
}