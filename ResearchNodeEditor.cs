using UnityEditor;
using UnityEngine;
using System.Collections.Generic;


[CustomEditor(typeof(ResearchNode))]
public class ResearchNodeEditor : Editor
{
    private Dictionary<ResearchNode.NodeType, ResearchNode.BonusType[]> nodeBonusMapping;
    private bool showTiersFoldout = true;
    private SerializedProperty tiersProperty;

    private void OnEnable()
    {
        nodeBonusMapping = new Dictionary<ResearchNode.NodeType, ResearchNode.BonusType[]> 
        { 
            { ResearchNode.NodeType.Dominion, new ResearchNode.BonusType[] { ResearchNode.BonusType.BuildingEfficiency, ResearchNode.BonusType.ResearchFundamentals, ResearchNode.BonusType.BasicTraining, ResearchNode.BonusType.Recruitment, ResearchNode.BonusType.FirstAid, ResearchNode.BonusType.EmergencyWard, ResearchNode.BonusType.StorageOptimization, ResearchNode.BonusType.WelfareGrowth, ResearchNode.BonusType.InfrastructureOptimization, ResearchNode.BonusType.Automation, ResearchNode.BonusType.TacticalManeuvers, ResearchNode.BonusType.DynamicEndurance } },
            { ResearchNode.NodeType.Prosperity, new ResearchNode.BonusType[] { ResearchNode.BonusType.BasicFarming, ResearchNode.BonusType.Lumbering, ResearchNode.BonusType.Mining, ResearchNode.BonusType.PowerGeneration, ResearchNode.BonusType.TradingBasics, ResearchNode.BonusType.Scavenging, ResearchNode.BonusType.ResourceGathering, ResearchNode.BonusType.PowerGrid, ResearchNode.BonusType.Recycling, ResearchNode.BonusType.MarketManipulation, ResearchNode.BonusType.ResourceTransportation, ResearchNode.BonusType.Terraforming } },
            { ResearchNode.NodeType.Supermacy, new ResearchNode.BonusType[] { ResearchNode.BonusType.InfantryTraining, ResearchNode.BonusType.RiderTraining, ResearchNode.BonusType.RangedTraining, ResearchNode.BonusType.InfantryIndurance, ResearchNode.BonusType.RiderIndurance, ResearchNode.BonusType.RangedIndurance,  ResearchNode.BonusType.SquadCapacity,  ResearchNode.BonusType.SquadTactics, ResearchNode.BonusType.BasicArmor, ResearchNode.BonusType.WeaponCrafting, ResearchNode.BonusType.MedicalTraining, ResearchNode.BonusType.Scouting, ResearchNode.BonusType.AmmunitionCrafting, ResearchNode.BonusType.SiegeTactics, ResearchNode.BonusType.CounterIntelligence, ResearchNode.BonusType.AdvancedCamouflage } }
        };
        
        tiersProperty = serializedObject.FindProperty("tiers");
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        ResearchNode researchNode = (ResearchNode)target;

        // Node Type dropdown
        EditorGUI.BeginChangeCheck();
        researchNode.nodeType = (ResearchNode.NodeType)EditorGUILayout.EnumPopup("Node Type", researchNode.nodeType);

        // Filtered Bonus Type dropdown
        if (nodeBonusMapping.TryGetValue(researchNode.nodeType, out ResearchNode.BonusType[] availableBonuses))
        {
            int selectedIndex = Mathf.Max(0, System.Array.IndexOf(availableBonuses, researchNode.bonusType));
            string[] displayNames = System.Array.ConvertAll(availableBonuses, b => GetInspectorName(b));
            selectedIndex = EditorGUILayout.Popup("Bonus Type", selectedIndex, displayNames);
            researchNode.bonusType = availableBonuses[selectedIndex];
        }

        // Add the percentage bonus toggle right after the bonus type
        EditorGUILayout.PropertyField(serializedObject.FindProperty("isPercentageBonus"), 
            new GUIContent("Is Percentage Bonus", "Check if this bonus should be displayed as a percentage"));

        EditorGUILayout.Space(5);

        // Base Values
        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("Base Values", EditorStyles.boldLabel);
        
        EditorGUILayout.LabelField("Starting Costs", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("startingFoodCost"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("startingWoodCost"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("startingMetalCost"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("startingGoldCost"), new GUIContent("Starting Gold Cost (Instant)"));
        
        EditorGUILayout.Space(5);
        EditorGUILayout.LabelField("Starting Time & Power", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("startingTime"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("startingPower"));

        // Tiers Management
        EditorGUILayout.Space(10);
        showTiersFoldout = EditorGUILayout.Foldout(showTiersFoldout, "Research Tiers", true);
        if (showTiersFoldout)
        {
            EditorGUI.indentLevel++;

            // Add Tier Button
            if (GUILayout.Button("Add New Tier"))
            {
                tiersProperty.arraySize++;
                var newTier = tiersProperty.GetArrayElementAtIndex(tiersProperty.arraySize - 1);
                newTier.FindPropertyRelative("tierNumber").intValue = tiersProperty.arraySize;
            }

            // Display existing tiers
            for (int i = 0; i < tiersProperty.arraySize; i++)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"Tier {i + 1}", EditorStyles.boldLabel);
                
                if (GUILayout.Button("Remove", GUILayout.Width(60)))
                {
                    tiersProperty.DeleteArrayElementAtIndex(i);
                    break;
                }
                EditorGUILayout.EndHorizontal();

                var tierProperty = tiersProperty.GetArrayElementAtIndex(i);

                EditorGUILayout.Space(5);
                EditorGUILayout.LabelField("Prerequisites", EditorStyles.boldLabel);

                SerializedProperty prerequisitesProperty = tierProperty.FindPropertyRelative("prerequisites");
                EditorGUILayout.PropertyField(prerequisitesProperty, true);
                EditorGUILayout.PropertyField(tierProperty.FindPropertyRelative("requiredLabLevel"));

                EditorGUILayout.Space(5);
                EditorGUILayout.LabelField("Information", EditorStyles.boldLabel);
                EditorGUILayout.PropertyField(tierProperty.FindPropertyRelative("nodeName"));
                EditorGUILayout.PropertyField(tierProperty.FindPropertyRelative("description"));
                EditorGUILayout.PropertyField(tierProperty.FindPropertyRelative("icon"));

                EditorGUILayout.Space(5);
                EditorGUILayout.LabelField("Leveling", EditorStyles.boldLabel);
                EditorGUILayout.PropertyField(tierProperty.FindPropertyRelative("maxLevel"));
                
                EditorGUILayout.Space(5);
                EditorGUILayout.LabelField("Stats", EditorStyles.boldLabel);
                EditorGUILayout.PropertyField(tierProperty.FindPropertyRelative("bonus"));

                EditorGUILayout.EndVertical();
                EditorGUILayout.Space(10);
            }

            EditorGUI.indentLevel--;
        }

        serializedObject.ApplyModifiedProperties();
        EditorUtility.SetDirty(researchNode);
    }

    // Retrieves the Inspector-friendly name from the enum
    private string GetInspectorName(ResearchNode.BonusType bonus)
    {
        var field = bonus.GetType().GetField(bonus.ToString());
        var attr = (InspectorNameAttribute)System.Attribute.GetCustomAttribute(field, typeof(InspectorNameAttribute));
        return attr != null ? attr.displayName : bonus.ToString();
    }
}
