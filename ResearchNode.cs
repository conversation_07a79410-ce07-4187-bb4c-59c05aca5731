using UnityEngine;
using System.Collections.Generic;


[CreateAssetMenu(fileName = "New Research Node", menuName = "Research/Research Node")]
public class ResearchNode : ScriptableObject
{

    public NodeType nodeType;
    public BonusType bonusType;
    public bool isPercentageBonus;

    [Header("Base Costs")]
    public int startingFoodCost;
    public int startingWoodCost;
    public int startingMetalCost;
    public int startingGoldCost;
    
    [<PERSON>er("Base Time & Power")]
    public float startingTime;
    public int startingPower;

    [Header("Tier Data")]
    public List<ResearchTierData> tiers = new List<ResearchTierData>();

    // Constants
    private const int SCALING_FACTOR = 3130;

    // Calculate resource cost for a specific tier and level
    public int CalculateCost(int tierIndex, int level, int startingCost)
    {
        if (level <= 1) return startingCost;
        
        int cost = startingCost;
        for (int n = 2; n <= level; n++)
        {
            cost += SCALING_FACTOR * (n + tierIndex);
        }
        return cost;
    }

    public int GetGoldCost(int tierIndex, int level)
    {
        if (tierIndex >= tiers.Count) return 0;  // Changed Length to Count
        return Mathf.RoundToInt(startingGoldCost * Mathf.Pow(1.3f, level - 1));
    }

    // Helper methods for specific resources
    public int GetFoodCost(int tierIndex, int level)
    {
        return CalculateCost(tierIndex, level, startingFoodCost);
    }

    public int GetWoodCost(int tierIndex, int level)
    {
        return CalculateCost(tierIndex, level, startingWoodCost);
    }

    public int GetMetalCost(int tierIndex, int level)
    {
        return CalculateCost(tierIndex, level, startingMetalCost);
    }

    public float GetTime(int tierIndex, int level)
    {
        if (level <= 1) return startingTime;
        
        float time = startingTime;
        for (int n = 2; n <= level; n++)
        {
            time += SCALING_FACTOR * (n + tierIndex - 1);
        }
        return time;
    }

    public int GetPower(int tierIndex, int level)
    {
        if (tierIndex >= tiers.Count) return 0;  // Changed Length to Count
        return Mathf.RoundToInt(startingPower * Mathf.Pow(1.3f, level - 1));
    }

    public enum NodeType { Dominion, Prosperity, Supermacy }
    
    public enum BonusType
    { 
    None,

    // Dominion Bonuses
    [InspectorName("Building Efficiency")] BuildingEfficiency,
    [InspectorName("Research Fundamentals")] ResearchFundamentals,
    [InspectorName("Basic Training")] BasicTraining,
    [InspectorName("Recruitment")] Recruitment,
    [InspectorName("First Aid")] FirstAid,
    [InspectorName("Emergency Ward")] EmergencyWard,
    [InspectorName("Storage Optimization")] StorageOptimization,
    [InspectorName("Welfare Growth")] WelfareGrowth,
    [InspectorName("Infrastructure Optimization")] InfrastructureOptimization,
    [InspectorName("Automation")] Automation,
    [InspectorName("Tactical Maneuvers")] TacticalManeuvers,
    [InspectorName("Dynamic Endurance")] DynamicEndurance,


    // Prosperity Bonuses
    [InspectorName("Basic Farming")] BasicFarming,
    [InspectorName("Lumbering")] Lumbering,
    [InspectorName("Mining")] Mining,
    [InspectorName("Power Generation")] PowerGeneration,
    [InspectorName("Trading Basics")] TradingBasics,
    [InspectorName("Scavenging")] Scavenging,
    [InspectorName("Resource Gathering")] ResourceGathering,
    [InspectorName("Power Grid")] PowerGrid,
    [InspectorName("Recycling")] Recycling,
    [InspectorName("Market Manipulation")] MarketManipulation,
    [InspectorName("Resource Transportation")] ResourceTransportation,
    [InspectorName("Terraforming")] Terraforming,

    // Supremacy Bonuses
    [InspectorName("Infantry Training")] InfantryTraining,
    [InspectorName("Rider Training")] RiderTraining,
    [InspectorName("Ranged Training")] RangedTraining,
    [InspectorName("Infantry Indurance")] InfantryIndurance,
    [InspectorName("Rider Indurance")] RiderIndurance,
    [InspectorName("Ranged Indurance")] RangedIndurance,
    [InspectorName("Squad Capacity")] SquadCapacity,
    [InspectorName("Squad Tactics")] SquadTactics,
    [InspectorName("Basic Armor")] BasicArmor,
    [InspectorName("Weapon Crafting")] WeaponCrafting,
    [InspectorName("Medical Training")] MedicalTraining,
    [InspectorName("Scouting")] Scouting,
    [InspectorName("Ammunition Crafting")] AmmunitionCrafting,
    [InspectorName("Siege Tactics")] SiegeTactics,
    [InspectorName("Counter-Intelligence")] CounterIntelligence,
    [InspectorName("Advanced Camouflage")] AdvancedCamouflage

    }
}

[System.Serializable]
public class ResearchPrerequisite
{
    public ResearchNode node;
    public int requiredTier; // Specify which tier needs to be completed
    public int requiredLevel; // Optionally specify required level for that tier
}

[System.Serializable]
public class ResearchTierData
{
    public int tierNumber;
    
    [Header("Prerequisites")]
    public ResearchPrerequisite[] prerequisites;
    public int requiredLabLevel;

    [Header("Information")]
    public string nodeName;
    public string description;
    public Sprite icon;

    [Header("Leveling")]
    public int maxLevel;

    [Header("Stats")]
    public int bonus;  // Bonus effect per level
}