using UnityEngine;
using UnityEngine.UI;

public class BarAnimatorUI : MonoBehaviour
{
    [System.Serializable]
    public class Bar
    {
        public RectTransform rect;      // The UI bar
        public float minHeight = 20f;   // Minimum bar height
        public float maxHeight = 200f;  // Maximum bar height
        public float speed = 5f;        // Lerp speed
        public float changeInterval = 0.5f; // How often to pick a new height

        [HideInInspector] public float targetHeight;
        [HideInInspector] public float timer;
    }

    public Bar[] bars = new Bar[3];

    void Start()
    {
        foreach (var bar in bars)
        {
            if (bar.rect != null)
            {
                float startHeight = Random.Range(bar.minHeight, bar.maxHeight);
                SetBarHeight(bar.rect, startHeight);
                bar.targetHeight = startHeight;
            }
        }
    }

    void Update()
    {
        foreach (var bar in bars)
        {
            if (bar.rect == null) continue;

            bar.timer += Time.deltaTime;
            if (bar.timer >= bar.changeInterval)
            {
                bar.targetHeight = Random.Range(bar.minHeight, bar.maxHeight);
                bar.timer = 0f;
            }

            float currentHeight = bar.rect.sizeDelta.y;
            float newHeight = Mathf.Lerp(currentHeight, bar.targetHeight, Time.deltaTime * bar.speed);
            SetBarHeight(bar.rect, newHeight);
        }
    }

    void SetBarHeight(RectTransform rect, float height)
    {
        Vector2 size = rect.sizeDelta;
        size.y = height;
        rect.sizeDelta = size;
    }
}
