using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Integrates the notification system with the training system.
/// Attach this to training UI buttons or objects that should show notifications.
/// </summary>
public class TrainingNotificationIntegration : MonoBehaviour
{
    [Header("Training Settings")]
    [SerializeField] private TroopType troopType;
    [SerializeField] private int troopLevel = 1;
    [SerializeField] private Button trainingButton;

    private NotificationSystem notificationSystem;
    private TroopSO troopData;

    private void Awake()
    {
        // Add NotificationSystem component if it doesn't exist
        notificationSystem = GetComponent<NotificationSystem>();
        if (notificationSystem == null)
        {
            notificationSystem = gameObject.AddComponent<NotificationSystem>();
        }

        // Set process type based on troop type
        switch (troopType)
        {
            case TroopType.Infantry:
                notificationSystem.SetProcessType("Training_Infantry");
                break;
            case TroopType.Rider:
                notificationSystem.SetProcessType("Training_Rider");
                break;
            case TroopType.Ranged:
                notificationSystem.SetProcessType("Training_Ranged");
                break;
            default:
                notificationSystem.SetProcessType("Training");
                break;
        }

        // Set target ID based on troop type and level
        notificationSystem.SetTargetId($"Training_{troopType}_{troopLevel}");
    }

    private void Start()
    {
        // Find troop data
        if (TrainingManager.Instance != null)
        {
            troopData = FindTroopData();
            if (troopData != null)
            {
                // Set resource requirements based on troop data
                notificationSystem.SetResourceRequirements(
                    troopData.FoodCost,
                    troopData.WoodCost,
                    troopData.MetalCost,
                    0 // No gold cost for training
                );
            }
        }

        // Subscribe to button click event if button is assigned
        if (trainingButton != null)
        {
            trainingButton.onClick.AddListener(OnTrainingButtonClicked);
        }

        // Subscribe to notification status changed event
        if (NotificationManager.Instance != null)
        {
            NotificationManager.Instance.OnNotificationStatusChanged += OnNotificationStatusChanged;
        }
    }

    private void OnDestroy()
    {
        // Unsubscribe from notification status changed event
        if (NotificationManager.Instance != null)
        {
            NotificationManager.Instance.OnNotificationStatusChanged -= OnNotificationStatusChanged;
        }

        // Unsubscribe from button click event
        if (trainingButton != null)
        {
            trainingButton.onClick.RemoveListener(OnTrainingButtonClicked);
        }
    }

    /// <summary>
    /// Find troop data based on troop type and level
    /// </summary>
    private TroopSO FindTroopData()
    {
        List<TroopSO> troopList = null;

        switch (troopType)
        {
            case TroopType.Infantry:
                troopList = TrainingManager.Instance.infantryTroops;
                break;
            case TroopType.Rider:
                troopList = TrainingManager.Instance.riderTroops;
                break;
            case TroopType.Ranged:
                troopList = TrainingManager.Instance.rangedTroops;
                break;
        }

        if (troopList != null)
        {
            foreach (TroopSO troop in troopList)
            {
                if (troop.Level == troopLevel)
                {
                    return troop;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Handle training button click
    /// </summary>
    private void OnTrainingButtonClicked()
    {
        // Force check notification status after button click
        notificationSystem.ForceCheckNotification();
    }

    /// <summary>
    /// Handle notification status changed event
    /// </summary>
    private void OnNotificationStatusChanged(string targetId, bool isActive)
    {
        // Only handle events for this target
        if (targetId != notificationSystem.TargetId)
        {
            return;
        }

        // Update button interactable state if button is assigned
        if (trainingButton != null)
        {
            trainingButton.interactable = isActive;
        }
    }
}
