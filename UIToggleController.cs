using UnityEngine;
using UnityEngine.UI;

public class UIToggleController : MonoBehaviour
{
    [SerializeField] private GameObject[] uiElements;
    [SerializeField] private RectTransform buttonTransform;
    [SerializeField] private Button toggleButton;
    
    private bool isVisible = true;

    private void Start()
    {
        // Add button listener through code instead of Inspector
        if (toggleButton != null)
        {
            toggleButton.onClick.RemoveAllListeners();
            toggleButton.onClick.AddListener(ToggleUI);
        }
    }

    private void ToggleUI()
    {
        isVisible = !isVisible;
        
        foreach (GameObject element in uiElements)
        {
            UIElementAnimator animator = element.GetComponent<UIElementAnimator>();
            if (animator != null)
            {
                if (!isVisible)
                {
                    animator.HandleDeactivation();
                }
                else
                {
                    element.SetActive(true);
                }
            }
            else
            {
                element.SetActive(isVisible);
            }
        }

        Vector3 currentRotation = buttonTransform.localEulerAngles;
        currentRotation.z = isVisible ? 0f : 180f;
        buttonTransform.localEulerAngles = currentRotation;
    }
}