using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class TrainingUI : MonoBehaviour
{
    // Singleton instance for easy access
    private static TrainingUI _instance;

    // Modified to ensure we always have a valid instance
    public static TrainingUI Instance
    {
        get
        {
            if (_instance == null)
            {
                // Try to find an instance in the scene
                _instance = FindAnyObjectByType<TrainingUI>();

                if (_instance == null)
                {
                    Debug.LogError("No TrainingUI instance found in scene!");
                }
                else
                {
                    Debug.Log($"Found TrainingUI instance in scene: {_instance.GetInstanceID()}");
                }
            }

            return _instance;
        }
    }

    [Header("Main Panels")]
    public GameObject mainPanel;
    public GameObject scrollViewPanel;
    public GameObject troopDetailPanel;
    public GameObject countdownPanel;
    public GameObject speedUpPanel;
    public GameObject completionPanel;
    public GameObject troopStatsPanel;
    public GameObject resourceReplenishmentPanel;

    [Header("Scroll View")]
    public Transform thumbnailContainer;
    public GameObject thumbnailPrefab;
    public TroopType currentTroopType = TroopType.Infantry;

    [Header("Troop Detail")]
    public Image troopImage;
    public TextMeshProUGUI troopNameText;
    public TextMeshProUGUI troopTypeText;
    public TextMeshProUGUI troopLevelText;
    public TextMeshProUGUI troopDescriptionText;
    public TextMeshProUGUI foodRequirementText;
    public TextMeshProUGUI woodRequirementText;
    public TextMeshProUGUI metalRequirementText;
    public TextMeshProUGUI timeRequirementText;
    public TextMeshProUGUI totalTrainedText;
    public Slider trainingAmountSlider;
    public TMP_InputField trainingAmountInput;
    public Button trainButton;
    public Button upgradeButton;
    public Button statsButton;

    [Header("Countdown")]
    public TextMeshProUGUI countdownText;
    public Button speedUpButton;
    public Button cancelButton;

    [Header("Speed Up")]
    public TextMeshProUGUI requiredTimeText;
    public TextMeshProUGUI availableTimeText;
    public Button confirmSpeedUpButton;
    public Button cancelSpeedUpButton;

    [Header("Completion")]
    public TextMeshProUGUI gainedBPText;

    [Header("Troop Stats")]
    public TextMeshProUGUI attackText;
    public TextMeshProUGUI defenseText;
    public TextMeshProUGUI healthText;
    public Button backFromStatsButton;

    [Header("Resource Replenishment")]
    public TextMeshProUGUI missingResourcesText;
    public Button replenishButton;
    public Button cancelReplenishButton;

    [Header("Background")]
    public Image backgroundImage;
    public Sprite[] infantryBackgrounds;
    public Sprite[] riderBackgrounds;
    public Sprite[] rangedBackgrounds;

    [Header("Type Selection")]
    public Button infantryButton;
    public Button riderButton;
    public Button rangedButton;

    [Header("UI Controls")]
    public Button closeButton;

    // Make this public so TroopThumbnail can access it to prevent infinite recursion
    public TroopSO selectedTroop;
    private int trainingAmount = 1;
    private TrainingQueueItem currentTrainingItem;
    private Coroutine countdownCoroutine;
    private List<GameObject> thumbnailObjects = new List<GameObject>();

    // Flag to prevent infinite recursion
    private bool isSelectingTroop = false;

    private void Awake()
    {
        Debug.Log("TrainingUI.Awake() called");

        // Set up singleton instance
        if (_instance == null)
        {
            _instance = this;
            Debug.Log($"TrainingUI singleton instance set: {GetInstanceID()}");
        }
        else if (_instance != this)
        {
            Debug.Log($"Duplicate TrainingUI instance detected, destroying this instance. Original instance ID: {_instance.GetInstanceID()}, This instance ID: {GetInstanceID()}");
            Destroy(gameObject);
            return;
        }

        // Make sure the instance is accessible through the static property
        if (Instance == null)
        {
            Debug.LogError("TrainingUI.Instance is null even after setting _instance in Awake");
        }
        else
        {
            Debug.Log($"TrainingUI.Instance is properly set: {Instance.GetInstanceID()}");
        }

        // Set up button listeners for internal UI functionality
        trainButton.onClick.AddListener(StartTraining);
        upgradeButton.onClick.AddListener(StartUpgrade);
        statsButton.onClick.AddListener(ShowStats);
        backFromStatsButton.onClick.AddListener(HideStats);
        speedUpButton.onClick.AddListener(ShowSpeedUpPanel);
        cancelButton.onClick.AddListener(CancelTraining);
        confirmSpeedUpButton.onClick.AddListener(ConfirmSpeedUp);
        cancelSpeedUpButton.onClick.AddListener(HideSpeedUpPanel);
        replenishButton.onClick.AddListener(ReplenishResources);
        cancelReplenishButton.onClick.AddListener(HideResourceReplenishmentPanel);

        // Set up close button
        if (closeButton != null)
        {
            // Remove any existing listeners to prevent duplicates
            closeButton.onClick.RemoveAllListeners();
            closeButton.onClick.AddListener(Close);
            Debug.Log("Close button listener set up");
        }
        else
        {
            Debug.LogWarning("Close button is not assigned in the inspector!");
        }

        // Set up slider and input field
        trainingAmountSlider.onValueChanged.AddListener(OnSliderValueChanged);
        trainingAmountInput.onValueChanged.AddListener(OnInputValueChanged);

        // Set up type selection buttons
        if (infantryButton != null)
        {
            infantryButton.onClick.RemoveAllListeners();
            infantryButton.onClick.AddListener(() => SwitchTroopType(TroopType.Infantry));
            Debug.Log("Infantry button listener set up");
        }
        else
        {
            Debug.LogWarning("Infantry button is not assigned in the inspector!");
        }

        if (riderButton != null)
        {
            riderButton.onClick.RemoveAllListeners();
            riderButton.onClick.AddListener(() => SwitchTroopType(TroopType.Rider));
            Debug.Log("Rider button listener set up");
        }
        else
        {
            Debug.LogWarning("Rider button is not assigned in the inspector!");
        }

        if (rangedButton != null)
        {
            rangedButton.onClick.RemoveAllListeners();
            rangedButton.onClick.AddListener(() => SwitchTroopType(TroopType.Ranged));
            Debug.Log("Ranged button listener set up");
        }
        else
        {
            Debug.LogWarning("Ranged button is not assigned in the inspector!");
        }

        // Try to find the Training Manager in the scene
        GameObject trainingManager = GameObject.Find("TrainingManager");
        if (trainingManager != null && !trainingManager.activeSelf)
        {
            trainingManager.SetActive(true);
            Debug.Log("TrainingUI.Awake: Activated TrainingManager");
        }
        else if (trainingManager == null)
        {
            // Try to find the Training Manager by type
            TrainingManager managerComponent = FindAnyObjectByType<TrainingManager>();
            if (managerComponent != null)
            {
                trainingManager = managerComponent.gameObject;
                trainingManager.SetActive(true);
                Debug.Log($"TrainingUI.Awake: Found and activated TrainingManager: {trainingManager.name}");
            }
        }

        // Subscribe to training events if TrainingManager instance exists
        if (TrainingManager.Instance != null)
        {
            TrainingManager.Instance.OnTrainingStarted += OnTrainingStarted;
            TrainingManager.Instance.OnTrainingCompleted += OnTrainingCompleted;
            TrainingManager.Instance.OnTrainingCancelled += OnTrainingCancelled;
            TrainingManager.Instance.OnTrainingSpeededUp += OnTrainingSpeededUp;
            Debug.Log("TrainingUI.Awake: Successfully subscribed to TrainingManager events");
        }
        else
        {
            Debug.LogWarning("TrainingManager.Instance is null in TrainingUI.Awake(). Will try to subscribe in Start().");
        }

        // Hide the UI initially
        HideAllPanels();
        gameObject.SetActive(false);
    }

    private void Start()
    {
        Debug.Log("TrainingUI.Start() called");

        // Try to subscribe to training events again if it wasn't possible in Awake
        if (TrainingManager.Instance != null)
        {
            // Check if we're already subscribed to avoid duplicate subscriptions
            TrainingManager.Instance.OnTrainingStarted -= OnTrainingStarted; // Remove first to avoid duplicates
            TrainingManager.Instance.OnTrainingCompleted -= OnTrainingCompleted;
            TrainingManager.Instance.OnTrainingCancelled -= OnTrainingCancelled;
            TrainingManager.Instance.OnTrainingSpeededUp -= OnTrainingSpeededUp;

            // Subscribe again
            TrainingManager.Instance.OnTrainingStarted += OnTrainingStarted;
            TrainingManager.Instance.OnTrainingCompleted += OnTrainingCompleted;
            TrainingManager.Instance.OnTrainingCancelled += OnTrainingCancelled;
            TrainingManager.Instance.OnTrainingSpeededUp += OnTrainingSpeededUp;

            Debug.Log("Successfully subscribed to TrainingManager events in Start()");
        }
        else
        {
            Debug.LogError("TrainingManager.Instance is still null in Start(). Training UI functionality will be limited.");
        }

        // Make sure the close button is set up
        if (closeButton != null)
        {
            // Remove any existing listeners to prevent duplicates
            closeButton.onClick.RemoveAllListeners();
            closeButton.onClick.AddListener(Close);
            Debug.Log("Close button listener set up in Start()");
        }
        else
        {
            Debug.LogWarning("Close button is not assigned in the inspector!");
        }

        // Make sure the train button is set up
        if (trainButton != null)
        {
            // Remove any existing listeners to prevent duplicates
            trainButton.onClick.RemoveAllListeners();
            trainButton.onClick.AddListener(StartTraining);
            Debug.Log("Train button listener set up in Start()");
        }
        else
        {
            Debug.LogWarning("Train button is not assigned in the inspector!");
        }

        // Initialize UI but keep it hidden
        if (TrainingManager.Instance != null)
        {
            SwitchTroopType(currentTroopType);
        }
        else
        {
            Debug.LogError("Cannot initialize UI without TrainingManager.Instance");
        }
    }

    // Method to open the training UI for a specific troop type
    public void Open(TroopType troopType)
    {
        Debug.Log($"Open method called with troop type: {troopType}");

        // Make sure the UI is visible
        gameObject.SetActive(true);

        // Show the main panels
        if (mainPanel != null) mainPanel.SetActive(true);
        if (scrollViewPanel != null) scrollViewPanel.SetActive(true);
        if (troopDetailPanel != null) troopDetailPanel.SetActive(true);

        // Set the current troop type
        currentTroopType = troopType;

        // Update button visuals
        if (infantryButton != null) infantryButton.interactable = troopType != TroopType.Infantry;
        if (riderButton != null) riderButton.interactable = troopType != TroopType.Rider;
        if (rangedButton != null) rangedButton.interactable = troopType != TroopType.Ranged;

        // Switch to the selected troop type immediately
        SwitchTroopType(troopType);

        Debug.Log($"Opened Training UI for {troopType} troops");
    }

    // Overload for opening with the current troop type
    public void Open()
    {
        Open(currentTroopType);
    }

    private void OnDestroy()
    {
        // Unsubscribe from events
        if (TrainingManager.Instance != null)
        {
            TrainingManager.Instance.OnTrainingStarted -= OnTrainingStarted;
            TrainingManager.Instance.OnTrainingCompleted -= OnTrainingCompleted;
            TrainingManager.Instance.OnTrainingCancelled -= OnTrainingCancelled;
            TrainingManager.Instance.OnTrainingSpeededUp -= OnTrainingSpeededUp;
        }
    }

    private void HideAllPanels()
    {
        mainPanel.SetActive(false);
        scrollViewPanel.SetActive(false);
        troopDetailPanel.SetActive(false);
        countdownPanel.SetActive(false);
        speedUpPanel.SetActive(false);
        completionPanel.SetActive(false);
        troopStatsPanel.SetActive(false);
        resourceReplenishmentPanel.SetActive(false);
    }

    public void SwitchTroopType(TroopType type)
    {
        Debug.Log($"SwitchTroopType called with type: {type}");

        // Check if TrainingManager is available
        if (TrainingManager.Instance == null)
        {
            Debug.LogError("TrainingManager.Instance is null, cannot switch troop type");

            // Try to find a TrainingManager in the scene
            TrainingManager existingManager = FindAnyObjectByType<TrainingManager>();
            if (existingManager != null)
            {
                Debug.Log("Found existing TrainingManager in scene");
            }
            else
            {
                Debug.LogError("No TrainingManager found in scene");
                return;
            }
        }

        // Set the current troop type
        currentTroopType = type;

        // Update button visuals
        if (infantryButton != null) infantryButton.interactable = type != TroopType.Infantry;
        if (riderButton != null) riderButton.interactable = type != TroopType.Rider;
        if (rangedButton != null) rangedButton.interactable = type != TroopType.Ranged;

        // Check if thumbnailContainer and thumbnailPrefab are valid
        if (thumbnailContainer == null)
        {
            Debug.LogError("thumbnailContainer is null! Cannot create thumbnails.");
            return;
        }

        if (thumbnailPrefab == null)
        {
            Debug.LogError("thumbnailPrefab is null! Cannot create thumbnails.");
            return;
        }

        // Clear existing thumbnails
        foreach (GameObject obj in thumbnailObjects)
        {
            if (obj != null) Destroy(obj);
        }
        thumbnailObjects.Clear();

        // Get troops for the selected type
        List<TroopSO> troops = null;
        try
        {
            troops = TrainingManager.Instance.GetTroopsForType(type);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error getting troops for type {type}: {e.Message}");
            return;
        }

        if (troops == null || troops.Count == 0)
        {
            Debug.LogWarning($"No troops found for type {type}");
            return;
        }

        // Create thumbnails
        foreach (TroopSO troop in troops)
        {
            if (troop == null) continue;

            try
            {
                GameObject thumbnailObj = Instantiate(thumbnailPrefab, thumbnailContainer);
                if (thumbnailObj == null)
                {
                    Debug.LogError("Failed to instantiate thumbnailPrefab!");
                    continue;
                }

                thumbnailObjects.Add(thumbnailObj);

                TroopThumbnail thumbnail = thumbnailObj.GetComponent<TroopThumbnail>();
                if (thumbnail != null)
                {
                    thumbnail.Initialize(troop, this);
                }
                else
                {
                    Debug.LogError("TroopThumbnail component not found on instantiated prefab!");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error creating thumbnail for troop {troop.Name}: {e.Message}");
            }
        }

        // Select the first troop
        if (troops.Count > 0)
        {
            try
            {
                SelectTroop(troops[0]);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error selecting troop: {e.Message}");
            }
        }
    }

    private IEnumerator PopulateThumbnails(TroopType type)
    {
        Debug.Log($"PopulateThumbnails called for type: {type}");

        // Clear existing thumbnails safely
        Debug.Log($"Clearing {thumbnailObjects.Count} existing thumbnails");
        List<GameObject> thumbnailsToDestroy = new List<GameObject>(thumbnailObjects);
        thumbnailObjects.Clear();

        foreach (GameObject obj in thumbnailsToDestroy)
        {
            if (obj != null)
            {
                Destroy(obj);
            }
        }

        // Yield to prevent freezing
        yield return null;

        // Check if thumbnail container is valid
        if (thumbnailContainer == null)
        {
            Debug.LogError("Thumbnail container is null, cannot create thumbnails");
            yield break;
        }

        // Check if thumbnail prefab is valid
        if (thumbnailPrefab == null)
        {
            Debug.LogError("Thumbnail prefab is null, cannot create thumbnails");
            yield break;
        }

        // Get troops for the selected type
        List<TroopSO> troops = null;
        try
        {
            troops = TrainingManager.Instance.GetTroopsForType(type);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error getting troops for type {type}: {e.Message}");

            // Create an empty list as a fallback
            troops = new List<TroopSO>();

            // Try to create some placeholder troops
            Debug.LogWarning("Creating placeholder troops as fallback");
            try
            {
                // Create a placeholder troop using ScriptableObject.CreateInstance
                TroopSO placeholder = ScriptableObject.CreateInstance<TroopSO>();

                // Set basic properties
                placeholder.Name = $"{type} Soldier";
                placeholder.Description = "Placeholder troop";
                placeholder.Type = type;
                placeholder.Level = 1;

                // Set combat stats
                placeholder.Attack = 50;
                placeholder.Defense = 50;
                placeholder.Health = 100;

                // Set training requirements
                placeholder.FoodCost = 100;
                placeholder.WoodCost = 100;
                placeholder.MetalCost = 50;
                placeholder.TrainingTime = 60; // 1 minute
                placeholder.BattlePower = 10;

                // Create a default image if needed
                if (placeholder.Image == null)
                {
                    Texture2D texture = new Texture2D(1, 1);
                    texture.SetPixel(0, 0, Color.white);
                    texture.Apply();
                    placeholder.Image = Sprite.Create(texture, new Rect(0, 0, 1, 1), Vector2.zero);
                }

                // Create a default thumbnail if needed
                if (placeholder.Thumbnail == null)
                {
                    Texture2D texture = new Texture2D(1, 1);
                    texture.SetPixel(0, 0, Color.white);
                    texture.Apply();
                    placeholder.Thumbnail = Sprite.Create(texture, new Rect(0, 0, 1, 1), Vector2.zero);
                }

                // Calculate all stats
                try
                {
                    placeholder.CalculateAll();
                }
                catch
                {
                    // Ignore calculation errors
                }

                // Add to the list
                troops.Add(placeholder);

                Debug.Log($"Created placeholder troop: {placeholder.Name}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error creating placeholder troops: {ex.Message}");
            }
        }

        if (troops == null)
        {
            Debug.LogError($"GetTroopsForType returned null for type {type}");
            troops = new List<TroopSO>(); // Create an empty list as a fallback
        }

        Debug.Log($"Got {troops.Count} troops for type {type}");

        // Yield to prevent freezing
        yield return null;

        // Create thumbnails
        int count = 0;
        foreach (TroopSO troop in troops)
        {
            if (troop == null)
            {
                Debug.LogError("Troop is null, skipping thumbnail creation");
                continue;
            }

            Debug.Log($"Creating thumbnail for troop: {troop.Name} (Level {troop.Level})");

            try
            {
                GameObject thumbnailObj = Instantiate(thumbnailPrefab, thumbnailContainer);
                if (thumbnailObj == null)
                {
                    Debug.LogError("Failed to instantiate thumbnail object");
                    continue;
                }

                thumbnailObjects.Add(thumbnailObj);

                // Set up thumbnail
                TroopThumbnail thumbnail = thumbnailObj.GetComponent<TroopThumbnail>();
                if (thumbnail != null)
                {
                    // Always use the singleton instance to ensure we have a valid reference
                    thumbnail.Initialize(troop, this);
                    Debug.Log($"Initialized thumbnail for troop: {troop.Name}");
                }
                else
                {
                    Debug.LogError($"TroopThumbnail component not found on instantiated prefab for troop: {troop.Name}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error creating thumbnail for troop {troop.Name}: {e.Message}");
            }

            // Yield every few thumbnails to prevent freezing
            count++;
            if (count % 3 == 0)
            {
                yield return null;
            }
        }

        // Yield to prevent freezing
        yield return null;

        // Select a troop if possible
        if (troops.Count > 0)
        {
            // Try to select the highest unlocked troop
            TroopSO troopToSelect = null;

            try
            {
                TroopSO highestUnlocked = TrainingManager.Instance.GetHighestUnlockedTroop(type);
                if (highestUnlocked != null)
                {
                    Debug.Log($"Selecting highest unlocked troop: {highestUnlocked.Name} (Level {highestUnlocked.Level})");
                    troopToSelect = highestUnlocked;
                }
                else
                {
                    Debug.Log($"No unlocked troops found, selecting first troop: {troops[0].Name} (Level {troops[0].Level})");
                    troopToSelect = troops[0];
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error getting highest unlocked troop: {e.Message}");
                troopToSelect = troops[0]; // Fallback to first troop
            }

            if (troopToSelect != null)
            {
                SelectTroop(troopToSelect);
            }
            else
            {
                Debug.LogWarning("No troop available to select");
            }
        }
        else
        {
            Debug.LogWarning("No troops available to select");
        }

        // Update background image
        try
        {
            UpdateBackgroundImage();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error updating background image: {e.Message}");
        }
    }

    public void SelectTroop(TroopSO troop)
    {
        // Prevent infinite recursion
        if (isSelectingTroop)
        {
            Debug.Log($"SelectTroop: Preventing recursive call for troop: {(troop != null ? troop.Name : "null")}");
            return;
        }

        isSelectingTroop = true;

        try
        {
            Debug.Log($"SelectTroop called with troop: {(troop != null ? troop.Name : "null")}");

            // Check if troop is null
            if (troop == null)
            {
                Debug.LogError("Cannot select null troop");
                isSelectingTroop = false;
                return;
            }

            // Check if TrainingManager is available
            if (TrainingManager.Instance == null)
            {
                Debug.LogError("Cannot select troop: TrainingManager.Instance is null");
                isSelectingTroop = false;
                return;
            }

            // Check if this is the same troop that's already selected
            if (selectedTroop == troop)
            {
                Debug.Log($"Troop {troop.Name} is already selected, skipping");
                isSelectingTroop = false;
                return;
            }

            selectedTroop = troop;
            Debug.Log($"Selected troop set to: {selectedTroop.Name}");

            // Update troop details - handle each UI element separately to prevent cascading failures
            try
            {
                if (troopImage != null)
                {
                    if (troop.Image != null)
                    {
                        troopImage.sprite = troop.Image;
                        Debug.Log($"Set troop image sprite: {troop.Image.name}");
                    }
                    else
                    {
                        // Use null sprite if the troop image is null
                        Debug.LogWarning($"Troop image is null for {troop.Name}, using null sprite");
                        troopImage.sprite = null;
                    }
                }
                else
                {
                    Debug.LogError("troopImage is null");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error setting troop image: {e.Message}");
            }

            try
            {
                if (troopNameText != null)
                {
                    troopNameText.text = troop.Name;
                    Debug.Log($"Set troop name text: {troop.Name}");
                }
                else
                {
                    Debug.LogError("troopNameText is null");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error setting troop name text: {e.Message}");
            }

            try
            {
                if (troopTypeText != null)
                {
                    troopTypeText.text = troop.Type.ToString();
                    Debug.Log($"Set troop type text: {troop.Type}");
                }
                else
                {
                    Debug.LogError("troopTypeText is null");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error setting troop type text: {e.Message}");
            }

            try
            {
                if (troopLevelText != null)
                {
                    troopLevelText.text = $"Level {troop.Level}";
                    Debug.Log($"Set troop level text: Level {troop.Level}");
                }
                else
                {
                    Debug.LogError("troopLevelText is null");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error setting troop level text: {e.Message}");
            }

            try
            {
                if (troopDescriptionText != null)
                {
                    troopDescriptionText.text = troop.Description;
                    Debug.Log($"Set troop description text: {troop.Description}");
                }
                else
                {
                    Debug.LogError("troopDescriptionText is null");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error setting troop description text: {e.Message}");
            }

            // Update requirements
            try
            {
                Debug.Log("Updating resource texts");
                UpdateResourceTexts();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error updating resource texts: {e.Message}");
            }

            // Update total trained
            try
            {
                if (totalTrainedText != null)
                {
                    int totalTrained = TrainingManager.Instance.GetTroopCount(troop.Type, troop.Level);
                    totalTrainedText.text = $"Total: {totalTrained}";
                    Debug.Log($"Set total trained text: Total: {totalTrained}");
                }
                else
                {
                    Debug.LogError("totalTrainedText is null");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error updating total trained text: {e.Message}");
            }

            // Update slider
            try
            {
                if (trainingAmountSlider != null && trainingAmountInput != null)
                {
                    trainingAmountSlider.minValue = 1;
                    trainingAmountSlider.maxValue = Mathf.Max(1, TrainingManager.Instance.GetRemainingTrainingCapacity());
                    trainingAmountSlider.value = 1;
                    trainingAmountInput.text = "1";
                    trainingAmount = 1;
                    Debug.Log($"Set training amount slider: min={trainingAmountSlider.minValue}, max={trainingAmountSlider.maxValue}, value={trainingAmountSlider.value}");
                }
                else
                {
                    Debug.LogError($"trainingAmountSlider is {(trainingAmountSlider == null ? "null" : "not null")}, trainingAmountInput is {(trainingAmountInput == null ? "null" : "not null")}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error updating training amount slider: {e.Message}");
            }

            // Update upgrade button
            try
            {
                Debug.Log("Updating upgrade button");
                UpdateUpgradeButton();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error updating upgrade button: {e.Message}");
            }

            // Update background image
            try
            {
                Debug.Log("Updating background image");
                UpdateBackgroundImage();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error updating background image: {e.Message}");
            }

            // Update thumbnails
            try
            {
                Debug.Log($"Updating {thumbnailObjects.Count} thumbnails");
                foreach (GameObject obj in thumbnailObjects)
                {
                    if (obj == null) continue;

                    TroopThumbnail thumbnail = obj.GetComponent<TroopThumbnail>();
                    if (thumbnail != null && thumbnail.Troop != null)
                    {
                        thumbnail.UpdateSelection(thumbnail.Troop == selectedTroop);
                        Debug.Log($"Updated selection for thumbnail: {thumbnail.Troop.Name}, selected: {thumbnail.Troop == selectedTroop}");
                    }
                    else
                    {
                        Debug.LogError("TroopThumbnail component or Troop is null on thumbnail object");
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error updating thumbnails: {e.Message}");
            }

            // Update stats
            try
            {
                Debug.Log("Updating stats panel");
                UpdateStatsPanel();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error updating stats panel: {e.Message}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in SelectTroop: {e.Message}\n{e.StackTrace}");
        }
        finally
        {
            // Always reset the flag
            isSelectingTroop = false;
        }
    }

    private void UpdateResourceTexts()
    {
        // Check if GameManager and TrainingManager are available
        if (GameManager.Instance == null || TrainingManager.Instance == null)
        {
            Debug.LogError("Cannot update resource texts: GameManager.Instance or TrainingManager.Instance is null");

            // Disable train button as a fallback
            if (trainButton != null)
            {
                trainButton.interactable = false;
            }
            return;
        }

        try
        {
            bool hasEnoughFood = GameManager.Instance.HasEnoughFood(selectedTroop.FoodCost * trainingAmount);
            bool hasEnoughWood = GameManager.Instance.HasEnoughWood(selectedTroop.WoodCost * trainingAmount);
            bool hasEnoughMetal = GameManager.Instance.HasEnoughMetal(selectedTroop.MetalCost * trainingAmount);

            foodRequirementText.text = $"{selectedTroop.FoodCost * trainingAmount}";
            woodRequirementText.text = $"{selectedTroop.WoodCost * trainingAmount}";
            metalRequirementText.text = $"{selectedTroop.MetalCost * trainingAmount}";

            // Calculate total training time based on the number of troops (scales linearly)
            float totalTrainingTime = selectedTroop.TrainingTime * trainingAmount;
            timeRequirementText.text = $"{FormatTime(totalTrainingTime)}";

            foodRequirementText.color = hasEnoughFood ? Color.white : Color.red;
            woodRequirementText.color = hasEnoughWood ? Color.white : Color.red;
            metalRequirementText.color = hasEnoughMetal ? Color.white : Color.red;

            // Enable/disable train button
            trainButton.interactable = hasEnoughFood && hasEnoughWood && hasEnoughMetal &&
                                      TrainingManager.Instance.IsTroopUnlocked(selectedTroop) &&
                                      TrainingManager.Instance.GetRemainingTrainingCapacity() >= trainingAmount;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in UpdateResourceTexts: {e.Message}\n{e.StackTrace}");

            // Disable train button as a fallback
            if (trainButton != null)
            {
                trainButton.interactable = false;
            }
        }
    }

    private void UpdateUpgradeButton()
    {
        // Check if TrainingManager is available
        if (TrainingManager.Instance == null)
        {
            Debug.LogError("Cannot update upgrade button: TrainingManager.Instance is null");

            // Hide upgrade button as a fallback
            if (upgradeButton != null)
            {
                upgradeButton.gameObject.SetActive(false);
            }
            return;
        }

        try
        {
            // Find the next level troop
            List<TroopSO> troops = TrainingManager.Instance.GetTroopsForType(selectedTroop.Type);
            TroopSO nextLevelTroop = null;

            foreach (TroopSO troop in troops)
            {
                if (troop.Level == selectedTroop.Level + 1)
                {
                    nextLevelTroop = troop;
                    break;
                }
            }

            // Show upgrade button only if there's a next level and it's unlocked
            if (nextLevelTroop != null && TrainingManager.Instance.IsTroopUnlocked(nextLevelTroop))
            {
                upgradeButton.gameObject.SetActive(true);
                upgradeButton.interactable = TrainingManager.Instance.GetTroopCount(selectedTroop.Type, selectedTroop.Level) > 0;
            }
            else
            {
                upgradeButton.gameObject.SetActive(false);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in UpdateUpgradeButton: {e.Message}\n{e.StackTrace}");

            // Hide upgrade button as a fallback
            if (upgradeButton != null)
            {
                upgradeButton.gameObject.SetActive(false);
            }
        }
    }

    private void UpdateBackgroundImage()
    {
        try
        {
            Debug.Log("UpdateBackgroundImage called");

            // Check if selectedTroop is null
            if (selectedTroop == null)
            {
                Debug.LogError("selectedTroop is null in UpdateBackgroundImage");
                return;
            }

            if (backgroundImage == null)
            {
                Debug.LogError("backgroundImage is null");
                return;
            }

            Sprite[] backgrounds;
            switch (currentTroopType)
            {
                case TroopType.Infantry:
                    backgrounds = infantryBackgrounds;
                    Debug.Log($"Using infantry backgrounds, count: {(infantryBackgrounds != null ? infantryBackgrounds.Length : 0)}");
                    break;
                case TroopType.Rider:
                    backgrounds = riderBackgrounds;
                    Debug.Log($"Using rider backgrounds, count: {(riderBackgrounds != null ? riderBackgrounds.Length : 0)}");
                    break;
                case TroopType.Ranged:
                    backgrounds = rangedBackgrounds;
                    Debug.Log($"Using ranged backgrounds, count: {(rangedBackgrounds != null ? rangedBackgrounds.Length : 0)}");
                    break;
                default:
                    backgrounds = infantryBackgrounds;
                    Debug.Log($"Using default (infantry) backgrounds, count: {(infantryBackgrounds != null ? infantryBackgrounds.Length : 0)}");
                    break;
            }

            // Check if backgrounds array is valid
            if (backgrounds == null || backgrounds.Length == 0)
            {
                Debug.LogWarning("No background sprites available, using null sprite");
                backgroundImage.sprite = null;
                return;
            }

            // Select background based on level
            int backgroundIndex;
            if (selectedTroop.Level <= 3)
                backgroundIndex = 0;
            else if (selectedTroop.Level <= 6)
                backgroundIndex = 1;
            else if (selectedTroop.Level <= 9)
                backgroundIndex = 2;
            else
                backgroundIndex = 3;

            // Make sure we don't go out of bounds
            backgroundIndex = Mathf.Min(backgroundIndex, backgrounds.Length - 1);
            backgroundIndex = Mathf.Max(0, backgroundIndex); // Also ensure it's not negative

            if (backgroundIndex < backgrounds.Length && backgrounds[backgroundIndex] != null)
            {
                backgroundImage.sprite = backgrounds[backgroundIndex];
                Debug.Log($"Set background image sprite: {backgrounds[backgroundIndex].name}");
            }
            else
            {
                Debug.LogWarning($"Background sprite at index {backgroundIndex} is null or out of bounds, using null sprite");
                backgroundImage.sprite = null;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in UpdateBackgroundImage: {e.Message}\n{e.StackTrace}");

            // Set sprite to null as a last resort
            try
            {
                if (backgroundImage != null)
                {
                    backgroundImage.sprite = null;
                }
            }
            catch
            {
                // Ignore any errors in the fallback code
            }
        }
    }

    private void UpdateStatsPanel()
    {
        Debug.Log("UpdateStatsPanel called");

        if (attackText != null)
        {
            attackText.text = $"Attack: {selectedTroop.Attack}";
            Debug.Log($"Set attack text: Attack: {selectedTroop.Attack}");
        }
        else
        {
            Debug.LogError("attackText is null");
        }

        if (defenseText != null)
        {
            defenseText.text = $"Defense: {selectedTroop.Defense}";
            Debug.Log($"Set defense text: Defense: {selectedTroop.Defense}");
        }
        else
        {
            Debug.LogError("defenseText is null");
        }

        if (healthText != null)
        {
            healthText.text = $"Health: {selectedTroop.Health}";
            Debug.Log($"Set health text: Health: {selectedTroop.Health}");
        }
        else
        {
            Debug.LogError("healthText is null");
        }
    }

    private void OnSliderValueChanged(float value)
    {
        trainingAmount = Mathf.RoundToInt(value);
        trainingAmountInput.text = trainingAmount.ToString();
        UpdateResourceTexts();
    }

    private void OnInputValueChanged(string value)
    {
        if (int.TryParse(value, out int amount))
        {
            amount = Mathf.Clamp(amount, 1, Mathf.RoundToInt(trainingAmountSlider.maxValue));
            trainingAmount = amount;
            trainingAmountSlider.value = amount;
        }
        UpdateResourceTexts();
    }

    private void StartTraining()
    {
        // Check if TrainingManager and GameManager are available
        if (TrainingManager.Instance == null || GameManager.Instance == null)
        {
            Debug.LogError("Cannot start training: TrainingManager.Instance or GameManager.Instance is null");
            return;
        }

        try
        {
            if (!TrainingManager.Instance.CanTrainTroop(selectedTroop, trainingAmount))
            {
                // Check if it's due to resources
                bool hasEnoughFood = GameManager.Instance.HasEnoughFood(selectedTroop.FoodCost * trainingAmount);
                bool hasEnoughWood = GameManager.Instance.HasEnoughWood(selectedTroop.WoodCost * trainingAmount);
                bool hasEnoughMetal = GameManager.Instance.HasEnoughMetal(selectedTroop.MetalCost * trainingAmount);

                if (!hasEnoughFood || !hasEnoughWood || !hasEnoughMetal)
                {
                    ShowResourceReplenishmentPanel();
                }
                return;
            }

            TrainingManager.Instance.StartTraining(selectedTroop, trainingAmount);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in StartTraining: {e.Message}\n{e.StackTrace}");
        }
    }

    private void StartUpgrade()
    {
        // Find the next level troop
        List<TroopSO> troops = TrainingManager.Instance.GetTroopsForType(selectedTroop.Type);
        TroopSO nextLevelTroop = null;

        foreach (TroopSO troop in troops)
        {
            if (troop.Level == selectedTroop.Level + 1)
            {
                nextLevelTroop = troop;
                break;
            }
        }

        if (nextLevelTroop != null)
        {
            // Check if we can upgrade
            if (!TrainingManager.Instance.CanUpgradeTroop(selectedTroop, nextLevelTroop, 1))
            {
                // Check if it's due to resources
                float upgradeCostFactor = TrainingManager.Instance.CalculateUpgradeCost(selectedTroop, nextLevelTroop) / 100f;
                int foodCost = Mathf.RoundToInt(nextLevelTroop.FoodCost * upgradeCostFactor);
                int woodCost = Mathf.RoundToInt(nextLevelTroop.WoodCost * upgradeCostFactor);
                int metalCost = Mathf.RoundToInt(nextLevelTroop.MetalCost * upgradeCostFactor);

                bool hasEnoughFood = GameManager.Instance.HasEnoughFood(foodCost);
                bool hasEnoughWood = GameManager.Instance.HasEnoughWood(woodCost);
                bool hasEnoughMetal = GameManager.Instance.HasEnoughMetal(metalCost);

                if (!hasEnoughFood || !hasEnoughWood || !hasEnoughMetal)
                {
                    ShowResourceReplenishmentPanel();
                }
                return;
            }

            TrainingManager.Instance.UpgradeTroop(selectedTroop, nextLevelTroop, 1);

            // Update UI
            SelectTroop(nextLevelTroop);
        }
    }

    private void ShowStats()
    {
        troopDetailPanel.SetActive(false);
        troopStatsPanel.SetActive(true);
    }

    private void HideStats()
    {
        troopStatsPanel.SetActive(false);
        troopDetailPanel.SetActive(true);
    }

    private void OnTrainingStarted(TroopSO troop, int count, float time)
    {
        // Find the training item
        List<TrainingQueueItem> queue = TrainingManager.Instance.GetTrainingQueue();
        foreach (TrainingQueueItem item in queue)
        {
            if (item.Troop == troop && item.Count == count)
            {
                currentTrainingItem = item;
                break;
            }
        }

        // Show countdown panel
        troopDetailPanel.SetActive(false);
        countdownPanel.SetActive(true);

        // Start countdown
        if (countdownCoroutine != null)
        {
            StopCoroutine(countdownCoroutine);
        }
        countdownCoroutine = StartCoroutine(UpdateCountdown());
    }

    private void OnTrainingCompleted(TroopSO troop, int count)
    {
        // Stop countdown
        if (countdownCoroutine != null)
        {
            StopCoroutine(countdownCoroutine);
            countdownCoroutine = null;
        }

        // Hide countdown panel
        countdownPanel.SetActive(false);

        // Show completion panel
        completionPanel.SetActive(true);
        gainedBPText.text = $"+{troop.BattlePower * count} Battle Power";

        // Hide completion panel after 2 seconds
        StartCoroutine(HideCompletionPanel());

        // Update UI
        UpdateResourceTexts();
        UpdateUpgradeButton();
        int totalTrained = TrainingManager.Instance.GetTroopCount(troop.Type, troop.Level);
        totalTrainedText.text = $"Total: {totalTrained}";
    }

    private void OnTrainingCancelled(TroopSO troop)
    {
        // Stop countdown
        if (countdownCoroutine != null)
        {
            StopCoroutine(countdownCoroutine);
            countdownCoroutine = null;
        }

        // Hide countdown panel
        countdownPanel.SetActive(false);
        troopDetailPanel.SetActive(true);

        // Update UI
        UpdateResourceTexts();
    }

    private void OnTrainingSpeededUp(TroopSO troop, float minutes)
    {
        // Hide speed up panel
        speedUpPanel.SetActive(false);

        // Update countdown
        if (currentTrainingItem != null && currentTrainingItem.RemainingTime <= 0)
        {
            // Training is complete
            countdownPanel.SetActive(false);
        }
    }

    private IEnumerator UpdateCountdown()
    {
        while (currentTrainingItem != null && currentTrainingItem.RemainingTime > 0)
        {
            countdownText.text = $"{FormatTime(currentTrainingItem.RemainingTime)}";
            yield return new WaitForSeconds(1f);
        }
    }

    private IEnumerator HideCompletionPanel()
    {
        yield return new WaitForSeconds(2f);
        completionPanel.SetActive(false);
        troopDetailPanel.SetActive(true);
    }

    private void CancelTraining()
    {
        if (currentTrainingItem != null)
        {
            TrainingManager.Instance.CancelTraining(currentTrainingItem);
            currentTrainingItem = null;
        }
    }

    private void ShowSpeedUpPanel()
    {
        if (currentTrainingItem != null)
        {
            speedUpPanel.SetActive(true);

            // Calculate required time
            float requiredMinutes = currentTrainingItem.RemainingTime / 60f;
            requiredTimeText.text = $"{requiredMinutes:F1} minutes";

            // Get available speed-up items
            float availableMinutes = GetAvailableSpeedUpMinutes();
            availableTimeText.text = $"{availableMinutes:F1} minutes";

            // Enable/disable confirm button
            confirmSpeedUpButton.interactable = availableMinutes >= requiredMinutes;
        }
    }

    private void HideSpeedUpPanel()
    {
        speedUpPanel.SetActive(false);
    }

    private float GetAvailableSpeedUpMinutes()
    {
        // Get speed-up items from inventory
        float totalMinutes = 0f;

        // Get training-specific speed-ups
        var trainingSpeedUps = InventorySystem.Instance.GetItemsByTag("TrainingSpeedUp");
        foreach (var item in trainingSpeedUps)
        {
            // Assuming the Value field contains the minutes
            totalMinutes += item.itemSO.Value * item.quantity;
        }

        // Get general speed-ups
        var generalSpeedUps = InventorySystem.Instance.GetItemsByTag("GeneralSpeedUp");
        foreach (var item in generalSpeedUps)
        {
            // Assuming the Value field contains the minutes
            totalMinutes += item.itemSO.Value * item.quantity;
        }

        return totalMinutes;
    }

    private void ConfirmSpeedUp()
    {
        if (currentTrainingItem != null)
        {
            float requiredMinutes = currentTrainingItem.RemainingTime / 60f;

            // Use speed-up items
            UseSpeedUpItems(requiredMinutes);

            // Apply speed-up
            TrainingManager.Instance.SpeedUpTraining(currentTrainingItem, requiredMinutes);
        }
    }

    private void UseSpeedUpItems(float requiredMinutes)
    {
        float remainingMinutes = requiredMinutes;

        // First use training-specific speed-ups
        var trainingSpeedUps = InventorySystem.Instance.GetItemsByTag("TrainingSpeedUp");
        foreach (var item in trainingSpeedUps)
        {
            if (remainingMinutes <= 0) break;

            int minutesPerItem = item.itemSO.Value;
            int itemsNeeded = Mathf.CeilToInt(remainingMinutes / minutesPerItem);
            int itemsToUse = Mathf.Min(itemsNeeded, item.quantity);

            if (itemsToUse > 0)
            {
                float minutesUsed = itemsToUse * minutesPerItem;
                remainingMinutes -= minutesUsed;

                // Remove items from inventory
                item.quantity -= itemsToUse;
            }
        }

        // If still need more, use general speed-ups
        if (remainingMinutes > 0)
        {
            var generalSpeedUps = InventorySystem.Instance.GetItemsByTag("GeneralSpeedUp");
            foreach (var item in generalSpeedUps)
            {
                if (remainingMinutes <= 0) break;

                int minutesPerItem = item.itemSO.Value;
                int itemsNeeded = Mathf.CeilToInt(remainingMinutes / minutesPerItem);
                int itemsToUse = Mathf.Min(itemsNeeded, item.quantity);

                if (itemsToUse > 0)
                {
                    float minutesUsed = itemsToUse * minutesPerItem;
                    remainingMinutes -= minutesUsed;

                    // Remove items from inventory
                    item.quantity -= itemsToUse;
                }
            }
        }

        // If we used more than needed, convert excess to smaller units
        if (remainingMinutes < 0)
        {
            float excessMinutes = -remainingMinutes;
            ConvertExcessMinutesToItems(excessMinutes);
        }
    }

    private void ConvertExcessMinutesToItems(float excessMinutes)
    {
        // Find the smallest speed-up item
        var allSpeedUps = new List<InventoryItem>();
        allSpeedUps.AddRange(InventorySystem.Instance.GetItemsByTag("TrainingSpeedUp"));
        allSpeedUps.AddRange(InventorySystem.Instance.GetItemsByTag("GeneralSpeedUp"));

        // Sort by value (minutes)
        allSpeedUps.Sort((a, b) => a.itemSO.Value.CompareTo(b.itemSO.Value));

        if (allSpeedUps.Count > 0)
        {
            var smallestItem = allSpeedUps[0];
            int minutesPerItem = smallestItem.itemSO.Value;
            int itemsToAdd = Mathf.FloorToInt(excessMinutes / minutesPerItem);

            if (itemsToAdd > 0)
            {
                // Add items back to inventory
                InventorySystem.Instance.AddItem(smallestItem.itemSO, itemsToAdd);
            }
        }
    }

    private void ShowResourceReplenishmentPanel()
    {
        resourceReplenishmentPanel.SetActive(true);

        // Calculate missing resources
        int missingFood = Mathf.Max(0, selectedTroop.FoodCost * trainingAmount - GameManager.Instance.Food);
        int missingWood = Mathf.Max(0, selectedTroop.WoodCost * trainingAmount - GameManager.Instance.Wood);
        int missingMetal = Mathf.Max(0, selectedTroop.MetalCost * trainingAmount - GameManager.Instance.Metal);

        string missingText = "Missing Resources:";
        if (missingFood > 0) missingText += $"\nFood: {missingFood}";
        if (missingWood > 0) missingText += $"\nWood: {missingWood}";
        if (missingMetal > 0) missingText += $"\nMetal: {missingMetal}";

        missingResourcesText.text = missingText;

        // Check if we have enough resources in inventory
        bool hasEnoughInInventory = HasEnoughResourcesInInventory(missingFood, missingWood, missingMetal);
        replenishButton.interactable = hasEnoughInInventory;
    }

    private void HideResourceReplenishmentPanel()
    {
        resourceReplenishmentPanel.SetActive(false);
    }

    private bool HasEnoughResourcesInInventory(int missingFood, int missingWood, int missingMetal)
    {
        int foodInInventory = GetResourceFromInventory("Food");
        int woodInInventory = GetResourceFromInventory("Wood");
        int metalInInventory = GetResourceFromInventory("Metal");

        return foodInInventory >= missingFood && woodInInventory >= missingWood && metalInInventory >= missingMetal;
    }

    private int GetResourceFromInventory(string resourceType)
    {
        var resources = InventorySystem.Instance.GetItemsByTag(resourceType);
        int total = 0;
        foreach (var item in resources)
        {
            total += item.quantity * item.itemSO.Value;
        }
        return total;
    }

    private void ReplenishResources()
    {
        // Calculate missing resources
        int missingFood = Mathf.Max(0, selectedTroop.FoodCost * trainingAmount - GameManager.Instance.Food);
        int missingWood = Mathf.Max(0, selectedTroop.WoodCost * trainingAmount - GameManager.Instance.Wood);
        int missingMetal = Mathf.Max(0, selectedTroop.MetalCost * trainingAmount - GameManager.Instance.Metal);

        // Use resources from inventory
        UseResourceFromInventory("Food", missingFood);
        UseResourceFromInventory("Wood", missingWood);
        UseResourceFromInventory("Metal", missingMetal);

        // Hide panel
        HideResourceReplenishmentPanel();

        // Update UI
        UpdateResourceTexts();
    }

    private void UseResourceFromInventory(string resourceType, int amount)
    {
        if (amount <= 0) return;

        var resources = InventorySystem.Instance.GetItemsByTag(resourceType);
        int remaining = amount;

        foreach (var item in resources)
        {
            if (remaining <= 0) break;

            int valuePerItem = item.itemSO.Value;
            int itemsNeeded = Mathf.CeilToInt((float)remaining / valuePerItem);
            int itemsToUse = Mathf.Min(itemsNeeded, item.quantity);

            if (itemsToUse > 0)
            {
                int valueUsed = itemsToUse * valuePerItem;
                remaining -= valueUsed;

                // Remove items from inventory
                item.quantity -= itemsToUse;

                // Add resources to GameManager
                switch (resourceType)
                {
                    case "Food":
                        GameManager.Instance.AddResources(valueUsed, 0, 0);
                        break;
                    case "Wood":
                        GameManager.Instance.AddResources(0, valueUsed, 0);
                        break;
                    case "Metal":
                        GameManager.Instance.AddResources(0, 0, valueUsed);
                        break;
                }
            }
        }
    }

    private string FormatTime(float seconds)
    {
        int minutes = Mathf.FloorToInt(seconds / 60);
        int remainingSeconds = Mathf.FloorToInt(seconds % 60);
        return $"{minutes:00}:{remainingSeconds:00}";
    }

    public void Close()
    {
        Debug.Log("Close method called");

        try
        {
            // Reset button interactability
            Debug.Log("Resetting type buttons interactability");
            ResetTypeButtonsInteractability();

            // Hide all panels first
            Debug.Log("Hiding all panels");
            HideAllPanels();

            // Then deactivate the entire UI
            Debug.Log("Deactivating Training UI gameObject");
            gameObject.SetActive(false);
            Debug.Log("Training UI closed");

            // Notify any listeners that the UI has been closed
            if (OnUIClose != null)
            {
                OnUIClose.Invoke();
                Debug.Log("OnUIClose event invoked");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in Close method: {e.Message}\n{e.StackTrace}");

            // Force deactivate as a last resort
            gameObject.SetActive(false);
        }
    }

    // Event that can be subscribed to when the UI is closed
    public System.Action OnUIClose;

    // Reset the interactability of all type selection buttons
    private void ResetTypeButtonsInteractability()
    {
        if (infantryButton != null)
        {
            infantryButton.interactable = true;
        }

        if (riderButton != null)
        {
            riderButton.interactable = true;
        }

        if (rangedButton != null)
        {
            rangedButton.interactable = true;
        }

        Debug.Log("Reset type buttons interactability");
    }
}
