using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class ResearchNodeButton : MonoBehaviour
{
    [SerializeField] private ResearchNode node;
    [SerializeField] private ResearchNodeUI detailUI;
    [SerializeField] private int tierIndex;
    [SerializeField] private GameObject selectionFrame;
    [SerializeField] private TextMesh<PERSON>roUGUI levelText;
    [SerializeField] private Gray<PERSON>le<PERSON>mage nodeImage;
    [SerializeField] private GameObject researchButtonsPanel;
    [SerializeField] private GameObject unavailablePanel;

    // Add reference to glow effects
    [SerializeField] private UIEnergyLine[] glowEffects;
    [SerializeField] private float unlockedGlowIntensity = 80f;
    
    private Button button;
    
    private void Awake()
    {
        button = GetComponent<Button>();
        button.onClick.AddListener(OnButtonClicked);

        // Ensure frame starts inactive
        if (selectionFrame != null)
            selectionFrame.SetActive(false);
    }

    private void Start()
    {
        UpdateButtonState();
    }

    private void OnButtonClicked()
    {
        // Set this button as selected
        SetSelected(true);
        
      
        // Always show the detail panel
        detailUI.gameObject.SetActive(true);
        detailUI.DisplayNodeDetails(node, tierIndex, this);

        // If node is locked, hide research buttons and show unavailable panel
        if (IsNodeLocked())
        {
            if (researchButtonsPanel != null)
                researchButtonsPanel.SetActive(false);
            if (unavailablePanel != null)
                unavailablePanel.SetActive(true);
        }
        else
        {
            if (researchButtonsPanel != null)
                researchButtonsPanel.SetActive(true);
            if (unavailablePanel != null)
                unavailablePanel.SetActive(false);
        }
    }

    public void SetSelected(bool selected)
    {
        if (selectionFrame != null)
            selectionFrame.SetActive(selected);
    }

    public void UpdateButtonState()
    {
        // Get current level from GameManager
        int currentLevel = GameManager.Instance.GetResearchLevel(node, tierIndex);
        
        // Update level text
        if (levelText != null)
        {
            int maxLevel = node.tiers[tierIndex].maxLevel;
            levelText.text = $"{currentLevel}/{maxLevel}";
        }

        // Check if node is locked
        bool isLocked = IsNodeLocked();
        
        // Update grayscale effect
        if (nodeImage != null)
        {
            nodeImage.SetGrayscaleAmount(isLocked ? 1f : 0f);
        }

        // Update glow effects
        if (glowEffects != null)
        {
            foreach (var glowEffect in glowEffects)
            {
                if (glowEffect != null)
                {
                    glowEffect.glowIntensity = isLocked ? 0f : unlockedGlowIntensity;
                }
            }
        }
    }

    private bool IsNodeLocked()
    {
        var prerequisites = node.tiers[tierIndex].prerequisites;
        if (prerequisites == null || prerequisites.Length == 0)
            return false;

        // Check lab level requirement
        int requiredLabLevel = node.tiers[tierIndex].requiredLabLevel;
        int currentLabLevel = GameManager.Instance.GetLabLevel(); // You'll need to add this method to GameManager

        if (currentLabLevel < requiredLabLevel)
            return true;

        // Check prerequisites
        foreach (var prerequisite in prerequisites)
        {
            int prerequisiteLevel = GameManager.Instance.GetResearchLevel(
                prerequisite.node, 
                prerequisite.requiredTier
            );
            
            if (prerequisiteLevel < prerequisite.requiredLevel)
                return true;
        }

        return false;
    }
    
}
