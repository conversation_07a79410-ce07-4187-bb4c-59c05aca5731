using UnityEngine;
using HeroSystem;

public class ZombieStatsManager : MonoBehaviour
{
    [System.Serializable]
    public class ZombieTypeConfig
    {
        public GameObject prefab;
        [Range(0.1f, 5f)]
        public float healthMultiplier = 1f;
        [Range(0.1f, 5f)]
        public float attackMultiplier = 1f;
        [Range(0.1f, 5f)]
        public float defenseMultiplier = 1f;
    }

    [Header("Zombie Prefabs and Base Multipliers")]
    public ZombieTypeConfig slowZombie;
    public ZombieTypeConfig fastZombie;
    public ZombieTypeConfig bossZombie;

    [Header("Level Scaling")]
    [Range(0.001f, 0.1f)]
    public float kValue = 0.015f;

    private int currentLevel;
    private HeroData currentHero;
    private float levelMultiplier;

    void Awake() // Changed from Start to Awake
    {
        // Get current level from PlayerPrefs (set by ExpeditionFPS)
        currentLevel = PlayerPrefs.GetInt("CurrentFPSLevel", 1);
        currentHero = GameManager.Instance?.SelectedHero;

        if (currentHero == null)
        {
            Debug.LogWarning("No hero selected, using default stats!");
            // Provide default stats to prevent null reference
            currentHero = ScriptableObject.CreateInstance<HeroData>();
        }

        CalculateLevelMultiplier();
    }

    private void CalculateLevelMultiplier()
    {
        // M(L) = 1 + k × (L - 1)²
        levelMultiplier = 1f + kValue * Mathf.Pow(currentLevel - 1, 2);
    }

    public ZombieStats GetZombieStats(ZombieType type)
    {
        ZombieTypeConfig config = GetConfigForType(type);

        return new ZombieStats
        {
            maxHealth = Mathf.Round(currentHero.BaseStats.HP * config.healthMultiplier * levelMultiplier),
            attack = Mathf.Round(currentHero.BaseStats.Attack * config.attackMultiplier * levelMultiplier),
            defense = Mathf.Round(currentHero.BaseStats.Defense * config.defenseMultiplier * levelMultiplier)
        };
    }

    public ZombieTypeConfig GetConfigForType(ZombieType type)
    {
        return type switch
        {
            ZombieType.Slow => slowZombie,
            ZombieType.Fast => fastZombie,
            ZombieType.Boss => bossZombie,
            _ => slowZombie
        };
    }

    public int GetZombieCount(ZombieType type)
    {
        return type switch
        {
            ZombieType.Slow => 10 + currentLevel,
            ZombieType.Fast => Mathf.RoundToInt(3 + 0.5f * currentLevel),
            ZombieType.Boss => (currentLevel % 5 == 0) ? 1 : 0,
            _ => 0
        };
    }

    public float GetSpawnInterval(ZombieType type)
    {
        // Base intervals adjusted by level
        float baseInterval = type switch
        {
            ZombieType.Slow => 3.0f,
            ZombieType.Fast => 4.5f,
            ZombieType.Boss => 0f, // Boss spawns immediately when its wave starts
            _ => 3.0f
        };

        // Decrease interval as level increases, but not below minimum
        float levelFactor = Mathf.Max(0.5f, 1f - (currentLevel * 0.02f));
        return Mathf.Max(1.0f, baseInterval * levelFactor);
    }
}

public enum ZombieType
{
    Slow,
    Fast,
    Boss
}

public struct ZombieStats
{
    public float maxHealth;
    public float attack;
    public float defense;
}

