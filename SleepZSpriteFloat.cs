using UnityEngine;

public class SleepZSpriteFloat : MonoBehaviour
{
    public float floatSpeed = 0.5f;
    public float lifetime = 2f;
    public Vector3 positionOffset;
    private float elapsed = 0f;
    private SpriteRenderer sr;
    private Camera cam;
    private Vector3 originalScale;

    void Start()
    {
        sr = GetComponent<SpriteRenderer>();
        cam = Camera.main;
        originalScale = transform.localScale;
        transform.localScale = originalScale * 0.5f;
    }

    void Update()
    {
        elapsed += Time.deltaTime;
        transform.position += (Vector3.up * floatSpeed * Time.deltaTime) + positionOffset;
        float scaleFactor = elapsed / lifetime;
        transform.localScale = Vector3.Lerp(originalScale * 0.5f, originalScale, scaleFactor);

        // Face camera (important if using 3D world space)
        if (cam != null)
           transform.forward = -cam.transform.forward;

        // Fade out
        if (sr != null)
        {
            float alpha = Mathf.Lerp(1f, 0f, elapsed / lifetime);
            Color c = sr.color;
            sr.color = new Color(c.r, c.g, c.b, alpha);
        }

        if (elapsed >= lifetime)
            Destroy(gameObject);
    }
}
