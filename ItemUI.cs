using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class ItemUI : MonoBehaviour
{
    [SerializeField] private Image itemIcon;
    [SerializeField] private Button selectButton;
    [SerializeField] private TextMesh<PERSON><PERSON>UGUI itemName;
    [SerializeField] private TextM<PERSON><PERSON><PERSON><PERSON>G<PERSON> itemQuantity;
    [SerializeField] private Image itemFrame;


    private InventoryItem currentItem;
    private ItemSelector itemSelector;

    public void Initialize(InventoryItem item, ItemSelector selector)
    {
        Debug.Log($"Initializing UI for item: {item.itemSO.Name}, Category: {item.itemSO.Category}, Quantity: {item.quantity}");
        currentItem = item;

        if (itemIcon != null)
            itemIcon.sprite = item.itemSO.Icon;
        if (itemName != null)
            itemName.text = item.itemSO.Name; // Use item.itemName instead of item.name
        if (itemQuantity != null)
            itemQuantity.text = item.quantity.ToString();
    

        itemSelector = selector;
        selectButton.onClick.AddListener(OnItemClicked);

        
    }

    private void OnItemClicked()
    {
        itemSelector.OnItemSelected(currentItem, this);
    }

    public void SetSelected(bool isSelected)
    {
        if (itemFrame != null)
        {
            itemFrame.enabled = isSelected; // Enable or disable the frame
        }

        
    }



}
