using UnityEngine;
using System.Threading.Tasks;
using System.Collections.Generic;

public class RewardManager : MonoBehaviour
{
    public static RewardManager Instance { get; private set; }

    [SerializeField] private RewardPoolSO basicRewardPool;
    [SerializeField] private RewardPoolSO advancedRewardPool;
    private Dictionary<string, InventoryItemSO> loadedRewards = new Dictionary<string, InventoryItemSO>();

    private void Awake()
    {
        Instance = this;
        InitializeRewardPool();
    }

    private void InitializeRewardPool()
    {
        // Initialize basic rewards
        foreach (var reward in basicRewardPool.rewards)
        {
            if (!reward.isAddressable)
            {
                loadedRewards[reward.rewardId] = reward.itemData;
            }
        }

        // Initialize advanced rewards
        foreach (var reward in advancedRewardPool.rewards)
        {
            if (!reward.isAddressable)
            {
                loadedRewards[reward.rewardId] = reward.itemData;
            }
        }
    }

    public async Task<InventoryItemSO> GetReward(string rewardId)
    {
        if (loadedRewards.TryGetValue(rewardId, out InventoryItemSO reward))
        {
            return reward;
        }

        // Check basic pool
        var basicReward = basicRewardPool.rewards.Find(r => r.rewardId == rewardId);
        if (basicReward != null)
        {
            if (basicReward.isAddressable)
            {
                var loadedReward = await LoadAddressableReward(basicReward.addressablePath);
                loadedRewards[rewardId] = loadedReward;
                return loadedReward;
            }
            return basicReward.itemData;
        }

        // Check advanced pool
        var advancedReward = advancedRewardPool.rewards.Find(r => r.rewardId == rewardId);
        if (advancedReward != null)
        {
            if (advancedReward.isAddressable)
            {
                var loadedReward = await LoadAddressableReward(advancedReward.addressablePath);
                loadedRewards[rewardId] = loadedReward;
                return loadedReward;
            }
            return advancedReward.itemData;
        }

        return null;
    }

    public void GrantReward(string rewardId, int amount)
    {
        var reward = GetReward(rewardId).Result;
        if (reward != null && CheckRewardConditions(rewardId))
        {
            InventorySystem.Instance.AddItem(reward, amount);
        }
    }

    private bool CheckRewardConditions(string rewardId)
    {
        var basicRewardEntry = basicRewardPool.rewards.Find(r => r.rewardId == rewardId);
        var advancedRewardEntry = advancedRewardPool.rewards.Find(r => r.rewardId == rewardId);
        var rewardEntry = basicRewardEntry ?? advancedRewardEntry;

        if (rewardEntry == null) return false;

        foreach (var condition in rewardEntry.conditions)
        {
            switch (condition.type)
            {
                case RewardPoolSO.ConditionType.HQLevel:
                    BuildingUpgrade hq = GameObject.FindGameObjectWithTag("HQ").GetComponent<BuildingUpgrade>();
                    if (hq.CurrentLevel < condition.requiredValue)
                        return false;
                    break;
                case RewardPoolSO.ConditionType.TimeGated:
                    var now = System.DateTime.Now;
                    if (now < condition.availableFrom || now > condition.availableUntil)
                        return false;
                    break;
            }
        }
        return true;
    }



    private Task<InventoryItemSO> LoadAddressableReward(string addressablePath)
    {
        // Implement Addressables loading logic
        return Task.FromResult<InventoryItemSO>(null);
    }
}
