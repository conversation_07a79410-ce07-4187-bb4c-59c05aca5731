using UnityEngine;

public enum TroopType
{
    Infantry,
    Rider,
    Ranged
}

[CreateAssetMenu(fileName = "NewTroop", menuName = "Training System/Troop")]
public class TroopSO : ScriptableObject
{
    [Header("Basic Information")]
    public TroopType Type;
    public string Name;
    [TextArea(3, 5)]
    public string Description;
    public Sprite Image;
    public Sprite Thumbnail;
    [Range(1, 10)]
    public int Level;

    [Header("Combat Stats")]
    public int Attack;
    public int Defense;
    public int Health;

    [Header("Training Requirements")]
    public int FoodCost;
    public int WoodCost;
    public int MetalCost;
    public float TrainingTime;
    public int BattlePower;

    // Required building level to unlock this troop level
    public int RequiredBuildingLevel => (Level - 1) * 3 + 1;

    // Calculate combat stats based on level and troop type
    public void CalculateStats()
    {
        switch (Type)
        {
            case TroopType.Infantry:
                Attack = Level * Level + 20 * Level + 24;
                Defense = Level * Level + 20 * Level + 86;
                Health = Level * Level + 20 * Level + 87;
                break;
            case TroopType.Rider:
                Attack = Level * Level + 20 * Level + 28;
                Defense = Level * Level + 20 * Level + 85;
                Health = Level * Level + 20 * Level + 82;
                break;
            case TroopType.Ranged:
                Attack = Level * Level + 20 * Level + 35;
                Defense = Level * Level + 20 * Level + 74;
                Health = Level * Level + 20 * Level + 75;
                break;
        }
    }

    // Calculate training time based on level
    public void CalculateTrainingTime()
    {
        // Make sure Level is at least 1
        int safeLevel = Mathf.Max(1, Level);

        // Calculate training time using the formula: time = 6 * level * level + 6
        float calculatedTime = 6f * safeLevel * safeLevel + 6f;

        // Ensure the training time is at least 12 seconds (minimum value from formula with level 1)
        TrainingTime = Mathf.Max(12f, calculatedTime);

        Debug.Log($"TroopSO.CalculateTrainingTime: {Name} (Level {safeLevel}) - Formula: 6 * {safeLevel} * {safeLevel} + 6 = {calculatedTime}, Final time: {TrainingTime}");
    }

    // Calculate battle power based on level
    public void CalculateBattlePower()
    {
        BattlePower = (int)Mathf.Round(Mathf.Pow(1.5f, Level - 1));
    }

    // Calculate resource requirements based on troop type and level
    public void CalculateResourceRequirements()
    {
        switch (Type)
        {
            case TroopType.Infantry:
                FoodCost = 40 * Level + 20 * Level * Level;
                WoodCost = FoodCost; // Same as food
                MetalCost = Level > 2 ? 25 * (Level - 2) * (Level - 2) : 0;
                break;
            case TroopType.Rider:
                FoodCost = Level > 2 ? 30 * (Level - 1) * (Level - 1) : 0;
                WoodCost = 50 * Level + 20 * Level * Level;
                MetalCost = Level > 1 ? 25 * (Level - 1) * (Level - 1) : 0;
                break;
            case TroopType.Ranged:
                FoodCost = 50 * Level + 20 * Level * Level;
                WoodCost = Level <= 2 ? 30 * (3 - Level) * (3 - Level) : 0;
                MetalCost = Level > 1 ? 25 * (Level - 1) * (Level - 1) : 0;
                break;
        }
    }

    // Calculate all stats and requirements
    public void CalculateAll()
    {
        CalculateStats();
        CalculateTrainingTime();
        CalculateBattlePower();
        CalculateResourceRequirements();
    }

    // This is called when the scriptable object is created or modified in the editor
    private void OnValidate()
    {
        CalculateAll();
    }
}
