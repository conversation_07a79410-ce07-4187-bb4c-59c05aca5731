using UnityEngine;
using System.Collections.Generic;

public class Player : MonoBehaviour
{
    public string playerId;
    public List<InventoryItem> inventory = new List<InventoryItem>();

    public void AddItem(InventoryItemSO itemSO)
    {
        InventoryItem item = new InventoryItem(itemSO); // Initialize using the constructor that takes InventoryItemSO
        inventory.Add(item);
        // Update UI or notify the server
    }

    public void RemoveItem(InventoryItem item)
    {
        inventory.Remove(item);
        // Update UI or notify the server
    }

    public List<InventoryItem> GetItemsByCategory(ItemCategory category)
    {
        return inventory.FindAll(item => item.itemSO.Category == category);
    }
}
