# GameDataManager

## Overview

The GameDataManager is a non-MonoBehaviour class that handles all game data persistence using SQLite. It replaces the previous PlayerPrefs-based data storage system with a more robust and scalable solution.

## Key Features

- **SQLite Database**: Uses SQLite for efficient and reliable data storage
- **Singleton Pattern**: Accessible via `GameDataManager.Instance`
- **Non-MonoBehaviour**: Works independently of the Unity scene lifecycle
- **Data Migration**: Includes utilities to migrate existing PlayerPrefs data to SQLite
- **Transaction Support**: Ensures data integrity for related operations

## Database Schema

The GameDataManager creates the following tables:

- **Resources**: Stores gold, food, wood, metal, battle power, and welfare
- **Heroes**: Stores hero progress including rank, level, experience, and lock status
- **HeroSkills**: Stores hero skill levels and bonuses
- **Buildings**: Stores building data including level and position
- **BuildingCapabilities**: Stores building capability values
- **CompletedResearch**: Stores completed research nodes
- **ActiveResearch**: Stores in-progress research
- **FPSGameProgress**: Stores FPS game progress
- **FPSLevelStats**: Stores statistics for each FPS level
- **FPSLevelHeroes**: Stores which heroes completed each level
- **InventoryItems**: Stores inventory item quantities
- **TroopCounts**: Stores troop counts by type and level

## How to Use

### Initialization

The GameDataManager is automatically initialized by the GameManager. You don't need to create an instance manually.

```csharp
// Access the GameDataManager instance
GameDataManager dataManager = GameDataManager.Instance;
```

### Resource Management

```csharp
// Get resources
dataManager.GetResources(out int gold, out int food, out int wood, out int metal, out int battlePower, out int welfare);

// Update resources
dataManager.UpdateResources(1000, 5000, 5000, 5000, 100, 50);
```

### Hero Management

```csharp
// Save hero progress
List<SkillProgressData> skills = new List<SkillProgressData>();
// Add skill data...
dataManager.SaveHeroProgress("HeroName", 2, 10, 500, 20, false, skills);

// Get hero progress
HeroProgressData heroProgress = dataManager.GetHeroProgress("HeroName");

// Get all heroes progress
List<HeroProgressData> allHeroes = dataManager.GetAllHeroesProgress();
```

### Building Management

```csharp
// Save building
dataManager.SaveBuilding(buildingId, "Farm", 3, new Vector3(10, 0, 10), 90);

// Save building capability
dataManager.SaveBuildingCapability(buildingId, (int)BuildingCapabilityType.ResourceProduction_Food, 300);

// Get building
BuildingData building = dataManager.GetBuilding(buildingId);

// Get building capabilities
Dictionary<int, float> capabilities = dataManager.GetBuildingCapabilities(buildingId);

// Delete building
dataManager.DeleteBuilding(buildingId);
```

### Research Management

```csharp
// Save completed research
dataManager.SaveCompletedResearch((int)nodeType, (int)bonusType, tierIndex, level);

// Save active research
dataManager.SaveActiveResearch((int)nodeType, (int)bonusType, tierIndex, currentLevel, remainingTime);

// Get completed research level
int level = dataManager.GetCompletedResearchLevel((int)nodeType, (int)bonusType, tierIndex);

// Get active research
ActiveResearchData research = dataManager.GetActiveResearch((int)nodeType, (int)bonusType, tierIndex);

// Clear active research
dataManager.ClearActiveResearch((int)nodeType, (int)bonusType, tierIndex);
```

### FPS Game Progress

```csharp
// Save FPS progress
dataManager.SaveFPSProgress(highestLevelReached);

// Get highest FPS level reached
int level = dataManager.GetHighestFPSLevelReached();

// Save FPS level stats
dataManager.SaveFPSLevelStats(level, timesCompleted, bestTime, highestScore);

// Add hero to FPS level completion
dataManager.AddHeroToFPSLevel(level, heroName);

// Get FPS level stats
FPSLevelStatsData stats = dataManager.GetFPSLevelStats(level);
```

### Inventory Management

```csharp
// Save inventory item
dataManager.SaveInventoryItem(itemId, quantity);

// Get inventory item quantity
int quantity = dataManager.GetInventoryItemQuantity(itemId);

// Get all inventory items
Dictionary<string, int> items = dataManager.GetAllInventoryItems();
```

### Troop Management

```csharp
// Save troop count
dataManager.SaveTroopCount((int)troopType, level, count);

// Get troop count
int count = dataManager.GetTroopCount((int)troopType, level);

// Get all troop counts
Dictionary<int, Dictionary<int, int>> troops = dataManager.GetAllTroopCounts();
```

### Data Migration

```csharp
// Migrate data from PlayerPrefs to SQLite
dataManager.MigrateFromPlayerPrefs();
```

## Implementation Details

### GameDataManager.cs

This is the main class that handles all database operations. It's a singleton that can be accessed via `GameDataManager.Instance`.

### GameManagerExtension.cs

This is a partial class that extends the existing GameManager to use the GameDataManager for data persistence. It overrides the necessary methods to save and load data using the GameDataManager.

## Best Practices

1. **Use Transactions**: When performing multiple related operations, use transactions to ensure data integrity.
2. **Close the Connection**: Call `dataManager.Close()` when you're done with the database to free up resources.
3. **Error Handling**: Always handle exceptions that might occur during database operations.
4. **Data Migration**: Use the provided migration utilities to migrate existing PlayerPrefs data to SQLite.

## Advantages Over PlayerPrefs

1. **Performance**: SQLite is more efficient for storing and retrieving large amounts of data.
2. **Data Integrity**: SQLite supports transactions, which ensure that related operations are performed atomically.
3. **Flexibility**: SQLite supports complex queries and relationships between data.
4. **Scalability**: SQLite can handle much larger amounts of data than PlayerPrefs.
5. **Security**: SQLite data is stored in a binary format, making it more secure than PlayerPrefs.
