using UnityEngine;

public class PlayerHealth : MonoBehaviour
{
    public int maxHealth = 100;
    private int currentHealth;

    public int CurrentHealth => currentHealth;

    void Start()
    {
        // maxHealth will be set by FPSGameUI based on hero stats
        currentHealth = maxHealth;
    }

    public void TakeDamage(int damage)
    {
        currentHealth -= damage;
        
        if (currentHealth <= 0)
        {
            Die();
        }
    }

    public bool IsDead()
    {
        return currentHealth <= 0;
    }

    void Die()
    {
        Debug.Log("Player is dead!");
        // Return to Settlement scene since we failed
        UnityEngine.SceneManagement.SceneManager.LoadScene("Settlement");
    }
}
