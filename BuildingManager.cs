using System.Collections.Generic;
using UnityEngine;

public class BuildingManager : MonoBehaviour
{
    [System.Serializable]
    public class BuildingLimit
    {
        public GameObject buildingPrefab; // Assign the building prefab
        public int maxCount; // Set the max number of instances
    }

    public List<BuildingLimit> buildingLimitsList = new List<BuildingLimit>(); // Set limits in Inspector
    private Dictionary<GameObject, int> buildingLimits = new Dictionary<GameObject, int>();
    private Dictionary<GameObject, int> placedBuildings = new Dictionary<GameObject, int>();

    public LayerMask groundLayer;
    public LayerMask buildingLayer;
    public LayerMask wallLayer;
    public MenuController menuController;
    public float gridSize = 1f;
    public GameObject smokeEffectPrefab;
    public Grid buildingGrid;
    public GameObject errorUIPrefab; // Assign an error UI prefab in Inspector

    private GameObject selectedBuildingPrefab;
    private GameObject currentBuildingPreview;
    private bool isDragging = false;


    private void Start()
    {
        InitializeBuildingLimits();




        if (Input.GetMouseButtonDown(1) || Input.GetKeyDown(KeyCode.Escape))
        {
            Debug.Log("Cancelling due to Escape or Right Click...");
        }


    }

    private void InitializeBuildingLimits()
    {
        foreach (var limit in buildingLimitsList)
        {
            if (limit.buildingPrefab != null)
            {
                buildingLimits[limit.buildingPrefab] = limit.maxCount;
                placedBuildings[limit.buildingPrefab] = 0; // Start with 0 placed buildings
            }
        }
    }

    public void SetBuildingPrefab(GameObject prefab)
    {
        if (prefab == null)
        {
            Debug.LogError("Prefab is null!");
            return;
        }

        if (buildingLimits.ContainsKey(prefab) && placedBuildings[prefab] >= buildingLimits[prefab])
        {
            Debug.LogWarning($"Cannot place {prefab.name}. Limit reached!");
            ShowErrorUI($"{prefab.name} limit reached!");
            return;
        }

        Debug.Log($"Setting selected building prefab to {prefab.name}");
        selectedBuildingPrefab = prefab;

        if (menuController != null)
        {
            menuController.CloseMenu();
        }

        StartPlacingBuilding();
    }

    private void StartPlacingBuilding()
    {
        if (selectedBuildingPrefab == null)
        {
            Debug.LogError("No building prefab selected!");
            return;
        }

        Debug.Log("Starting to place building...");

        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
        {
            Vector3 snappedPosition = SnapToGrid(hit.point);
            Debug.Log($"Instantiating preview at {snappedPosition}");

            // Ensure we don't overwrite previous buildings
            if (currentBuildingPreview != null)
            {
                Debug.LogError($"WARNING: currentBuildingPreview already exists! It is {currentBuildingPreview.name}");
                Destroy(currentBuildingPreview); // Destroy the existing preview
            }

            currentBuildingPreview = Instantiate(selectedBuildingPrefab, snappedPosition, Quaternion.identity);

            if (currentBuildingPreview == null)
            {
                Debug.LogError("Failed to instantiate building preview!");
                return;
            }

            ApplyPreviewMaterial(currentBuildingPreview, true);
            isDragging = true;
            Debug.Log("Building preview instantiated successfully.");
        }
        else
        {
            Debug.LogWarning("Failed to raycast ground layer.");
        }
    }



    void Update()
    {
        if (isDragging)
        {
            Debug.Log("Dragging building preview...");
            UpdateBuildingPreview();

            if (Input.GetMouseButtonDown(0))
            {
                Debug.Log("Left mouse button clicked. Ending drag...");
                EndDragging();
            }
            else if (Input.GetMouseButtonDown(1) || Input.GetKeyDown(KeyCode.Escape))
            {
                Debug.Log("Right mouse button or Escape pressed. Canceling drag...");
                CancelDragging();
            }
        }


    }

    private void UpdateBuildingPreview()
    {
        if (!currentBuildingPreview.activeInHierarchy)
        {
            Debug.LogError("currentBuildingPreview was disabled!");
        }

        if (currentBuildingPreview == null) return;

        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
        {
            Vector3 snappedPosition = SnapToGrid(hit.point);
            bool isValid = IsPlacementValid(snappedPosition);
            currentBuildingPreview.transform.position = snappedPosition;
            ApplyPreviewMaterial(currentBuildingPreview, isValid);
        }
    }

    private void EndDragging()
    {
        if (currentBuildingPreview == null)
        {
            Debug.LogError("Current building preview is null before finalizing placement!");
            return;
        }

        Debug.Log("Ending drag and placing building...");
        Vector3 snappedPosition = currentBuildingPreview.transform.position;

        if (IsPlacementValid(snappedPosition))
        {
            // Instantiate the final building
            GameObject newBuilding = Instantiate(selectedBuildingPrefab, snappedPosition, Quaternion.identity);

            // Reset the material of the final building
            Renderer finalRenderer = newBuilding.GetComponent<Renderer>();
            if (finalRenderer != null)
            {
                // Reset to the default material (or use a specific material if needed)
                finalRenderer.material.color = Color.white; // Or use the prefab's original material
            }

            if (newBuilding == null)
            {
                Debug.LogError("Failed to instantiate final building!");
                return;
            }

            // The building should already have a BuildingMenu component from the prefab
            BuildingMenu buildingMenu = newBuilding.GetComponent<BuildingMenu>();
            if (buildingMenu != null)
            {
                // Make sure the menu is hidden initially
                if (buildingMenu.menuObject != null)
                {
                    buildingMenu.HideMenu();
                }
            }
            else
            {
                // This should not happen if all prefabs have the component
                Debug.LogError($"Building {newBuilding.name} does not have a BuildingMenu component. Check your prefab setup.");
            }

            if (placedBuildings.ContainsKey(selectedBuildingPrefab))
            {
                placedBuildings[selectedBuildingPrefab]++;
                Debug.Log($"Placed {selectedBuildingPrefab.name}. Total placed: {placedBuildings[selectedBuildingPrefab]}");
            }
            else
            {
                Debug.LogError($"Building prefab {selectedBuildingPrefab.name} not found in placedBuildings dictionary.");
            }

            ShowSmokeEffect(snappedPosition);
        }
        else
        {
            Debug.LogWarning("Cannot place building here; invalid position.");
        }

        // Destroy the preview object
        if (currentBuildingPreview != null)
        {
            Debug.Log($"Destroying preview object: {currentBuildingPreview.name}");
            Destroy(currentBuildingPreview);
            currentBuildingPreview = null;
            isDragging = false;
            Debug.Log("Building placement completed.");
        }
    }


    private void CancelDragging()
    {
        if (currentBuildingPreview == null)
        {
            Debug.LogWarning("CancelDragging called, but no preview exists.");
            return;
        }
        if (currentBuildingPreview != null)
        {
            Debug.LogError($"Destroying object: {currentBuildingPreview.name}");
            Destroy(currentBuildingPreview);
            currentBuildingPreview = null;
            isDragging = false;
            Debug.Log("Building placement completed.");
        }

    }


    private Vector3 SnapToGrid(Vector3 position)
    {
        if (buildingGrid == null)
        {
            Debug.LogError("Building grid is not assigned!");
            return position;
        }

        Vector3Int cell = buildingGrid.WorldToCell(position);
        Vector3 snappedPosition = buildingGrid.CellToWorld(cell) + new Vector3(gridSize / 2, 0.01f, gridSize / 2);
        Debug.Log($"Snapped position: {snappedPosition}");
        return snappedPosition;
    }

    private bool IsPlacementValid(Vector3 position)
    {
        Vector3 size = GetBuildingSize();
        Collider[] wallColliders = Physics.OverlapBox(position, size / 2, Quaternion.identity, wallLayer);
        if (wallColliders.Length > 0)
        {
            Debug.Log("Invalid placement: Collision with wall.");
            return false;
        }

        Collider[] buildingColliders = Physics.OverlapBox(position, size / 2, Quaternion.identity, buildingLayer);
        foreach (var collider in buildingColliders)
        {
            if (collider.gameObject != currentBuildingPreview)
            {
                Debug.Log("Invalid placement: Collision with another building.");
                return false;
            }
        }
        Debug.Log("Placement is valid.");
        return true;
    }

    private Vector3 GetBuildingSize()
    {
        if (selectedBuildingPrefab != null)
        {
            BoxCollider collider = selectedBuildingPrefab.GetComponent<BoxCollider>();
            if (collider != null) return collider.size;
        }
        return Vector3.one;
    }

    private void ApplyPreviewMaterial(GameObject preview, bool isValid)
    {
        Renderer renderer = preview.GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material.color = isValid ? new Color(0, 1, 0, 0.5f) : new Color(1, 0, 0, 0.5f);
        }
    }

    public void ShowSmokeEffect(Vector3 position)
    {
        if (smokeEffectPrefab != null)
        {
            GameObject smoke = Instantiate(smokeEffectPrefab, position, Quaternion.identity);
            Destroy(smoke, 2f);
        }
    }

    private void ShowErrorUI(string message)
    {
        if (errorUIPrefab != null)
        {
            GameObject errorUI = Instantiate(errorUIPrefab);
            ErrorUIController errorController = errorUI.GetComponent<ErrorUIController>();
            if (errorController != null)
            {
                errorController.ShowMessage(message);
            }
            Destroy(errorUI, 2f);
        }
    }
}
