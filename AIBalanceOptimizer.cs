using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Text;

public class AIBalanceOptimizer : MonoBehaviour
{
    [Header("References")]
    public GameBalanceData balanceData;
    public BalanceAnalyzer balanceAnalyzer;
    
    [Header("AI Optimization Settings")]
    public string aiPromptTemplate = "Analyze the following game balance data and suggest improvements:\n\n{0}\n\nFocus on these aspects:\n- Hero power balance between different types and rarities\n- Building upgrade cost vs. benefit ratio\n- Research time vs. value ratio\n- Overall progression curve\n- Resource generation rates\n\nProvide specific numerical suggestions for parameters that should be adjusted.";
    public string aiResponseFilePath = "ai_balance_suggestions.txt";
    
    [Header("Optimization Goals")]
    [Range(0, 1)]
    public float heroBalanceWeight = 0.2f;
    [Range(0, 1)]
    public float buildingBalanceWeight = 0.2f;
    [Range(0, 1)]
    public float researchBalanceWeight = 0.2f;
    [Range(0, 1)]
    public float progressionCurveWeight = 0.2f;
    [Range(0, 1)]
    public float resourceBalanceWeight = 0.2f;
    
    [Header("Optimization Constraints")]
    [Range(0, 1)]
    public float maxParameterChangePercent = 0.3f; // Maximum change allowed for any parameter (30%)
    public bool preserveRelativeHeroPower = true; // Maintain relative power between hero rarities
    public bool preserveBuildingHierarchy = true; // Maintain building upgrade hierarchy
    
    [Header("Optimization Results")]
    public List<ParameterChange> suggestedChanges = new List<ParameterChange>();
    public float overallBalanceScore = 0f; // 0-1 scale, higher is better
    
    // Generate AI prompt for balance optimization
    public string GenerateAIPrompt()
    {
        if (balanceData == null)
        {
            Debug.LogError("No balance data assigned!");
            return string.Empty;
        }
        
        StringBuilder dataDescription = new StringBuilder();
        
        // Add hero data
        dataDescription.AppendLine("## Hero Data");
        foreach (var hero in balanceData.heroBalanceData)
        {
            dataDescription.AppendLine($"Hero: {hero.heroName}");
            dataDescription.AppendLine($"  Type: {hero.heroType}");
            dataDescription.AppendLine($"  Rarity: {hero.rarity}");
            dataDescription.AppendLine($"  Initial Power: {hero.initialPower}");
            dataDescription.AppendLine($"  Base Stats: Attack={hero.baseStats.Attack}, Defense={hero.baseStats.Defense}, HP={hero.baseStats.HP}, MarchCapacity={hero.baseStats.MarchCapacity}");
            dataDescription.AppendLine($"  Troop Modifiers: Attack+{hero.troopModifiers.AttackBonus}%, Defense+{hero.troopModifiers.DefenseBonus}%, HP+{hero.troopModifiers.HpBonus}%");
            dataDescription.AppendLine();
        }
        
        // Add building data
        dataDescription.AppendLine("## Building Data");
        foreach (var building in balanceData.buildingBalanceData)
        {
            dataDescription.AppendLine($"Building: {building.buildingName}");
            dataDescription.AppendLine($"  Type: {building.buildingType}");
            dataDescription.AppendLine($"  Max Level: {building.maxLevel}");
            dataDescription.AppendLine($"  Base Costs: Food={building.baseFoodCost}, Wood={building.baseWoodCost}, Metal={building.baseMetalCost}, Gold={building.baseGoldCost}");
            dataDescription.AppendLine($"  Base Values: UpgradeTime={building.baseUpgradeTime}s, BattlePower={building.baseBattlePower}, Welfare={building.baseWelfare}, ResourceGen={building.baseResourceGeneration}");
            dataDescription.AppendLine($"  Scaling Factors: Resource={building.resourceGrowthFactor}, Time={building.timeGrowthFactor}, BP={building.battlePowerGrowthFactor}, Welfare={building.welfareGrowthFactor}, ResourceGen={building.resourceGenerationGrowthFactor}");
            dataDescription.AppendLine();
        }
        
        // Add research data
        dataDescription.AppendLine("## Research Data");
        foreach (var research in balanceData.researchBalanceData)
        {
            dataDescription.AppendLine($"Research: {research.researchName}");
            dataDescription.AppendLine($"  Type: {research.nodeType}, Bonus: {research.bonusType} ({(research.isPercentageBonus ? "%" : "flat")})");
            dataDescription.AppendLine($"  Base Costs: Food={research.startingFoodCost}, Wood={research.startingWoodCost}, Metal={research.startingMetalCost}, Gold={research.startingGoldCost}");
            dataDescription.AppendLine($"  Base Values: Time={research.startingTime}s, Power={research.startingPower}");
            
            if (research.tiers.Count > 0)
            {
                dataDescription.AppendLine($"  Tiers: {research.tiers.Count}");
                foreach (var tier in research.tiers)
                {
                    dataDescription.AppendLine($"    Tier {tier.tierNumber}: MaxLevel={tier.maxLevel}, Bonus={tier.bonus}, LabLevel={tier.requiredLabLevel}");
                }
            }
            
            dataDescription.AppendLine();
        }
        
        // Add resource generation data
        dataDescription.AppendLine("## Resource Generation");
        dataDescription.AppendLine($"Food Generation Rate: {balanceData.resourceGeneration.foodGenerationRate}");
        dataDescription.AppendLine($"Wood Generation Rate: {balanceData.resourceGeneration.woodGenerationRate}");
        dataDescription.AppendLine($"Metal Generation Rate: {balanceData.resourceGeneration.metalGenerationRate}");
        dataDescription.AppendLine($"Gold Generation Rate: {balanceData.resourceGeneration.goldGenerationRate}");
        dataDescription.AppendLine();
        
        // Add upgrade scaling data
        dataDescription.AppendLine("## Upgrade Scaling");
        dataDescription.AppendLine($"Hero Rank Scaling: {balanceData.upgradeScaling.heroRankScaling}");
        dataDescription.AppendLine($"Building Upgrade Time Scaling: {balanceData.upgradeScaling.buildingUpgradeTimeScaling}");
        dataDescription.AppendLine($"Building Resource Cost Scaling: {balanceData.upgradeScaling.buildingResourceCostScaling}");
        dataDescription.AppendLine($"Research Time Scaling: {balanceData.upgradeScaling.researchTimeScaling}");
        dataDescription.AppendLine($"Research Cost Scaling: {balanceData.upgradeScaling.researchCostScaling}");
        
        // Format the final prompt
        string prompt = string.Format(aiPromptTemplate, dataDescription.ToString());
        
        return prompt;
    }
    
    // Export AI prompt to a text file
    public void ExportAIPrompt()
    {
        string prompt = GenerateAIPrompt();
        
        if (string.IsNullOrEmpty(prompt))
        {
            Debug.LogError("Failed to generate AI prompt!");
            return;
        }
        
        string filePath = Path.Combine(Application.dataPath, "ai_balance_prompt.txt");
        File.WriteAllText(filePath, prompt);
        
        Debug.Log($"AI prompt exported to: {filePath}");
    }
    
    // Import AI suggestions from a text file
    public void ImportAISuggestions()
    {
        string filePath = Path.Combine(Application.dataPath, aiResponseFilePath);
        
        if (!File.Exists(filePath))
        {
            Debug.LogError($"AI response file not found at: {filePath}");
            return;
        }
        
        string aiResponse = File.ReadAllText(filePath);
        
        if (string.IsNullOrEmpty(aiResponse))
        {
            Debug.LogError("AI response is empty!");
            return;
        }
        
        // Parse AI suggestions
        ParseAISuggestions(aiResponse);
        
        Debug.Log($"AI suggestions imported from: {filePath}");
    }
    
    // Parse AI suggestions from text
    private void ParseAISuggestions(string aiResponse)
    {
        suggestedChanges.Clear();
        
        // This is a simplified parser that looks for specific patterns in the AI response
        // In a real implementation, you would use a more robust parsing approach
        
        string[] lines = aiResponse.Split('\n');
        
        foreach (string line in lines)
        {
            // Look for lines with parameter changes
            if (line.Contains(":") && (line.Contains("change") || line.Contains("adjust") || 
                                       line.Contains("increase") || line.Contains("decrease") ||
                                       line.Contains("set to")))
            {
                try
                {
                    // Try to extract parameter name and new value
                    string[] parts = line.Split(':');
                    if (parts.Length >= 2)
                    {
                        string paramName = parts[0].Trim();
                        string suggestion = parts[1].Trim();
                        
                        // Try to extract a numeric value
                        float newValue = ExtractNumericValue(suggestion);
                        
                        if (!float.IsNaN(newValue))
                        {
                            ParameterChange change = new ParameterChange
                            {
                                parameterName = paramName,
                                suggestedValue = newValue,
                                originalValue = GetCurrentParameterValue(paramName),
                                changeDescription = suggestion,
                                isPercentage = suggestion.Contains("%")
                            };
                            
                            suggestedChanges.Add(change);
                        }
                    }
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"Failed to parse suggestion: {line}. Error: {e.Message}");
                }
            }
        }
        
        Debug.Log($"Parsed {suggestedChanges.Count} parameter change suggestions.");
    }
    
    // Extract numeric value from a text suggestion
    private float ExtractNumericValue(string text)
    {
        // This is a simplified extractor that looks for numbers in the text
        // In a real implementation, you would use a more robust approach
        
        try
        {
            // Look for patterns like "to X" or "by X%" or just a number
            string[] words = text.Split(' ');
            
            foreach (string word in words)
            {
                string numStr = word.Trim(',', '.', '%', ')', '(');
                float value;
                if (float.TryParse(numStr, out value))
                {
                    return value;
                }
            }
        }
        catch
        {
            // Ignore parsing errors
        }
        
        return float.NaN;
    }
    
    // Get current value of a parameter by name
    private float GetCurrentParameterValue(string paramName)
    {
        // This is a simplified lookup that matches parameter names to values
        // In a real implementation, you would use a more robust approach
        
        // Check global parameters
        if (paramName.Contains("globalResourceMultiplier")) return balanceData.globalResourceMultiplier;
        if (paramName.Contains("globalTimeMultiplier")) return balanceData.globalTimeMultiplier;
        if (paramName.Contains("globalPowerMultiplier")) return balanceData.globalPowerMultiplier;
        
        // Check upgrade scaling parameters
        if (paramName.Contains("heroRankScaling")) return balanceData.upgradeScaling.heroRankScaling;
        if (paramName.Contains("buildingUpgradeTimeScaling")) return balanceData.upgradeScaling.buildingUpgradeTimeScaling;
        if (paramName.Contains("buildingResourceCostScaling")) return balanceData.upgradeScaling.buildingResourceCostScaling;
        if (paramName.Contains("researchTimeScaling")) return balanceData.upgradeScaling.researchTimeScaling;
        if (paramName.Contains("researchCostScaling")) return balanceData.upgradeScaling.researchCostScaling;
        
        // Check resource generation parameters
        if (paramName.Contains("foodGenerationRate")) return balanceData.resourceGeneration.foodGenerationRate;
        if (paramName.Contains("woodGenerationRate")) return balanceData.resourceGeneration.woodGenerationRate;
        if (paramName.Contains("metalGenerationRate")) return balanceData.resourceGeneration.metalGenerationRate;
        if (paramName.Contains("goldGenerationRate")) return balanceData.resourceGeneration.goldGenerationRate;
        
        // For other parameters, we would need more complex parsing
        // This is just a placeholder
        return 0f;
    }
    
    // Apply suggested changes to the balance data
    public void ApplySuggestedChanges()
    {
        if (suggestedChanges.Count == 0)
        {
            Debug.LogWarning("No suggested changes to apply!");
            return;
        }
        
        int appliedChanges = 0;
        
        foreach (var change in suggestedChanges)
        {
            // Skip changes that exceed the maximum allowed change percentage
            if (change.originalValue > 0 && 
                Math.Abs(change.suggestedValue - change.originalValue) / change.originalValue > maxParameterChangePercent)
            {
                Debug.LogWarning($"Skipping change to {change.parameterName} as it exceeds the maximum allowed change percentage.");
                continue;
            }
            
            // Apply the change
            bool success = ApplyParameterChange(change);
            
            if (success)
            {
                appliedChanges++;
            }
        }
        
        Debug.Log($"Applied {appliedChanges} out of {suggestedChanges.Count} suggested changes.");
        
        // Recalculate balance score
        CalculateBalanceScore();
    }
    
    // Apply a specific parameter change
    private bool ApplyParameterChange(ParameterChange change)
    {
        // This is a simplified implementation that applies changes to specific parameters
        // In a real implementation, you would use a more robust approach
        
        try
        {
            // Apply to global parameters
            if (change.parameterName.Contains("globalResourceMultiplier"))
            {
                balanceData.globalResourceMultiplier = change.suggestedValue;
                return true;
            }
            
            if (change.parameterName.Contains("globalTimeMultiplier"))
            {
                balanceData.globalTimeMultiplier = change.suggestedValue;
                return true;
            }
            
            if (change.parameterName.Contains("globalPowerMultiplier"))
            {
                balanceData.globalPowerMultiplier = change.suggestedValue;
                return true;
            }
            
            // Apply to upgrade scaling parameters
            if (change.parameterName.Contains("heroRankScaling"))
            {
                balanceData.upgradeScaling.heroRankScaling = change.suggestedValue;
                return true;
            }
            
            if (change.parameterName.Contains("buildingUpgradeTimeScaling"))
            {
                balanceData.upgradeScaling.buildingUpgradeTimeScaling = change.suggestedValue;
                return true;
            }
            
            if (change.parameterName.Contains("buildingResourceCostScaling"))
            {
                balanceData.upgradeScaling.buildingResourceCostScaling = change.suggestedValue;
                return true;
            }
            
            if (change.parameterName.Contains("researchTimeScaling"))
            {
                balanceData.upgradeScaling.researchTimeScaling = change.suggestedValue;
                return true;
            }
            
            if (change.parameterName.Contains("researchCostScaling"))
            {
                balanceData.upgradeScaling.researchCostScaling = change.suggestedValue;
                return true;
            }
            
            // Apply to resource generation parameters
            if (change.parameterName.Contains("foodGenerationRate"))
            {
                balanceData.resourceGeneration.foodGenerationRate = change.suggestedValue;
                return true;
            }
            
            if (change.parameterName.Contains("woodGenerationRate"))
            {
                balanceData.resourceGeneration.woodGenerationRate = change.suggestedValue;
                return true;
            }
            
            if (change.parameterName.Contains("metalGenerationRate"))
            {
                balanceData.resourceGeneration.metalGenerationRate = change.suggestedValue;
                return true;
            }
            
            if (change.parameterName.Contains("goldGenerationRate"))
            {
                balanceData.resourceGeneration.goldGenerationRate = change.suggestedValue;
                return true;
            }
            
            // For hero-specific, building-specific, or research-specific parameters,
            // we would need more complex parsing and application logic
            
            return false;
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to apply change to {change.parameterName}: {e.Message}");
            return false;
        }
    }
    
    // Calculate overall balance score
    private void CalculateBalanceScore()
    {
        if (balanceAnalyzer == null)
        {
            Debug.LogError("No balance analyzer assigned!");
            return;
        }
        
        // Run balance analysis
        balanceAnalyzer.AnalyzeGameBalance();
        
        // Calculate score based on number and severity of issues
        float heroBalanceScore = CalculateCategoryScore(BalanceIssueType.HeroPower, BalanceIssueType.TroopTypeBalance, BalanceIssueType.SkillEffect);
        float buildingBalanceScore = CalculateCategoryScore(BalanceIssueType.UpgradeCostBenefit);
        float researchBalanceScore = CalculateCategoryScore(BalanceIssueType.ResearchTimeValue);
        float progressionScore = CalculateCategoryScore(BalanceIssueType.ProgressionCurve);
        float resourceScore = CalculateCategoryScore(BalanceIssueType.ResourceGeneration);
        
        // Calculate weighted average
        overallBalanceScore = 
            heroBalanceScore * heroBalanceWeight +
            buildingBalanceScore * buildingBalanceWeight +
            researchBalanceScore * researchBalanceWeight +
            progressionScore * progressionCurveWeight +
            resourceScore * resourceBalanceWeight;
        
        Debug.Log($"Balance scores - Hero: {heroBalanceScore:F2}, Building: {buildingBalanceScore:F2}, Research: {researchBalanceScore:F2}, Progression: {progressionScore:F2}, Resource: {resourceScore:F2}");
        Debug.Log($"Overall balance score: {overallBalanceScore:F2}");
    }
    
    // Calculate score for a specific category of balance issues
    private float CalculateCategoryScore(params BalanceIssueType[] issueTypes)
    {
        if (balanceAnalyzer == null || balanceAnalyzer.detectedIssues == null)
            return 1.0f; // Perfect score if no issues
        
        // Get issues of the specified types
        var issues = balanceAnalyzer.detectedIssues.Where(i => issueTypes.Contains(i.issueType)).ToList();
        
        if (issues.Count == 0)
            return 1.0f; // Perfect score if no issues of this type
        
        // Calculate score based on number and severity of issues
        float totalSeverity = issues.Sum(i => i.severity);
        float maxPossibleSeverity = issues.Count * 5.0f; // Assuming max severity is 5
        
        // Score is inversely proportional to total severity
        float score = 1.0f - (totalSeverity / maxPossibleSeverity);
        
        // Ensure score is between 0 and 1
        return Mathf.Clamp01(score);
    }
}

[Serializable]
public class ParameterChange
{
    public string parameterName;
    public float originalValue;
    public float suggestedValue;
    public string changeDescription;
    public bool isPercentage;
    
    public float ChangePercent => originalValue != 0 ? (suggestedValue - originalValue) / originalValue : 0;
}
