# Building System Menu Implementation

This document explains how to use the new building menu system that attaches menus directly to building prefabs as child objects.

## Overview

The new system replaces the old approach of looking for menus by name in a central location. Instead, each building prefab has:
1. A child GameObject named "BuildingMenu" that contains the UI elements
2. A `BuildingMenu` component that manages the menu functionality

## Quick Start

1. Create a menu GameObject as a child of each building prefab
2. Name the child GameObject "BuildingMenu"
3. Add buttons to the menu (InfoButton, UpgradeButton, MoveButton, FunctionButton)
4. Add the BuildingMenu component to the building
5. Configure the BuildingMenu component in the Inspector

Alternatively, use the Building Menu Converter tool (Tools > Building System > Building Menu Converter) to automatically convert existing buildings.

## Components

### 1. BuildingMenu

This component should be attached to each building prefab. It handles:
- Managing the child menu GameObject
- Setting up button functionality
- Providing building information to the BuildingInfoUI

The BuildingMenu component expects:
- A child GameObject named "BuildingMenu" (or assigned to the menuObject field)
- A "Buttons" container within the menu GameObject
- Button GameObjects with specific names (InfoButton, UpgradeButton, etc.)

### 2. BuildingInfoUI

This component displays detailed information about a building when the Info button is clicked:
- Building image and name
- Current level
- Description
- List of capabilities

## Setup Instructions

### 1. Create the Building Menu GameObject

1. Create a new GameObject as a child of your building prefab
2. Name it "BuildingMenu"
3. Position it above the building (adjust the Y position)
4. Add a Canvas component to the menu GameObject
   - Set Render Mode to "World Space"
   - Adjust the scale to fit your game's scale
5. Add a "Buttons" container GameObject as a child of the menu
6. Add the following buttons as children of the "Buttons" container:
   - InfoButton
   - UpgradeButton
   - MoveButton
   - FunctionButton (optional)
7. Each button should have:
   - A Button component
   - A Text or TextMeshPro component for its label

### 2. Create the Capability Prefab

1. Create a new UI prefab for displaying capabilities (use the provided CapabilityPrefab.prefab.txt as a template)
2. Make sure it has a TextMeshPro component named "CapabilityText"

### 3. Set Up Building Prefabs

For each building prefab:

1. Add the `BuildingMenu` component
2. The component should automatically find the "BuildingMenu" child GameObject
3. If it doesn't, assign it manually to the "Menu Object" field
4. Fill in the building information:
   - Building Name
   - Building Image
   - Building Description
5. Configure which buttons should be available:
   - Has Info Button
   - Has Upgrade Button
   - Has Move Button
   - Has Function Button
6. If using the function button, set:
   - Function Button Text
   - Function Name (e.g., "training", "research")

### 4. Update BuildingSelection

The BuildingSelection component has been updated to work with the new BuildingMenu system. Make sure all your building prefabs have both components.

## Usage

When a building is selected:
1. The BuildingSelection component calls the BuildingMenu's ShowMenu method
2. The child menu GameObject is activated
3. Clicking buttons performs the corresponding actions:
   - Info: Shows the BuildingInfoUI with building details
   - Upgrade: Opens the upgrade UI
   - Move: Enables dragging the building
   - Function: Performs the building-specific function (e.g., opens Training UI)

When the building is deselected:
1. The BuildingSelection component calls the BuildingMenu's HideMenu method
2. The child menu GameObject is deactivated

## Customization

You can customize the menu for each building type:
- Different buttons can be enabled/disabled
- Function buttons can be configured for different purposes
- Building information can be unique to each building type

## Troubleshooting

- If the menu doesn't appear, check if the Canvas exists in the scene
- If buttons don't work, ensure they have the correct names in the prefab
- If capabilities don't show, verify the BuildingCapabilities component is attached to the building

## Building Menu Converter Tool

The Building Menu Converter tool helps you quickly convert existing buildings to use the new system:

1. Open the tool from Tools > Building System > Building Menu Converter
2. Assign a menu prefab to use as a template for creating child menus
3. Choose to convert all buildings or a specific building
4. Click "Convert Buildings"

The tool will:
- Add the BuildingMenu component to each building
- Create a child GameObject named "BuildingMenu" if it doesn't exist
- Position the menu above the building
- Configure buttons based on the building type
- Set default building information

After conversion, you can customize each building's menu settings in the Inspector.

## Transitioning from BuildingMenuManager

The new BuildingMenu system replaces the BuildingMenuManager. To help with the transition:

1. Open the Building Menu Manager Transition tool (Tools > Building System > Building Menu Manager Transition)
2. The tool will:
   - Check for BuildingMenuManager instances in the scene
   - Find buildings without the BuildingMenu component
   - Check building prefabs for the BuildingMenu component
3. Use the tool to:
   - Remove BuildingMenuManager instances (after all buildings are converted)
   - Convert buildings that don't have the BuildingMenu component
   - Add the BuildingMenu component to building prefabs

**Note:** The BuildingMenuManager class is no longer needed with the new system. Each building now manages its own menu through the BuildingMenu component.
