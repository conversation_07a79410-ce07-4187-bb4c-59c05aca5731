# Training System

This system allows players to train troops of three different types (Infantry, Rider, Ranged) at various levels, upgrade them, and use speed-up items when necessary.

## Setup Instructions

1. Create the necessary folders:
   - Assets/Training System/Troops/Infantry
   - Assets/Training System/Troops/Rider
   - Assets/Training System/Troops/Ranged
   - Assets/Training System/Prefabs
   - Assets/Training System/Editor

2. Create sample troop data:
   - Open the Troop Data Creator window (Tools > Training System > Create Troop Data)
   - Set the default thumbnail and image sprites
   - Click "Create All Troops" to generate sample troop data

3. Create the prefabs:
   - Open the Training System Prefab Creator window (Tools > Training System > Create Training System Prefabs)
   - Click "Create Training Manager Prefab" to create the TrainingManager prefab
   - Click "Create Training UI Prefab" to create the TrainingUI prefab

4. Set up the Training Manager:
   - Drag the TrainingManager prefab into your scene
   - Assign the troop scriptable objects to the appropriate lists (Infantry, Rider, Ranged)
   - Set the initial building levels for each barracks type

5. Set up the Training UI:
   - Drag the TrainingUI prefab into your scene
   - Assign the necessary UI elements in the inspector
   - Set up the thumbnail prefab with the TroopThumbnail component

6. Integration with other systems:
   - Ensure the GameManager is in the scene for resource management
   - Ensure the InventorySystem is in the scene for speed-up items

## Usage

- Call `TrainingManager.Instance.StartTraining(troopSO, count)` to start training troops
- Call `TrainingManager.Instance.UpgradeTroop(currentTroop, targetTroop, count)` to upgrade troops
- Call `TrainingManager.Instance.SpeedUpTraining(queueItem, minutes)` to speed up training

## UI Components

The Training UI consists of the following panels:
- Scroll View Panel: Displays thumbnails of all troop levels
- Troop Detail Panel: Shows troop information and training options
- Countdown UI: Shows remaining time for training/upgrading
- Speed Up Confirmation UI: Allows using speed-up items
- Completion UI: Shows gained Battle Power after training
- Troop Stats UI: Displays combat stats for the selected troop
- Resource Replenishment UI: Offers option to use resources from inventory

## Customization

- Edit the TroopSO scriptable objects to customize troop stats and requirements
- Modify the TrainingUI prefab to change the appearance of the UI
- Adjust the formulas in the TroopSO class to change how stats are calculated

## Dependencies

- GameManager: For resource management and battle power
- InventorySystem: For speed-up items and resource replenishment
- Building levels: For troop unlocking based on building level
