using UnityEngine;
using UnityEngine.UI;
using System.Collections;

public class UIElementAnimator : MonoBehaviour
{
    public enum AnimationType
    {
        ShrinkFadeIn,
        GrowFadeOut,
        SlideFadeIn,
        SlideFadeOut,
        FadeIn,
        FadeOut
    }

    public delegate void AnimationCompleteHandler();
    public event AnimationCompleteHandler OnAnimationComplete;

    [SerializeField] private AnimationType animationInType;
    [SerializeField] private float inDelay = 0f;
    [SerializeField] private AnimationType animationOutType;
    [SerializeField] private float outDelay = 0f;
    [SerializeField] private float animationDuration = 0.5f;
    [SerializeField] private Vector3 slideOffset = new Vector3(0, -100, 0); // Offset for slide animations
    


    private CanvasGroup canvasGroup;
    private RectTransform rectTransform;
    private Vector3 originalScale;
    private Vector3 originalPosition;
    private bool isAnimatingOut = false;
    private bool isDeactivating = false;

    private void Awake()
    {
        // Get or add CanvasGroup component
        canvasGroup = GetComponent<CanvasGroup>();
        if (canvasGroup == null)
        {
            canvasGroup = gameObject.AddComponent<CanvasGroup>();
        }

        // Get RectTransform (every UI element should have this)
        rectTransform = GetComponent<RectTransform>();
        if (rectTransform != null)
        {
            originalScale = rectTransform.localScale;
            originalPosition = rectTransform.localPosition;
        }
        else
        {
            Debug.LogError($"UIElementAnimator on {gameObject.name} requires a RectTransform component!");
        }
    }

    private void OnEnable()
    {
        if (!isAnimatingOut)
        {
            StartCoroutine(AnimateIn());
        }
    }

    private void OnDisable()
    {
        if (!isAnimatingOut && !isDeactivating && rectTransform != null)
        {
            ResetState();
        }
    }

    private void ResetState()
    {
        if (this == null || !gameObject.activeInHierarchy) return;

        StopAllCoroutines();
        
        if (rectTransform != null)
        {
            rectTransform.localScale = originalScale;
            rectTransform.localPosition = originalPosition;
        }
        
        if (canvasGroup != null)
        {
            canvasGroup.alpha = 1f;
        }
        
        isAnimatingOut = false;
        isDeactivating = false;
    }

    public void HandleDeactivation()
    {
        isDeactivating = true;
        StartCoroutine(CloseAfterAnimation());
    }

    private IEnumerator CloseAfterAnimation()
    {
        yield return StartCoroutine(AnimateOut());
        ResetState();
        gameObject.SetActive(false);
    }

    public void Close()
    {
        if (!isAnimatingOut)
        {
            StartCoroutine(CloseAfterAnimation());
        }
    }

    private IEnumerator AnimateIn()
    {
        yield return StartCoroutine(AnimateInWithDelay());
        
    }

    private IEnumerator AnimateOut()
    {
        yield return StartCoroutine(AnimateOutWithDelay());
       
    }

    private IEnumerator AnimateInWithDelay()
    {
        // Set initial states based on animation type
        switch (animationInType)
        {
            case AnimationType.ShrinkFadeIn:
                rectTransform.localScale = Vector3.zero;
                canvasGroup.alpha = 0f;
                break;
            case AnimationType.SlideFadeIn:
                rectTransform.localPosition = originalPosition + slideOffset;
                canvasGroup.alpha = 0f;
                break;
            case AnimationType.FadeIn:
                canvasGroup.alpha = 0f;
                break;
        }

        yield return new WaitForSeconds(inDelay);
        
        float elapsedTime = 0f;

        // Animation logic remains the same
        switch (animationInType)
        {
            case AnimationType.ShrinkFadeIn:
                while (elapsedTime < animationDuration)
                {
                    rectTransform.localScale = Vector3.Lerp(Vector3.zero, originalScale, elapsedTime / animationDuration);
                    canvasGroup.alpha = Mathf.Lerp(0f, 1f, elapsedTime / animationDuration);
                    elapsedTime += Time.deltaTime;
                    yield return null;
                }
                break;

            case AnimationType.SlideFadeIn:
                while (elapsedTime < animationDuration)
                {
                    rectTransform.localPosition = Vector3.Lerp(originalPosition + slideOffset, originalPosition, elapsedTime / animationDuration);
                    canvasGroup.alpha = Mathf.Lerp(0f, 1f, elapsedTime / animationDuration);
                    elapsedTime += Time.deltaTime;
                    yield return null;
                }
                break;

            case AnimationType.FadeIn:
                while (elapsedTime < animationDuration)
                {
                    canvasGroup.alpha = Mathf.Lerp(0f, 1f, elapsedTime / animationDuration);
                    elapsedTime += Time.deltaTime;
                    yield return null;
                }
                break;
        }

        rectTransform.localScale = originalScale;
        rectTransform.localPosition = originalPosition;
        canvasGroup.alpha = 1f;
    }
    private IEnumerator AnimateOutWithDelay()
    {
        yield return new WaitForSeconds(outDelay);
        
        isAnimatingOut = true;
        float elapsedTime = 0f;

        switch (animationOutType)
        {
            case AnimationType.GrowFadeOut:
                rectTransform.localScale = originalScale;
                canvasGroup.alpha = 1f;
                while (elapsedTime < animationDuration)
                {
                    rectTransform.localScale = Vector3.Lerp(originalScale, Vector3.zero, elapsedTime / animationDuration);
                    canvasGroup.alpha = Mathf.Lerp(1f, 0f, elapsedTime / animationDuration);
                    elapsedTime += Time.deltaTime;
                    yield return null;
                }
                break;

            case AnimationType.SlideFadeOut:
                rectTransform.localPosition = originalPosition;
                canvasGroup.alpha = 1f;
                while (elapsedTime < animationDuration)
                {
                    rectTransform.localPosition = Vector3.Lerp(originalPosition, originalPosition + slideOffset, elapsedTime / animationDuration);
                    canvasGroup.alpha = Mathf.Lerp(1f, 0f, elapsedTime / animationDuration);
                    elapsedTime += Time.deltaTime;
                    yield return null;
                }
                break;

            case AnimationType.FadeOut:
                canvasGroup.alpha = 1f;
                while (elapsedTime < animationDuration)
                {
                    canvasGroup.alpha = Mathf.Lerp(1f, 0f, elapsedTime / animationDuration);
                    elapsedTime += Time.deltaTime;
                    yield return null;
                }
                break;
        }

        rectTransform.localScale = originalScale;
        rectTransform.localPosition = originalPosition;
        canvasGroup.alpha = 0f;
        isAnimatingOut = false;
        
        // Notify listeners that animation is complete
        OnAnimationComplete?.Invoke();
    }
}
