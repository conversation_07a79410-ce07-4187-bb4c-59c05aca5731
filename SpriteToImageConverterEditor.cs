using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(SpriteToImageConverter))]
public class SpriteToImageConverterEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();
        
        SpriteToImageConverter converter = (SpriteToImageConverter)target;
        
        if(GUILayout.Button("Convert Sprites to Images"))
        {
            converter.ConvertSpritesToImages();
        }
    }
}