using System.Collections.Generic;
using System.Linq; // Add this for LINQ methods
using UnityEngine;

public class InventorySystem : MonoBehaviour
{
    public static InventorySystem Instance;
    private List<InventoryItem> items;

    [System.Serializable]
    private class InventoryData
    {
        public List<ItemData> items = new List<ItemData>();
    }

    [System.Serializable]
    private class ItemData
    {
        public string itemID; // Using InventoryItemSO.ID
        public int quantity;

        public ItemData(string id, int qty)
        {
            itemID = id;
            quantity = qty;
        }
    }

    // Save inventory state
    public void SaveInventory()
    {
        InventoryData data = new InventoryData();
        
        foreach (var item in items)
        {
            if (item.quantity > 0) // Only save items with quantity > 0
            {
                data.items.Add(new ItemData(item.itemSO.ID, item.quantity));
            }
        }

        string json = JsonUtility.ToJson(data);
        PlayerPrefs.SetString("InventoryData", json);
        PlayerPrefs.Save();
        
        // Debug.Log($"Saved inventory with {data.items.Count} items");
    }

    // Load inventory state
    public void LoadInventory()
    {
        if (PlayerPrefs.HasKey("InventoryData"))
        {
            string json = PlayerPrefs.GetString("InventoryData");
            InventoryData data = JsonUtility.FromJson<InventoryData>(json);
            
            // Clear current inventory
            items.Clear();
            
            // Load all InventoryItemSOs
            var allItemSOs = Resources.LoadAll<InventoryItemSO>("InventoryItems");
            
            // Check for duplicate IDs
            var duplicateIDs = allItemSOs.GroupBy(so => so.ID)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key);

            foreach (var dupID in duplicateIDs)
            {
                Debug.LogError($"Duplicate Item ID found: {dupID}. Items with this ID:");
                foreach (var item in allItemSOs.Where(so => so.ID == dupID))
                {
                    Debug.LogError($"- {item.name} (Asset name) with ID: {item.ID}");
                }
            }

            // Create dictionary, skipping duplicates
            Dictionary<string, InventoryItemSO> itemSOMap = new Dictionary<string, InventoryItemSO>();
            foreach (var itemSO in allItemSOs)
            {
                if (!itemSOMap.ContainsKey(itemSO.ID))
                {
                    itemSOMap[itemSO.ID] = itemSO;
                }
                else
                {
                    Debug.LogWarning($"Skipping duplicate item ID: {itemSO.ID} from asset: {itemSO.name}");
                }
            }

            // Restore saved quantities
            foreach (var itemData in data.items)
            {
                if (itemSOMap.TryGetValue(itemData.itemID, out InventoryItemSO itemSO))
                {
                    AddItem(itemSO, itemData.quantity);
                }
                else
                {
                    Debug.LogWarning($"Could not find InventoryItemSO with ID: {itemData.itemID}");
                }
            }
            
            Debug.Log($"Loaded inventory with {data.items.Count} items");
        }
        
        UpdateUI();
    }

    // Add these to Awake method after InitializeInventory
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeInventory();
            LoadInventory(); // Load saved inventory data
            
            // Only add test items if in development/testing
            #if UNITY_EDITOR
            AddTestItems();
            #endif
        }
        else
        {
            Destroy(gameObject);
        }
    }

    // Optional: Auto-save when application pauses/quits
    private void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            SaveInventory();
        }
    }

    private void OnApplicationQuit()
    {
        SaveInventory();
    }

    // Public method to get item quantity (useful for other systems)
    public int GetItemQuantity(string itemID)
    {
        var item = items.FirstOrDefault(i => i.itemSO.ID == itemID);
        return item?.quantity ?? 0;
    }

    // Public method to check if has enough of an item
    public bool HasEnoughItems(string itemID, int requiredAmount)
    {
        return GetItemQuantity(itemID) >= requiredAmount;
    }
    
    private void AddTestItems()
    {
        // Add test quantities for different items
        var allItems = Resources.LoadAll<InventoryItemSO>("InventoryItems");
        foreach (var item in allItems)
        {
            AddItem(item, Random.Range(1, 100)); // Adds random quantity between 1-99
        }
    }

    private void InitializeInventory()
    {
        items = new List<InventoryItem>();
        InventoryItemSO[] itemSOs = Resources.LoadAll<InventoryItemSO>("InventoryItems"); // Load all items from Resources/InventoryItems
        // Debug.Log($"Loaded {itemSOs.Length} items from Resources");

        foreach (var itemSO in itemSOs)
        {
            // Debug.Log($"Adding item: {itemSO.Name}, Category: {itemSO.Category}");
            items.Add(new InventoryItem(itemSO));
        }

        UpdateUI();
    }

    public List<InventoryItem> GetItemsByCategory(ItemCategory category)
    {
        // Debug.Log($"Total items in inventory: {items.Count}");
        var filteredItems = items.Where(item => item.itemSO.Category == category).ToList();
        // Debug.Log($"Found {filteredItems.Count} items in category {category}");
        return filteredItems;
    }

    public List<InventoryItem> GetItemsByTag(string tag)
    {
        return items.Where(item => item.itemSO.Tag == tag).ToList();
    }

    public void AddItem(InventoryItemSO itemSO, int quantity)
    {
        var existingItem = items.FirstOrDefault(item => item.itemSO == itemSO);
        if (existingItem != null)
        {
            existingItem.quantity += quantity;
        }
        else
        {
            InventoryItem newItem = new InventoryItem(itemSO, quantity);
            items.Add(newItem);
        }
        // Debug.Log($"Added {quantity} of {itemSO.Name}, Category: {itemSO.Category}");
        UpdateUI();
    }


    public void RemoveItem(InventoryItem item)
    {
        items.Remove(item);
        UpdateUI();
    }

    private void UpdateUI()
    {
        if (InventoryUI.Instance != null)
        {
            InventoryUI.Instance.DisplayItems(ItemCategory.Resources); // Update this to the appropriate category
        }
    }

    public void RemoveItemByTag(string tag, int amount)
    {
        var items = GetItemsByTag(tag);
        foreach (var item in items)
        {
            if (item.quantity >= amount)
            {
                item.quantity -= amount;
                UpdateUI();
                return;
            }
        }
    }

    public int GetItemQuantityByTag(string tag)
    {
        return GetItemsByTag(tag).Sum(item => item.quantity);
    }

}
